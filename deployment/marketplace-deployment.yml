# Marketplace Deployment Configuration
# Production-ready deployment setup for Claude Code Marketplace

version: '1.0'
deployment:
  name: 'claudia-marketplace'
  environment: 'production'
  
# Database Migration Configuration
database:
  migration:
    enabled: true
    backup_before_migration: true
    rollback_on_failure: true
    timeout_seconds: 300
    scripts:
      - path: '../marketplace-schema.sql'
        description: 'Initial marketplace schema creation'
        rollback_script: '../migrations/rollback_marketplace.sql'
    
  performance:
    connection_pool_size: 20
    query_timeout: 30000
    cache_size: '256MB'
    journal_mode: 'WAL'
    synchronous: 'NORMAL'

# Backend Configuration  
backend:
  rust:
    release_mode: true
    optimization_level: 3
    features:
      - 'marketplace'
      - 'github-integration'
      - 'analytics'
    
  api:
    rate_limiting:
      requests_per_minute: 1000
      burst_capacity: 100
      enable_ip_whitelist: true
    
    caching:
      redis_url: "${REDIS_URL}"
      ttl_seconds: 300
      max_memory: '512MB'
    
    security:
      cors_origins:
        - 'https://claudia-app.com'
        - 'https://*.claudia-app.com'
      max_request_size: '10MB'
      enable_helmet: true

# Frontend Configuration
frontend:
  build:
    mode: 'production'
    source_maps: false
    minimize: true
    chunk_splitting: true
  
  performance:
    lazy_loading: true
    image_optimization: true
    code_splitting: true
    service_worker: true
  
  monitoring:
    sentry_dsn: "${SENTRY_DSN}"
    google_analytics: "${GA_TRACKING_ID}"
    performance_monitoring: true

# Feature Flags
feature_flags:
  marketplace_enabled: true
  github_sync_enabled: true
  agent_ratings: true
  agent_collections: false  # Phase 2
  analytics_enabled: true
  offline_mode: true

# Monitoring Configuration
monitoring:
  health_checks:
    - name: 'database_connection'
      endpoint: '/health/db'
      interval_seconds: 30
      timeout_seconds: 5
    
    - name: 'marketplace_api'
      endpoint: '/api/marketplace/health'
      interval_seconds: 60
      timeout_seconds: 10
    
    - name: 'github_api'
      endpoint: '/health/github'
      interval_seconds: 300
      timeout_seconds: 15

  metrics:
    - name: 'api_response_time'
      type: 'histogram'
      labels: ['endpoint', 'method', 'status']
    
    - name: 'agent_installations'
      type: 'counter'
      labels: ['source', 'success']
    
    - name: 'search_queries'
      type: 'counter'
      labels: ['category', 'results_count']
    
    - name: 'cache_hit_rate'
      type: 'gauge'
      labels: ['cache_type']

  alerts:
    - name: 'high_error_rate'
      condition: 'error_rate > 0.05'
      duration: '5m'
      severity: 'critical'
    
    - name: 'slow_api_response'
      condition: 'p95_response_time > 1000ms'
      duration: '10m'
      severity: 'warning'
    
    - name: 'failed_installations'
      condition: 'failed_installation_rate > 0.10'
      duration: '5m'
      severity: 'warning'

# Security Configuration
security:
  authentication:
    session_timeout: 3600
    max_failed_attempts: 5
    lockout_duration: 900
  
  data_protection:
    encrypt_sensitive_fields: true
    hash_user_identifiers: true
    anonymize_analytics: true
  
  api_security:
    enable_rate_limiting: true
    validate_input_schemas: true
    sanitize_user_content: true
    verify_file_integrity: true

# Backup Configuration
backup:
  database:
    schedule: '0 2 * * *'  # Daily at 2 AM
    retention_days: 30
    compression: true
    encryption: true
  
  agent_files:
    schedule: '0 3 * * 0'  # Weekly on Sunday at 3 AM
    retention_weeks: 8
    incremental: true

# Performance Targets
performance:
  targets:
    api_response_time_p95: '200ms'
    search_response_time: '150ms'
    agent_installation_time: '5s'
    cache_hit_rate: '90%'
    uptime: '99.9%'
  
  optimization:
    enable_compression: true
    minify_responses: true
    optimize_images: true
    enable_cdn: true

# Rollback Configuration
rollback:
  automated:
    error_rate_threshold: 0.10
    response_time_threshold: '2s'
    check_duration: '5m'
  
  manual:
    backup_retention: 7
    rollback_timeout: '10m'
    notification_channels:
      - 'slack'
      - 'email'

# Environment Variables
env_vars:
  required:
    - 'DATABASE_URL'
    - 'GITHUB_TOKEN'
    - 'REDIS_URL'
    - 'SENTRY_DSN'
  
  optional:
    - 'MARKETPLACE_API_URL'
    - 'ANALYTICS_API_KEY'
    - 'CDN_URL'

# Deployment Steps
deployment_steps:
  pre_deployment:
    - name: 'run_tests'
      command: 'npm run test:all'
      timeout: 600
    
    - name: 'security_scan'
      command: 'npm run security:audit'
      timeout: 300
    
    - name: 'backup_database'
      command: 'scripts/backup-db.sh'
      timeout: 1800

  deployment:
    - name: 'migrate_database'
      command: 'cargo run --bin migrate'
      timeout: 600
      rollback_on_failure: true
    
    - name: 'build_backend'
      command: 'cargo build --release --features marketplace'
      timeout: 1200
    
    - name: 'build_frontend'
      command: 'npm run build:prod'
      timeout: 600
    
    - name: 'deploy_assets'
      command: 'scripts/deploy-assets.sh'
      timeout: 300

  post_deployment:
    - name: 'run_smoke_tests'
      command: 'npm run test:smoke'
      timeout: 300
    
    - name: 'warm_cache'
      command: 'scripts/warm-cache.sh'
      timeout: 600
    
    - name: 'verify_health'
      command: 'scripts/health-check.sh'
      timeout: 120

# Success Criteria
success_criteria:
  - api_response_time_p95 < 300ms
  - error_rate < 0.01
  - all_health_checks_passing: true
  - smoke_tests_passing: true
  - cache_warmed: true