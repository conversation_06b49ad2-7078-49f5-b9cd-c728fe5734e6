/**
 * Comprehensive Marketplace Testing Suite
 * Tests all marketplace functionality from API to UI components
 */

import { describe, test, expect, beforeAll, afterAll, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { act } from '@testing-library/react-hooks';
import userEvent from '@testing-library/user-event';
import { marketplaceApi } from '@/lib/api-marketplace';
import { useMarketplaceStore } from '@/stores/marketplaceStore';
import { MarketplaceBrowser } from '@/components/marketplace/MarketplaceBrowser';
import { AgentMarketplaceCard } from '@/components/marketplace/AgentMarketplaceCard';
import { InstallButton } from '@/components/marketplace/InstallButton';
import type { MarketplaceAgent, MarketplaceSearchParams } from '@/types/marketplace';

// Mock data
const mockAgent: MarketplaceAgent = {
  remote_id: 'test-agent-1',
  name: 'Test Agent',
  description: 'A test agent for unit testing',
  version: '1.0.0',
  author: 'Test Author',
  download_url: 'https://example.com/test-agent.json',
  download_count: 100,
  rating_average: 4.5,
  rating_count: 20,
  is_installed: false,
  file_size: 1024,
  enable_file_read: true,
  enable_file_write: false,
  enable_network: true,
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
  last_synced_at: '2024-01-01T00:00:00Z',
  icon: '🤖',
  system_prompt: 'You are a test agent.',
  model: 'sonnet',
  installation_source: null,
  installed_at: null
};

const mockCategories = [
  { id: 1, name: 'Development', slug: 'development', sort_order: 1, is_active: true, created_at: '2024-01-01T00:00:00Z' },
  { id: 2, name: 'Content', slug: 'content', sort_order: 2, is_active: true, created_at: '2024-01-01T00:00:00Z' }
];

// Mock API responses
const mockApiResponse = {
  searchMarketplaceAgents: vi.fn(),
  getFeaturedAgents: vi.fn(),
  getCategories: vi.fn(),
  installAgent: vi.fn(),
  uninstallAgent: vi.fn()
};

vi.mock('@/lib/api-marketplace', () => ({
  marketplaceApi: mockApiResponse
}));

describe('Marketplace API Layer', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Agent Search', () => {
    test('should search agents with basic parameters', async () => {
      const searchParams: MarketplaceSearchParams = {
        query: 'test',
        page: 1,
        limit: 20
      };

      const expectedResult = {
        agents: [mockAgent],
        total_count: 1,
        page: 1,
        limit: 20,
        total_pages: 1,
        filters_applied: searchParams
      };

      mockApiResponse.searchMarketplaceAgents.mockResolvedValue(expectedResult);

      const result = await marketplaceApi.searchMarketplaceAgents(searchParams);

      expect(mockApiResponse.searchMarketplaceAgents).toHaveBeenCalledWith(searchParams);
      expect(result).toEqual(expectedResult);
      expect(result.agents).toHaveLength(1);
      expect(result.agents[0].name).toBe('Test Agent');
    });

    test('should handle search with category filter', async () => {
      const searchParams: MarketplaceSearchParams = {
        category_id: 1,
        sort_by: 'rating',
        sort_order: 'desc'
      };

      mockApiResponse.searchMarketplaceAgents.mockResolvedValue({
        agents: [],
        total_count: 0,
        page: 1,
        limit: 20,
        total_pages: 0,
        filters_applied: searchParams
      });

      await marketplaceApi.searchMarketplaceAgents(searchParams);

      expect(mockApiResponse.searchMarketplaceAgents).toHaveBeenCalledWith(searchParams);
    });

    test('should handle API errors gracefully', async () => {
      mockApiResponse.searchMarketplaceAgents.mockRejectedValue(new Error('Network error'));

      await expect(marketplaceApi.searchMarketplaceAgents({})).rejects.toThrow('Network error');
    });
  });

  describe('Agent Installation', () => {
    test('should install agent successfully', async () => {
      const installRequest = {
        remote_id: 'test-agent-1',
        source: 'github' as const,
        install_dependencies: true
      };

      const expectedResult = {
        success: true,
        message: 'Agent installed successfully',
        agent: { ...mockAgent, id: 1, is_installed: true },
        errors: null,
        warnings: null,
        installed_dependencies: [],
        skipped_dependencies: []
      };

      mockApiResponse.installAgent.mockResolvedValue(expectedResult);

      const result = await marketplaceApi.installAgent(installRequest);

      expect(result.success).toBe(true);
      expect(result.agent?.is_installed).toBe(true);
    });

    test('should handle installation failures', async () => {
      const installRequest = {
        remote_id: 'invalid-agent',
        source: 'github' as const
      };

      const expectedResult = {
        success: false,
        message: 'Agent not found',
        agent: null,
        errors: ['AGENT_NOT_FOUND'],
        warnings: null,
        installed_dependencies: null,
        skipped_dependencies: null
      };

      mockApiResponse.installAgent.mockResolvedValue(expectedResult);

      const result = await marketplaceApi.installAgent(installRequest);

      expect(result.success).toBe(false);
      expect(result.errors).toContain('AGENT_NOT_FOUND');
    });
  });

  describe('Category Management', () => {
    test('should fetch categories successfully', async () => {
      mockApiResponse.getCategories.mockResolvedValue(mockCategories);

      const result = await marketplaceApi.getCategories();

      expect(result).toEqual(mockCategories);
      expect(result).toHaveLength(2);
    });
  });
});

describe('Marketplace Store', () => {
  beforeEach(() => {
    useMarketplaceStore.setState({
      agents: [],
      categories: [],
      isLoading: false,
      error: null,
      searchQuery: '',
      selectedCategory: null,
      currentPage: 1
    });
  });

  test('should update search query', () => {
    const { setSearchQuery } = useMarketplaceStore.getState();
    
    act(() => {
      setSearchQuery('test query');
    });

    expect(useMarketplaceStore.getState().searchQuery).toBe('test query');
  });

  test('should filter agents by search query', () => {
    const testAgents = [
      { ...mockAgent, name: 'Development Helper' },
      { ...mockAgent, remote_id: 'agent-2', name: 'Content Writer' },
      { ...mockAgent, remote_id: 'agent-3', name: 'Code Reviewer' }
    ];

    useMarketplaceStore.setState({
      agents: testAgents,
      searchQuery: 'dev'
    });

    const { getFilteredAgents } = useMarketplaceStore.getState();
    const filteredAgents = getFilteredAgents();

    expect(filteredAgents).toHaveLength(1);
    expect(filteredAgents[0].name).toBe('Development Helper');
  });

  test('should handle category filtering', () => {
    const testAgents = [
      { ...mockAgent, category_id: 1 },
      { ...mockAgent, remote_id: 'agent-2', category_id: 2 }
    ];

    useMarketplaceStore.setState({
      agents: testAgents,
      selectedCategory: '1'
    });

    const { getFilteredAgents } = useMarketplaceStore.getState();
    const filteredAgents = getFilteredAgents();

    expect(filteredAgents).toHaveLength(1);
    expect(filteredAgents[0].category_id).toBe(1);
  });
});

describe('MarketplaceBrowser Component', () => {
  const defaultProps = {
    onBack: vi.fn()
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mockApiResponse.searchMarketplaceAgents.mockResolvedValue({
      agents: [mockAgent],
      total_count: 1,
      page: 1,
      limit: 20,
      total_pages: 1,
      filters_applied: {}
    });
    mockApiResponse.getCategories.mockResolvedValue(mockCategories);
    mockApiResponse.getFeaturedAgents.mockResolvedValue([mockAgent]);
  });

  test('should render marketplace browser', async () => {
    render(<MarketplaceBrowser {...defaultProps} />);

    expect(screen.getByText('Agent Marketplace')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Search agents...')).toBeInTheDocument();
  });

  test('should handle search input', async () => {
    const user = userEvent.setup();
    render(<MarketplaceBrowser {...defaultProps} />);

    const searchInput = screen.getByPlaceholderText('Search agents...');
    await user.type(searchInput, 'test query');

    await waitFor(() => {
      expect(searchInput).toHaveValue('test query');
    });
  });

  test('should display agents in grid view', async () => {
    render(<MarketplaceBrowser {...defaultProps} />);

    await waitFor(() => {
      expect(screen.getByText('Test Agent')).toBeInTheDocument();
    });
  });

  test('should handle view mode toggle', async () => {
    const user = userEvent.setup();
    render(<MarketplaceBrowser {...defaultProps} />);

    const listViewButton = screen.getByLabelText('List view');
    await user.click(listViewButton);

    // Verify list view is active
    expect(listViewButton).toHaveClass('bg-accent');
  });
});

describe('AgentMarketplaceCard Component', () => {
  const defaultProps = {
    agent: mockAgent,
    viewMode: 'grid' as const,
    index: 0
  };

  test('should render agent card with basic information', () => {
    render(<AgentMarketplaceCard {...defaultProps} />);

    expect(screen.getByText('Test Agent')).toBeInTheDocument();
    expect(screen.getByText('A test agent for unit testing')).toBeInTheDocument();
    expect(screen.getByText('Test Author')).toBeInTheDocument();
    expect(screen.getByText('v1.0.0')).toBeInTheDocument();
  });

  test('should display rating information', () => {
    render(<AgentMarketplaceCard {...defaultProps} />);

    expect(screen.getByText('4.5')).toBeInTheDocument();
    expect(screen.getByText('(20 reviews)')).toBeInTheDocument();
  });

  test('should show download count', () => {
    render(<AgentMarketplaceCard {...defaultProps} />);

    expect(screen.getByText('100 downloads')).toBeInTheDocument();
  });

  test('should handle list view mode', () => {
    render(<AgentMarketplaceCard {...defaultProps} viewMode="list" />);

    expect(screen.getByText('Test Agent')).toBeInTheDocument();
    // Verify different layout for list view
    expect(screen.getByRole('article')).toHaveClass('flex-row');
  });
});

describe('InstallButton Component', () => {
  const defaultProps = {
    agent: mockAgent
  };

  beforeEach(() => {
    mockApiResponse.installAgent.mockResolvedValue({
      success: true,
      message: 'Installation successful',
      agent: { ...mockAgent, is_installed: true }
    });
  });

  test('should render install button for uninstalled agent', () => {
    render(<InstallButton {...defaultProps} />);

    expect(screen.getByText('Install')).toBeInTheDocument();
  });

  test('should show installed state', () => {
    const installedAgent = { ...mockAgent, is_installed: true };
    render(<InstallButton {...defaultProps} agent={installedAgent} />);

    expect(screen.getByText('Installed')).toBeInTheDocument();
  });

  test('should handle install click', async () => {
    const user = userEvent.setup();
    render(<InstallButton {...defaultProps} />);

    const installButton = screen.getByText('Install');
    await user.click(installButton);

    expect(screen.getByText('Installing...')).toBeInTheDocument();

    await waitFor(() => {
      expect(mockApiResponse.installAgent).toHaveBeenCalledWith({
        remote_id: 'test-agent-1',
        source: 'marketplace',
        install_dependencies: true
      });
    });
  });

  test('should handle installation error', async () => {
    mockApiResponse.installAgent.mockResolvedValue({
      success: false,
      message: 'Installation failed',
      errors: ['DOWNLOAD_FAILED']
    });

    const user = userEvent.setup();
    render(<InstallButton {...defaultProps} />);

    const installButton = screen.getByText('Install');
    await user.click(installButton);

    await waitFor(() => {
      expect(screen.getByText('Install Failed')).toBeInTheDocument();
    });
  });
});

describe('Integration Tests', () => {
  test('should complete full agent discovery and installation flow', async () => {
    const user = userEvent.setup();
    
    // Setup API mocks
    mockApiResponse.searchMarketplaceAgents.mockResolvedValue({
      agents: [mockAgent],
      total_count: 1,
      page: 1,
      limit: 20,
      total_pages: 1,
      filters_applied: {}
    });
    
    mockApiResponse.installAgent.mockResolvedValue({
      success: true,
      message: 'Installation successful',
      agent: { ...mockAgent, is_installed: true }
    });

    render(<MarketplaceBrowser onBack={vi.fn()} />);

    // 1. Wait for initial load
    await waitFor(() => {
      expect(screen.getByText('Test Agent')).toBeInTheDocument();
    });

    // 2. Click on agent card to view details
    const agentCard = screen.getByText('Test Agent');
    await user.click(agentCard);

    // 3. Verify details modal opens
    await waitFor(() => {
      expect(screen.getByRole('dialog')).toBeInTheDocument();
    });

    // 4. Install the agent
    const installButton = screen.getByText('Install');
    await user.click(installButton);

    // 5. Verify installation success
    await waitFor(() => {
      expect(mockApiResponse.installAgent).toHaveBeenCalled();
    });
  });

  test('should handle error recovery gracefully', async () => {
    // Simulate API failure
    mockApiResponse.searchMarketplaceAgents.mockRejectedValue(new Error('Network error'));

    render(<MarketplaceBrowser onBack={vi.fn()} />);

    await waitFor(() => {
      expect(screen.getByText('Failed to load marketplace')).toBeInTheDocument();
    });

    // Verify retry functionality
    const retryButton = screen.getByText('Retry');
    expect(retryButton).toBeInTheDocument();
  });
});

describe('Performance Tests', () => {
  test('should handle large agent lists efficiently', async () => {
    const largeAgentList = Array.from({ length: 1000 }, (_, i) => ({
      ...mockAgent,
      remote_id: `agent-${i}`,
      name: `Agent ${i}`
    }));

    const startTime = performance.now();

    mockApiResponse.searchMarketplaceAgents.mockResolvedValue({
      agents: largeAgentList,
      total_count: 1000,
      page: 1,
      limit: 20,
      total_pages: 50,
      filters_applied: {}
    });

    render(<MarketplaceBrowser onBack={vi.fn()} />);

    await waitFor(() => {
      expect(screen.getByText('Agent 0')).toBeInTheDocument();
    });

    const endTime = performance.now();
    const renderTime = endTime - startTime;

    // Should render within 500ms
    expect(renderTime).toBeLessThan(500);
  });

  test('should debounce search input', async () => {
    const user = userEvent.setup();
    render(<MarketplaceBrowser onBack={vi.fn()} />);

    const searchInput = screen.getByPlaceholderText('Search agents...');

    // Type quickly
    await user.type(searchInput, 'test');

    // Should not call API immediately
    expect(mockApiResponse.searchMarketplaceAgents).not.toHaveBeenCalledWith(
      expect.objectContaining({ query: 'test' })
    );

    // Wait for debounce period
    await waitFor(() => {
      expect(mockApiResponse.searchMarketplaceAgents).toHaveBeenCalledWith(
        expect.objectContaining({ query: 'test' })
      );
    }, { timeout: 1000 });
  });
});

describe('Accessibility Tests', () => {
  test('should support keyboard navigation', async () => {
    render(<MarketplaceBrowser onBack={vi.fn()} />);

    await waitFor(() => {
      expect(screen.getByText('Test Agent')).toBeInTheDocument();
    });

    // Test tab navigation
    const searchInput = screen.getByPlaceholderText('Search agents...');
    searchInput.focus();
    
    expect(document.activeElement).toBe(searchInput);

    // Tab to next element
    fireEvent.keyDown(searchInput, { key: 'Tab' });
    
    // Should focus on next interactive element
    expect(document.activeElement).not.toBe(searchInput);
  });

  test('should have proper ARIA labels', () => {
    render(<MarketplaceBrowser onBack={vi.fn()} />);

    expect(screen.getByLabelText('Search agents')).toBeInTheDocument();
    expect(screen.getByLabelText('Grid view')).toBeInTheDocument();
    expect(screen.getByLabelText('List view')).toBeInTheDocument();
  });
});