import { useState, useEffect, useCallback } from 'react';

// Learning content types
export interface LearningResource {
  id: string;
  title: string;
  description: string;
  type: 'course' | 'tutorial' | 'paper' | 'book' | 'video' | 'documentation';
  url: string;
  provider: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  duration?: string;
  rating?: number;
  price?: 'free' | 'paid';
  tags: string[];
  progress?: number; // 0-100
  isBookmarked?: boolean;
  thumbnailUrl?: string;
  authorName?: string;
  publishedAt?: Date;
  enrollmentCount?: number;
  certificateAvailable?: boolean;
  languages?: string[];
  prerequisites?: string[];
}

export interface LearningPath {
  id: string;
  title: string;
  description: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  estimatedHours: number;
  resources: LearningResource[];
  tags: string[];
  progress?: number;
  isEnrolled?: boolean;
}

export interface LearningGoal {
  id: string;
  title: string;
  description: string;
  targetDate: Date;
  progress: number;
  relatedResources: string[]; // resource IDs
  milestones: Array<{
    id: string;
    title: string;
    completed: boolean;
    completedAt?: Date;
  }>;
}

export interface LearningFilters {
  resourceTypes: Array<'course' | 'tutorial' | 'paper' | 'book' | 'video' | 'documentation'>;
  difficulty: Array<'beginner' | 'intermediate' | 'advanced'>;
  providers: string[];
  topics: string[];
  priceFilter: 'all' | 'free' | 'paid';
  sortBy: 'relevance' | 'rating' | 'recent' | 'popular' | 'duration';
  searchQuery?: string;
}

export interface UseLearningContentOptions {
  autoRefresh?: boolean;
  refreshInterval?: number; // in minutes
  cacheTimeout?: number; // in minutes
  enableRecommendations?: boolean;
}

export interface UseLearningContentReturn {
  // Data
  resources: LearningResource[];
  learningPaths: LearningPath[];
  goals: LearningGoal[];
  recommendations: LearningResource[];
  bookmarkedResources: LearningResource[];
  
  // State
  loading: boolean;
  error: string | null;
  filters: LearningFilters;
  
  // Statistics
  totalResources: number;
  completedResources: number;
  totalLearningHours: number;
  currentStreak: number;
  lastUpdated: Date | null;
  
  // Actions
  setFilters: (filters: Partial<LearningFilters>) => void;
  search: (query: string) => Promise<void>;
  refresh: () => Promise<void>;
  clearError: () => void;
  
  // Resource management
  bookmarkResource: (resourceId: string) => Promise<void>;
  unbookmarkResource: (resourceId: string) => Promise<void>;
  updateProgress: (resourceId: string, progress: number) => Promise<void>;
  
  // Goal management
  createGoal: (goal: Omit<LearningGoal, 'id' | 'progress'>) => Promise<void>;
  updateGoal: (goalId: string, updates: Partial<LearningGoal>) => Promise<void>;
  deleteGoal: (goalId: string) => Promise<void>;
  
  // Learning path management
  enrollInPath: (pathId: string) => Promise<void>;
  unenrollFromPath: (pathId: string) => Promise<void>;
}

// Cache interface
interface CacheEntry<T> {
  data: T;
  timestamp: number;
  expiry: number;
}

// In-memory cache
const cache = new Map<string, CacheEntry<any>>();

const DEFAULT_FILTERS: LearningFilters = {
  resourceTypes: ['course', 'tutorial', 'video'],
  difficulty: ['beginner', 'intermediate', 'advanced'],
  providers: [],
  topics: [],
  priceFilter: 'all',
  sortBy: 'relevance'
};

const DEFAULT_OPTIONS: Required<UseLearningContentOptions> = {
  autoRefresh: true,
  refreshInterval: 120, // 2 hours
  cacheTimeout: 60,
  enableRecommendations: true
};

// Mock data generators for development
const generateMockResources = (): LearningResource[] => [
  {
    id: '1',
    title: 'Complete Machine Learning Course',
    description: 'Master machine learning from basics to advanced concepts with hands-on projects and real-world applications.',
    type: 'course',
    url: 'https://www.coursera.org/learn/machine-learning',
    provider: 'Coursera',
    difficulty: 'intermediate',
    duration: '54 hours',
    rating: 4.8,
    price: 'paid',
    tags: ['machine learning', 'python', 'data science', 'ai'],
    progress: 25,
    isBookmarked: true,
    thumbnailUrl: 'https://via.placeholder.com/400x225/1e40af/ffffff?text=ML+Course',
    authorName: 'Andrew Ng',
    publishedAt: new Date('2023-01-15'),
    enrollmentCount: 125000,
    certificateAvailable: true,
    languages: ['English', 'Spanish'],
    prerequisites: ['Basic Python', 'Linear Algebra']
  },
  {
    id: '2',
    title: 'React 18 Complete Tutorial',
    description: 'Learn React 18 with hooks, suspense, concurrent features, and modern development patterns.',
    type: 'video',
    url: 'https://www.youtube.com/watch?v=react18',
    provider: 'YouTube',
    difficulty: 'beginner',
    duration: '8 hours',
    rating: 4.6,
    price: 'free',
    tags: ['react', 'javascript', 'frontend', 'web development'],
    progress: 0,
    isBookmarked: false,
    thumbnailUrl: 'https://via.placeholder.com/400x225/06b6d4/ffffff?text=React+18',
    authorName: 'Mosh Hamedani',
    publishedAt: new Date('2023-12-01'),
    enrollmentCount: 45000,
    certificateAvailable: false,
    languages: ['English'],
    prerequisites: ['Basic JavaScript']
  },
  {
    id: '3',
    title: 'Deep Learning Specialization',
    description: 'Master deep learning and neural networks. Build career-relevant deep learning skills.',
    type: 'course',
    url: 'https://www.coursera.org/specializations/deep-learning',
    provider: 'Coursera',
    difficulty: 'advanced',
    duration: '90 hours',
    rating: 4.9,
    price: 'paid',
    tags: ['deep learning', 'neural networks', 'ai', 'python', 'tensorflow'],
    progress: 0,
    isBookmarked: false,
    thumbnailUrl: 'https://via.placeholder.com/400x225/7c3aed/ffffff?text=Deep+Learning',
    authorName: 'Andrew Ng',
    publishedAt: new Date('2023-06-20'),
    enrollmentCount: 75000,
    certificateAvailable: true,
    languages: ['English', 'Spanish', 'Chinese'],
    prerequisites: ['Machine Learning Basics', 'Python Programming']
  },
  {
    id: '4',
    title: 'TypeScript Handbook',
    description: 'Official TypeScript documentation and comprehensive guide to using TypeScript effectively.',
    type: 'documentation',
    url: 'https://www.typescriptlang.org/docs/',
    provider: 'Microsoft',
    difficulty: 'intermediate',
    duration: '10 hours',
    rating: 4.7,
    price: 'free',
    tags: ['typescript', 'javascript', 'programming', 'web development'],
    progress: 60,
    isBookmarked: true,
    thumbnailUrl: 'https://via.placeholder.com/400x225/3178c6/ffffff?text=TypeScript',
    authorName: 'Microsoft TypeScript Team',
    publishedAt: new Date('2023-11-15'),
    enrollmentCount: 200000,
    certificateAvailable: false,
    languages: ['English'],
    prerequisites: ['JavaScript Fundamentals']
  },
  {
    id: '5',
    title: 'AWS Cloud Practitioner',
    description: 'Prepare for AWS Cloud Practitioner certification with comprehensive cloud computing fundamentals.',
    type: 'course',
    url: 'https://aws.amazon.com/training/path-cloudpractitioner/',
    provider: 'AWS',
    difficulty: 'beginner',
    duration: '20 hours',
    rating: 4.5,
    price: 'free',
    tags: ['aws', 'cloud computing', 'certification', 'devops'],
    progress: 0,
    isBookmarked: false,
    thumbnailUrl: 'https://via.placeholder.com/400x225/ff9900/ffffff?text=AWS+Cloud',
    authorName: 'AWS Training Team',
    publishedAt: new Date('2023-10-01'),
    enrollmentCount: 95000,
    certificateAvailable: true,
    languages: ['English', 'Japanese'],
    prerequisites: ['Basic IT Knowledge']
  },
  {
    id: '6',
    title: 'Building LLM Applications',
    description: 'Learn to build production-ready applications using Large Language Models and modern AI techniques.',
    type: 'tutorial',
    url: 'https://platform.openai.com/docs/tutorials/web-qa-embeddings',
    provider: 'OpenAI',
    difficulty: 'advanced',
    duration: '15 hours',
    rating: 4.8,
    price: 'free',
    tags: ['llm', 'ai', 'openai', 'nlp', 'python'],
    progress: 40,
    isBookmarked: true,
    thumbnailUrl: 'https://via.placeholder.com/400x225/10b981/ffffff?text=LLM+Apps',
    authorName: 'OpenAI Team',
    publishedAt: new Date('2024-01-10'),
    enrollmentCount: 35000,
    certificateAvailable: false,
    languages: ['English'],
    prerequisites: ['Python Programming', 'API Experience']
  }
];

const generateMockLearningPaths = (): LearningPath[] => [
  {
    id: 'path-1',
    title: 'Full-Stack AI Developer',
    description: 'Complete path to become a full-stack AI developer with modern tools and frameworks.',
    difficulty: 'intermediate',
    estimatedHours: 200,
    resources: generateMockResources().slice(0, 4),
    tags: ['ai', 'full-stack', 'python', 'javascript'],
    progress: 35,
    isEnrolled: true
  },
  {
    id: 'path-2',
    title: 'Frontend React Mastery',
    description: 'Master modern React development with TypeScript and advanced patterns.',
    difficulty: 'intermediate',
    estimatedHours: 80,
    resources: generateMockResources().slice(1, 3),
    tags: ['react', 'frontend', 'typescript', 'web development'],
    progress: 15,
    isEnrolled: false
  }
];

const generateMockGoals = (): LearningGoal[] => [
  {
    id: 'goal-1',
    title: 'Complete ML Specialization',
    description: 'Finish the complete machine learning specialization by end of quarter.',
    targetDate: new Date('2024-03-31'),
    progress: 65,
    relatedResources: ['1', '3'],
    milestones: [
      { id: 'm1', title: 'Complete Linear Regression', completed: true, completedAt: new Date('2024-01-15') },
      { id: 'm2', title: 'Complete Neural Networks', completed: true, completedAt: new Date('2024-02-01') },
      { id: 'm3', title: 'Complete Deep Learning', completed: false }
    ]
  },
  {
    id: 'goal-2',
    title: 'Build AI Portfolio Project',
    description: 'Create a production-ready AI application using LLMs.',
    targetDate: new Date('2024-02-28'),
    progress: 30,
    relatedResources: ['6'],
    milestones: [
      { id: 'm4', title: 'Learn LLM APIs', completed: true, completedAt: new Date('2024-01-20') },
      { id: 'm5', title: 'Build MVP', completed: false },
      { id: 'm6', title: 'Deploy to Production', completed: false }
    ]
  }
];

// API functions (mock implementations for now)
const fetchLearningResources = async (filters: LearningFilters): Promise<LearningResource[]> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 800));
  
  let resources = generateMockResources();
  
  // Apply filters
  if (filters.searchQuery) {
    resources = resources.filter(resource =>
      resource.title.toLowerCase().includes(filters.searchQuery!.toLowerCase()) ||
      resource.description.toLowerCase().includes(filters.searchQuery!.toLowerCase()) ||
      resource.tags.some(tag => tag.toLowerCase().includes(filters.searchQuery!.toLowerCase()))
    );
  }
  
  if (filters.resourceTypes.length > 0) {
    resources = resources.filter(resource => filters.resourceTypes.includes(resource.type));
  }
  
  if (filters.difficulty.length > 0) {
    resources = resources.filter(resource => filters.difficulty.includes(resource.difficulty));
  }
  
  if (filters.priceFilter !== 'all') {
    resources = resources.filter(resource => resource.price === filters.priceFilter);
  }
  
  if (filters.providers.length > 0) {
    resources = resources.filter(resource => filters.providers.includes(resource.provider));
  }
  
  // Apply sorting
  switch (filters.sortBy) {
    case 'rating':
      resources.sort((a, b) => (b.rating || 0) - (a.rating || 0));
      break;
    case 'recent':
      resources.sort((a, b) => (b.publishedAt?.getTime() || 0) - (a.publishedAt?.getTime() || 0));
      break;
    case 'popular':
      resources.sort((a, b) => (b.enrollmentCount || 0) - (a.enrollmentCount || 0));
      break;
    case 'duration':
      resources.sort((a, b) => {
        const aDuration = parseInt(a.duration?.match(/\d+/)?.[0] || '0');
        const bDuration = parseInt(b.duration?.match(/\d+/)?.[0] || '0');
        return aDuration - bDuration;
      });
      break;
    default:
      // Keep original order for relevance
      break;
  }
  
  return resources;
};

const fetchLearningPaths = async (): Promise<LearningPath[]> => {
  await new Promise(resolve => setTimeout(resolve, 600));
  return generateMockLearningPaths();
};

const fetchLearningGoals = async (): Promise<LearningGoal[]> => {
  await new Promise(resolve => setTimeout(resolve, 400));
  return generateMockGoals();
};

const fetchRecommendations = async (userProfile?: any): Promise<LearningResource[]> => {
  await new Promise(resolve => setTimeout(resolve, 500));
  // For now, return a subset of resources as recommendations
  return generateMockResources().slice(0, 3);
};

// Utility functions
const getCacheKey = (key: string, filters?: any): string => {
  return filters ? `${key}-${JSON.stringify(filters)}` : key;
};

const setCache = <T>(key: string, data: T, ttl: number): void => {
  const now = Date.now();
  cache.set(key, {
    data,
    timestamp: now,
    expiry: now + ttl * 60 * 1000
  });
};

const getCache = <T>(key: string): T | null => {
  const entry = cache.get(key);
  if (!entry) return null;
  
  if (Date.now() > entry.expiry) {
    cache.delete(key);
    return null;
  }
  
  return entry.data as T;
};

export function useLearningContent(
  initialFilters: Partial<LearningFilters> = {},
  options: UseLearningContentOptions = {}
): UseLearningContentReturn {
  const [resources, setResources] = useState<LearningResource[]>([]);
  const [learningPaths, setLearningPaths] = useState<LearningPath[]>([]);
  const [goals, setGoals] = useState<LearningGoal[]>([]);
  const [recommendations, setRecommendations] = useState<LearningResource[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  
  const [filters, setFiltersState] = useState<LearningFilters>({
    ...DEFAULT_FILTERS,
    ...initialFilters
  });
  
  const opts = { ...DEFAULT_OPTIONS, ...options };
  
  // Derived state
  const bookmarkedResources = resources.filter(r => r.isBookmarked);
  const completedResources = resources.filter(r => (r.progress || 0) >= 100).length;
  const totalLearningHours = resources.reduce((total, r) => {
    const hours = parseInt(r.duration?.match(/\d+/)?.[0] || '0');
    const progress = (r.progress || 0) / 100;
    return total + (hours * progress);
  }, 0);
  
  // Load data function
  const loadData = useCallback(async (forceRefresh = false) => {
    try {
      setLoading(true);
      setError(null);
      
      const cacheKey = getCacheKey('learning-resources', filters);
      
      // Check cache first (unless forcing refresh)
      if (!forceRefresh) {
        const cachedResources = getCache<LearningResource[]>(cacheKey);
        const cachedPaths = getCache<LearningPath[]>('learning-paths');
        const cachedGoals = getCache<LearningGoal[]>('learning-goals');
        const cachedRecommendations = getCache<LearningResource[]>('recommendations');
        
        if (cachedResources && cachedPaths && cachedGoals) {
          setResources(cachedResources);
          setLearningPaths(cachedPaths);
          setGoals(cachedGoals);
          if (cachedRecommendations) {
            setRecommendations(cachedRecommendations);
          }
          setLastUpdated(new Date());
          setLoading(false);
          return;
        }
      }
      
      // Fetch fresh data
      const [resourcesData, pathsData, goalsData, recommendationsData] = await Promise.all([
        fetchLearningResources(filters),
        fetchLearningPaths(),
        fetchLearningGoals(),
        opts.enableRecommendations ? fetchRecommendations() : Promise.resolve([])
      ]);
      
      // Cache the results
      setCache(cacheKey, resourcesData, opts.cacheTimeout);
      setCache('learning-paths', pathsData, opts.cacheTimeout);
      setCache('learning-goals', goalsData, opts.cacheTimeout);
      if (opts.enableRecommendations) {
        setCache('recommendations', recommendationsData, opts.cacheTimeout);
      }
      
      setResources(resourcesData);
      setLearningPaths(pathsData);
      setGoals(goalsData);
      setRecommendations(recommendationsData);
      setLastUpdated(new Date());
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load learning content');
    } finally {
      setLoading(false);
    }
  }, [filters, opts.cacheTimeout, opts.enableRecommendations]);
  
  // Initial load
  useEffect(() => {
    loadData();
  }, [loadData]);
  
  // Auto-refresh
  useEffect(() => {
    if (!opts.autoRefresh) return;
    
    const interval = setInterval(() => {
      loadData();
    }, opts.refreshInterval * 60 * 1000);
    
    return () => clearInterval(interval);
  }, [loadData, opts.autoRefresh, opts.refreshInterval]);
  
  // Actions
  const setFilters = useCallback((newFilters: Partial<LearningFilters>) => {
    setFiltersState(prev => ({ ...prev, ...newFilters }));
  }, []);
  
  const search = useCallback(async (query: string) => {
    setFilters({ searchQuery: query });
  }, [setFilters]);
  
  const refresh = useCallback(async () => {
    await loadData(true);
  }, [loadData]);
  
  const clearError = useCallback(() => {
    setError(null);
  }, []);
  
  // Resource management
  const bookmarkResource = useCallback(async (resourceId: string) => {
    setResources(prev => prev.map(r => 
      r.id === resourceId ? { ...r, isBookmarked: true } : r
    ));
    // In a real app, this would make an API call
  }, []);
  
  const unbookmarkResource = useCallback(async (resourceId: string) => {
    setResources(prev => prev.map(r => 
      r.id === resourceId ? { ...r, isBookmarked: false } : r
    ));
  }, []);
  
  const updateProgress = useCallback(async (resourceId: string, progress: number) => {
    setResources(prev => prev.map(r => 
      r.id === resourceId ? { ...r, progress } : r
    ));
  }, []);
  
  // Goal management
  const createGoal = useCallback(async (goal: Omit<LearningGoal, 'id' | 'progress'>) => {
    const newGoal: LearningGoal = {
      ...goal,
      id: `goal-${Date.now()}`,
      progress: 0
    };
    setGoals(prev => [...prev, newGoal]);
  }, []);
  
  const updateGoal = useCallback(async (goalId: string, updates: Partial<LearningGoal>) => {
    setGoals(prev => prev.map(g => 
      g.id === goalId ? { ...g, ...updates } : g
    ));
  }, []);
  
  const deleteGoal = useCallback(async (goalId: string) => {
    setGoals(prev => prev.filter(g => g.id !== goalId));
  }, []);
  
  // Learning path management
  const enrollInPath = useCallback(async (pathId: string) => {
    setLearningPaths(prev => prev.map(p => 
      p.id === pathId ? { ...p, isEnrolled: true } : p
    ));
  }, []);
  
  const unenrollFromPath = useCallback(async (pathId: string) => {
    setLearningPaths(prev => prev.map(p => 
      p.id === pathId ? { ...p, isEnrolled: false } : p
    ));
  }, []);
  
  return {
    // Data
    resources,
    learningPaths,
    goals,
    recommendations,
    bookmarkedResources,
    
    // State
    loading,
    error,
    filters,
    
    // Statistics
    totalResources: resources.length,
    completedResources,
    totalLearningHours: Math.round(totalLearningHours * 10) / 10,
    currentStreak: 7, // Mock streak
    lastUpdated,
    
    // Actions
    setFilters,
    search,
    refresh,
    clearError,
    
    // Resource management
    bookmarkResource,
    unbookmarkResource,
    updateProgress,
    
    // Goal management
    createGoal,
    updateGoal,
    deleteGoal,
    
    // Learning path management
    enrollInPath,
    unenrollFromPath
  };
}

// Convenience hook for widget usage
export function useLearningWidget(settings: any) {
  return useLearningContent(
    {
      resourceTypes: settings.resourceTypes || ['course', 'tutorial', 'video'],
      difficulty: settings.difficulty || ['beginner', 'intermediate', 'advanced'],
      providers: settings.providers || [],
      topics: settings.topics || [],
      sortBy: settings.sortBy || 'relevance',
      priceFilter: 'all'
    },
    {
      autoRefresh: true,
      refreshInterval: 120,
      cacheTimeout: 60,
      enableRecommendations: true
    }
  );
}