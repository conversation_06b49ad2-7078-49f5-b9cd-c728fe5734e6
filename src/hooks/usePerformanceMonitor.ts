/**
 * Performance monitoring hook for Hub Dashboard
 */

import { useEffect, useRef, useCallback } from 'react';
import { useHubStore } from '@/stores/hubStore';

export interface PerformanceMetrics {
  timestamp: number;
  widgetLoadTime: number;
  renderTime: number;
  memoryUsage: number;
  apiResponseTime: number;
  errorCount: number;
  interactionLatency: number;
}

export interface WidgetMetrics {
  widgetId: string;
  widgetType: string;
  loadTime: number;
  renderTime: number;
  updateFrequency: number;
  errorCount: number;
  lastError?: string;
}

class PerformanceMonitor {
  private metrics: PerformanceMetrics[] = [];
  private widgetMetrics: Map<string, WidgetMetrics> = new Map();
  private observers: Set<(metrics: PerformanceMetrics) => void> = new Set();
  private performanceObserver?: PerformanceObserver;
  private memoryInterval?: NodeJS.Timeout;

  constructor() {
    this.initializeObservers();
  }

  private initializeObservers() {
    // Performance Observer for navigation and resource timing
    if ('PerformanceObserver' in window) {
      this.performanceObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        for (const entry of entries) {
          this.handlePerformanceEntry(entry);
        }
      });

      try {
        this.performanceObserver.observe({
          entryTypes: ['navigation', 'resource', 'measure', 'paint']
        });
      } catch (e) {
        console.warn('Performance Observer not fully supported:', e);
      }
    }

    // Memory usage monitoring
    this.startMemoryMonitoring();

    // React DevTools Profiler integration
    this.setupReactProfiler();
  }

  private startMemoryMonitoring() {
    if ('memory' in performance) {
      this.memoryInterval = setInterval(() => {
        const memory = (performance as any).memory;
        if (memory) {
          this.recordMemoryUsage(memory.usedJSHeapSize);
        }
      }, 5000); // Check every 5 seconds
    }
  }

  private setupReactProfiler() {
    // Listen for React DevTools profiler data if available
    if (typeof window !== 'undefined' && 'React' in window) {
      try {
        const React = (window as any).React;
        if (React && React.Profiler) {
          // Set up React Profiler integration
          this.setupReactProfilerCallback();
        }
      } catch (e) {
        // React DevTools not available
      }
    }
  }

  private setupReactProfilerCallback() {
    // This would be called by React Profiler components
    (window as any).__CLAUDIA_PROFILER_CALLBACK = (
      id: string,
      phase: 'mount' | 'update',
      actualDuration: number,
      baseDuration: number,
      startTime: number,
      commitTime: number
    ) => {
      this.recordRenderMetrics(id, phase, actualDuration, baseDuration);
    };
  }

  private handlePerformanceEntry(entry: PerformanceEntry) {
    switch (entry.entryType) {
      case 'navigation':
        const navEntry = entry as PerformanceNavigationTiming;
        this.recordNavigationMetrics(navEntry);
        break;
      case 'resource':
        const resourceEntry = entry as PerformanceResourceTiming;
        this.recordResourceMetrics(resourceEntry);
        break;
      case 'measure':
        const measureEntry = entry as PerformanceMeasure;
        this.recordCustomMeasure(measureEntry);
        break;
      case 'paint':
        const paintEntry = entry as PerformancePaintTiming;
        this.recordPaintMetrics(paintEntry);
        break;
    }
  }

  private recordNavigationMetrics(entry: PerformanceNavigationTiming) {
    const loadTime = entry.loadEventEnd - entry.navigationStart;
    this.addMetric({
      timestamp: Date.now(),
      widgetLoadTime: 0,
      renderTime: entry.domContentLoadedEventEnd - entry.domContentLoadedEventStart,
      memoryUsage: this.getCurrentMemoryUsage(),
      apiResponseTime: 0,
      errorCount: 0,
      interactionLatency: loadTime
    });
  }

  private recordResourceMetrics(entry: PerformanceResourceTiming) {
    if (entry.name.includes('/api/') || entry.name.includes('github.com/')) {
      const responseTime = entry.responseEnd - entry.requestStart;
      this.updateApiResponseTime(responseTime);
    }
  }

  private recordCustomMeasure(entry: PerformanceMeasure) {
    if (entry.name.startsWith('widget-')) {
      const widgetId = entry.name.replace('widget-', '');
      this.updateWidgetMetrics(widgetId, { loadTime: entry.duration });
    }
  }

  private recordPaintMetrics(entry: PerformancePaintTiming) {
    // Record First Contentful Paint, etc.
    console.debug(`Paint metric: ${entry.name} - ${entry.startTime}ms`);
  }

  private recordRenderMetrics(id: string, phase: string, actualDuration: number, baseDuration: number) {
    if (id.startsWith('widget-')) {
      const widgetId = id.replace('widget-', '');
      this.updateWidgetMetrics(widgetId, { renderTime: actualDuration });
    }
  }

  private getCurrentMemoryUsage(): number {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      return memory ? memory.usedJSHeapSize : 0;
    }
    return 0;
  }

  private recordMemoryUsage(usage: number) {
    this.addMetric({
      timestamp: Date.now(),
      widgetLoadTime: 0,
      renderTime: 0,
      memoryUsage: usage,
      apiResponseTime: 0,
      errorCount: 0,
      interactionLatency: 0
    });
  }

  private updateApiResponseTime(responseTime: number) {
    this.addMetric({
      timestamp: Date.now(),
      widgetLoadTime: 0,
      renderTime: 0,
      memoryUsage: this.getCurrentMemoryUsage(),
      apiResponseTime: responseTime,
      errorCount: 0,
      interactionLatency: 0
    });
  }

  private addMetric(metric: PerformanceMetrics) {
    this.metrics.push(metric);
    
    // Keep only last 100 metrics to prevent memory bloat
    if (this.metrics.length > 100) {
      this.metrics = this.metrics.slice(-100);
    }

    // Notify observers
    this.observers.forEach(observer => observer(metric));
  }

  // Public API
  public startWidgetMeasure(widgetId: string, widgetType: string) {
    performance.mark(`widget-${widgetId}-start`);
    
    if (!this.widgetMetrics.has(widgetId)) {
      this.widgetMetrics.set(widgetId, {
        widgetId,
        widgetType,
        loadTime: 0,
        renderTime: 0,
        updateFrequency: 0,
        errorCount: 0
      });
    }
  }

  public endWidgetMeasure(widgetId: string) {
    performance.mark(`widget-${widgetId}-end`);
    performance.measure(
      `widget-${widgetId}`,
      `widget-${widgetId}-start`,
      `widget-${widgetId}-end`
    );
  }

  public recordWidgetError(widgetId: string, error: string) {
    const metrics = this.widgetMetrics.get(widgetId);
    if (metrics) {
      metrics.errorCount += 1;
      metrics.lastError = error;
      this.widgetMetrics.set(widgetId, metrics);
    }

    // Add to global error count
    this.addMetric({
      timestamp: Date.now(),
      widgetLoadTime: 0,
      renderTime: 0,
      memoryUsage: this.getCurrentMemoryUsage(),
      apiResponseTime: 0,
      errorCount: 1,
      interactionLatency: 0
    });
  }

  public updateWidgetMetrics(widgetId: string, updates: Partial<WidgetMetrics>) {
    const existing = this.widgetMetrics.get(widgetId);
    if (existing) {
      this.widgetMetrics.set(widgetId, { ...existing, ...updates });
    }
  }

  public getMetrics(): PerformanceMetrics[] {
    return [...this.metrics];
  }

  public getWidgetMetrics(): Map<string, WidgetMetrics> {
    return new Map(this.widgetMetrics);
  }

  public getAverageMetrics(): Partial<PerformanceMetrics> {
    if (this.metrics.length === 0) return {};

    const totals = this.metrics.reduce((acc, metric) => ({
      widgetLoadTime: acc.widgetLoadTime + metric.widgetLoadTime,
      renderTime: acc.renderTime + metric.renderTime,
      memoryUsage: acc.memoryUsage + metric.memoryUsage,
      apiResponseTime: acc.apiResponseTime + metric.apiResponseTime,
      errorCount: acc.errorCount + metric.errorCount,
      interactionLatency: acc.interactionLatency + metric.interactionLatency
    }), {
      widgetLoadTime: 0,
      renderTime: 0,
      memoryUsage: 0,
      apiResponseTime: 0,
      errorCount: 0,
      interactionLatency: 0
    });

    const count = this.metrics.length;
    return {
      widgetLoadTime: totals.widgetLoadTime / count,
      renderTime: totals.renderTime / count,
      memoryUsage: totals.memoryUsage / count,
      apiResponseTime: totals.apiResponseTime / count,
      errorCount: totals.errorCount,
      interactionLatency: totals.interactionLatency / count
    };
  }

  public subscribe(callback: (metrics: PerformanceMetrics) => void) {
    this.observers.add(callback);
    return () => this.observers.delete(callback);
  }

  public cleanup() {
    if (this.performanceObserver) {
      this.performanceObserver.disconnect();
    }
    if (this.memoryInterval) {
      clearInterval(this.memoryInterval);
    }
    this.observers.clear();
  }
}

// Singleton instance
const performanceMonitor = new PerformanceMonitor();

// React hook
export const usePerformanceMonitor = () => {
  const metricsRef = useRef<PerformanceMetrics[]>([]);
  const { settings, setError } = useHubStore();

  const startWidgetMeasure = useCallback((widgetId: string, widgetType: string) => {
    if (settings.performance.enableVirtualization) {
      performanceMonitor.startWidgetMeasure(widgetId, widgetType);
    }
  }, [settings.performance.enableVirtualization]);

  const endWidgetMeasure = useCallback((widgetId: string) => {
    if (settings.performance.enableVirtualization) {
      performanceMonitor.endWidgetMeasure(widgetId);
    }
  }, [settings.performance.enableVirtualization]);

  const recordWidgetError = useCallback((widgetId: string, error: string) => {
    performanceMonitor.recordWidgetError(widgetId, error);
    if (settings.notifications.errorNotifications) {
      setError(`Widget error: ${error}`);
    }
  }, [settings.notifications.errorNotifications, setError]);

  const getMetrics = useCallback(() => {
    return {
      current: performanceMonitor.getMetrics(),
      average: performanceMonitor.getAverageMetrics(),
      widgets: performanceMonitor.getWidgetMetrics()
    };
  }, []);

  const subscribeToMetrics = useCallback((callback: (metrics: PerformanceMetrics) => void) => {
    return performanceMonitor.subscribe(callback);
  }, []);

  // Memory leak detection
  useEffect(() => {
    if (!settings.advanced.debugMode) return;

    const interval = setInterval(() => {
      const currentMemory = performanceMonitor.getCurrentMemoryUsage();
      const recentMetrics = performanceMonitor.getMetrics().slice(-10);
      
      if (recentMetrics.length >= 10) {
        const avgMemory = recentMetrics.reduce((sum, m) => sum + m.memoryUsage, 0) / 10;
        const memoryIncrease = currentMemory - avgMemory;
        
        // Alert if memory usage increased by more than 50MB
        if (memoryIncrease > 50 * 1024 * 1024) {
          console.warn('Potential memory leak detected:', {
            current: (currentMemory / 1024 / 1024).toFixed(2) + 'MB',
            average: (avgMemory / 1024 / 1024).toFixed(2) + 'MB',
            increase: (memoryIncrease / 1024 / 1024).toFixed(2) + 'MB'
          });
        }
      }
    }, 30000); // Check every 30 seconds

    return () => clearInterval(interval);
  }, [settings.advanced.debugMode]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      performanceMonitor.cleanup();
    };
  }, []);

  return {
    startWidgetMeasure,
    endWidgetMeasure,
    recordWidgetError,
    getMetrics,
    subscribeToMetrics
  };
};

export default performanceMonitor;