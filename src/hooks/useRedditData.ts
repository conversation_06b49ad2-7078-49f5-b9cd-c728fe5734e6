import { useState, useEffect, useCallback } from 'react';
import type { RedditPost, RedditWidgetSettings } from '@/types/hub';

interface RedditAPIResponse {
  data: {
    children: Array<{
      data: {
        id: string;
        title: string;
        author: string;
        score: number;
        url: string;
        permalink: string;
        subreddit: string;
        created_utc: number;
        num_comments: number;
        is_self: boolean;
        selftext?: string;
        preview?: {
          images: Array<{
            source: {
              url: string;
              width: number;
              height: number;
            };
          }>;
        };
      };
    }>;
  };
}

interface UseRedditDataResult {
  posts: RedditPost[];
  isLoading: boolean;
  error: string | null;
  lastUpdated: Date | null;
  refresh: () => Promise<void>;
}

interface UseRedditDataOptions {
  settings: RedditWidgetSettings;
  enabled?: boolean;
  cacheKey?: string;
}

// In-memory cache with TTL
const cache = new Map<string, { data: RedditPost[]; expiry: number }>();
const CACHE_TTL = 30 * 60 * 1000; // 30 minutes

/**
 * Custom hook for fetching Reddit data with caching and error handling
 */
export const useRedditData = ({ 
  settings, 
  enabled = true, 
  cacheKey 
}: UseRedditDataOptions): UseRedditDataResult => {
  const [posts, setPosts] = useState<RedditPost[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  // Generate cache key based on settings
  const getCacheKey = useCallback(() => {
    if (cacheKey) return cacheKey;
    
    const settingsKey = `${settings.subreddits.join(',')}:${settings.sortBy}:${settings.postLimit}:${settings.timeRange || 'all'}`;
    return `reddit:${settingsKey}`;
  }, [settings, cacheKey]);

  // Check cache first
  const getCachedData = useCallback(() => {
    const key = getCacheKey();
    const cached = cache.get(key);
    
    if (cached && Date.now() < cached.expiry) {
      return cached.data;
    }
    
    if (cached) {
      cache.delete(key); // Remove expired data
    }
    
    return null;
  }, [getCacheKey]);

  // Set cache data
  const setCachedData = useCallback((data: RedditPost[]) => {
    const key = getCacheKey();
    cache.set(key, {
      data,
      expiry: Date.now() + CACHE_TTL
    });
  }, [getCacheKey]);

  // Build Reddit API URL
  const buildRedditUrl = useCallback((subreddit: string) => {
    const baseUrl = `https://www.reddit.com/r/${subreddit}/${settings.sortBy}.json`;
    const params = new URLSearchParams();
    
    params.append('limit', settings.postLimit.toString());
    
    // Add time range for 'top' sort
    if (settings.sortBy === 'top' && settings.timeRange) {
      params.append('t', settings.timeRange);
    }
    
    // Prevent CORS issues
    params.append('raw_json', '1');
    
    return `${baseUrl}?${params.toString()}`;
  }, [settings]);

  // Transform Reddit API response to our format
  const transformRedditPost = useCallback((redditData: any): RedditPost => {
    return {
      id: redditData.id,
      title: redditData.title,
      author: redditData.author,
      score: redditData.score,
      url: redditData.url,
      permalink: `https://reddit.com${redditData.permalink}`,
      subreddit: redditData.subreddit,
      created_utc: redditData.created_utc,
      num_comments: redditData.num_comments,
      is_self: redditData.is_self,
      selftext: redditData.selftext,
      preview: redditData.preview ? {
        images: redditData.preview.images.map((img: any) => ({
          source: {
            url: img.source.url.replace(/&amp;/g, '&'), // Decode HTML entities
            width: img.source.width,
            height: img.source.height
          }
        }))
      } : undefined
    };
  }, []);

  // Fetch data from a single subreddit
  const fetchSubredditData = useCallback(async (subreddit: string): Promise<RedditPost[]> => {
    const url = buildRedditUrl(subreddit);
    
    try {
      const response = await fetch(url, {
        headers: {
          'User-Agent': 'Claudia Hub Dashboard/1.0.0'
        }
      });
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data: RedditAPIResponse = await response.json();
      
      return data.data.children.map(child => transformRedditPost(child.data));
    } catch (fetchError) {
      console.error(`Failed to fetch r/${subreddit}:`, fetchError);
      throw new Error(`Failed to fetch r/${subreddit}: ${fetchError instanceof Error ? fetchError.message : 'Unknown error'}`);
    }
  }, [buildRedditUrl, transformRedditPost]);

  // Main fetch function
  const fetchRedditData = useCallback(async (): Promise<RedditPost[]> => {
    const promises = settings.subreddits.map(subreddit => 
      fetchSubredditData(subreddit)
    );
    
    try {
      const results = await Promise.allSettled(promises);
      const allPosts: RedditPost[] = [];
      const errors: string[] = [];
      
      results.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          allPosts.push(...result.value);
        } else {
          errors.push(`r/${settings.subreddits[index]}: ${result.reason.message}`);
        }
      });
      
      // If we have some posts but also some errors, show a warning
      if (errors.length > 0 && allPosts.length > 0) {
        console.warn('Some subreddits failed to load:', errors);
      } else if (errors.length > 0 && allPosts.length === 0) {
        throw new Error(`All subreddits failed to load: ${errors.join(', ')}`);
      }
      
      // Sort all posts by score (descending) or creation time
      const sortedPosts = allPosts.sort((a, b) => {
        if (settings.sortBy === 'new') {
          return b.created_utc - a.created_utc;
        }
        return b.score - a.score; // Default to score-based sorting
      });
      
      // Limit to the requested number of posts
      return sortedPosts.slice(0, settings.postLimit);
      
    } catch (fetchError) {
      throw new Error(`Failed to fetch Reddit data: ${fetchError instanceof Error ? fetchError.message : 'Unknown error'}`);
    }
  }, [settings, fetchSubredditData]);

  // Main refresh function
  const refresh = useCallback(async () => {
    if (!enabled) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      const data = await fetchRedditData();
      setPosts(data);
      setCachedData(data);
      setLastUpdated(new Date());
    } catch (fetchError) {
      const errorMessage = fetchError instanceof Error ? fetchError.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('Reddit data fetch error:', fetchError);
    } finally {
      setIsLoading(false);
    }
  }, [enabled, fetchRedditData, setCachedData]);

  // Initial load effect
  useEffect(() => {
    if (!enabled) return;
    
    // Check cache first
    const cachedData = getCachedData();
    if (cachedData) {
      setPosts(cachedData);
      setLastUpdated(new Date()); // We don't store cache timestamp, so use current time
      return;
    }
    
    // If no cache, fetch fresh data
    refresh();
  }, [enabled, refresh, getCachedData]);

  // Settings change effect
  useEffect(() => {
    if (!enabled) return;
    
    // Clear current data when settings change
    setPosts([]);
    setError(null);
    
    // Check cache for new settings
    const cachedData = getCachedData();
    if (cachedData) {
      setPosts(cachedData);
      setLastUpdated(new Date());
    } else {
      refresh();
    }
  }, [settings.subreddits.join(','), settings.sortBy, settings.postLimit, settings.timeRange, enabled, refresh, getCachedData]);

  return {
    posts,
    isLoading,
    error,
    lastUpdated,
    refresh
  };
};

// Utility function to clear all Reddit cache
export const clearRedditCache = () => {
  const keysToDelete = Array.from(cache.keys()).filter(key => key.startsWith('reddit:'));
  keysToDelete.forEach(key => cache.delete(key));
  console.log(`Cleared ${keysToDelete.length} Reddit cache entries`);
};

// Utility function to get cache stats
export const getRedditCacheStats = () => {
  const redditEntries = Array.from(cache.entries()).filter(([key]) => key.startsWith('reddit:'));
  const now = Date.now();
  
  return {
    total: redditEntries.length,
    expired: redditEntries.filter(([, value]) => now >= value.expiry).length,
    active: redditEntries.filter(([, value]) => now < value.expiry).length
  };
};