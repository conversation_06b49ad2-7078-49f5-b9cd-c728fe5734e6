import { useState, useEffect, useCallback } from 'react';
import { 
  fetchTrendingRepos, 
  fetchTrendingReposEnhanced,
  fetchTrendingDevelopers,
  fetchTrendingTopics,
  searchRepositories,
  fetchRepositoryStats,
  type TrendingRepo,
  type GitHubDeveloper,
  type GitHubTopic
} from '@/lib/github-api';

export type GitHubContentType = 'repositories' | 'developers' | 'topics';
export type TimeRange = 'daily' | 'weekly' | 'monthly';
export type SortOption = 'stars' | 'forks' | 'updated';

export interface GitHubTrendingData {
  repositories: TrendingRepo[];
  developers: GitHubDeveloper[];
  topics: GitHubTopic[];
}

export interface GitHubTrendingFilters {
  contentType: GitHubContentType;
  timeRange: TimeRange;
  language: string;
  sort: SortOption;
  searchQuery?: string;
}

export interface UseGitHubTrendingOptions {
  autoRefresh?: boolean;
  refreshInterval?: number; // in minutes
  cacheTimeout?: number; // in minutes
  enhanced?: boolean; // use enhanced API calls
}

export interface UseGitHubTrendingReturn {
  data: GitHubTrendingData;
  loading: boolean;
  error: string | null;
  filters: GitHubTrendingFilters;
  
  // Actions
  setFilters: (filters: Partial<GitHubTrendingFilters>) => void;
  refresh: () => Promise<void>;
  search: (query: string) => Promise<void>;
  clearError: () => void;
  
  // Statistics
  totalRepos: number;
  totalDevelopers: number;
  totalTopics: number;
  lastUpdated: Date | null;
}

// Cache interface
interface CacheEntry<T> {
  data: T;
  timestamp: number;
  expiry: number;
}

// In-memory cache
const cache = new Map<string, CacheEntry<any>>();

const DEFAULT_FILTERS: GitHubTrendingFilters = {
  contentType: 'repositories',
  timeRange: 'weekly',
  language: 'all',
  sort: 'stars'
};

const DEFAULT_OPTIONS: Required<UseGitHubTrendingOptions> = {
  autoRefresh: true,
  refreshInterval: 30,
  cacheTimeout: 60,
  enhanced: true
};

export function useGitHubTrending(
  initialFilters: Partial<GitHubTrendingFilters> = {},
  options: UseGitHubTrendingOptions = {}
): UseGitHubTrendingReturn {
  const [data, setData] = useState<GitHubTrendingData>({
    repositories: [],
    developers: [],
    topics: []
  });
  
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  
  const [filters, setFiltersState] = useState<GitHubTrendingFilters>({
    ...DEFAULT_FILTERS,
    ...initialFilters
  });
  
  const opts = { ...DEFAULT_OPTIONS, ...options };

  // Cache helpers
  const getCacheKey = (type: string, filters: GitHubTrendingFilters, query?: string): string => {
    return `github_${type}_${filters.timeRange}_${filters.language}_${filters.sort}_${query || 'none'}`;
  };

  const getCachedData = <T>(key: string): T | null => {
    const cached = cache.get(key);
    if (!cached) return null;
    
    if (Date.now() > cached.expiry) {
      cache.delete(key);
      return null;
    }
    
    return cached.data;
  };

  const setCachedData = <T>(key: string, data: T): void => {
    cache.set(key, {
      data,
      timestamp: Date.now(),
      expiry: Date.now() + (opts.cacheTimeout * 60 * 1000)
    });
  };

  // Data fetchers
  const fetchRepositories = useCallback(async (
    searchQuery?: string
  ): Promise<TrendingRepo[]> => {
    const cacheKey = getCacheKey('repos', filters, searchQuery);
    const cached = getCachedData<TrendingRepo[]>(cacheKey);
    if (cached) return cached;

    let repos: TrendingRepo[];
    
    if (searchQuery) {
      repos = await searchRepositories(
        searchQuery,
        filters.language === 'all' ? undefined : filters.language,
        filters.sort,
        filters.timeRange
      );
    } else if (opts.enhanced) {
      repos = await fetchTrendingReposEnhanced(
        filters.timeRange,
        filters.language === 'all' ? undefined : filters.language
      );
    } else {
      repos = await fetchTrendingRepos(
        filters.timeRange,
        filters.language === 'all' ? undefined : filters.language
      );
    }

    // Enhance with statistics
    const enhancedRepos = await fetchRepositoryStats(repos);
    
    setCachedData(cacheKey, enhancedRepos);
    return enhancedRepos;
  }, [filters, opts.enhanced, opts.cacheTimeout]);

  const fetchDevelopersData = useCallback(async (): Promise<GitHubDeveloper[]> => {
    const cacheKey = getCacheKey('developers', filters);
    const cached = getCachedData<GitHubDeveloper[]>(cacheKey);
    if (cached) return cached;

    const developers = await fetchTrendingDevelopers(
      filters.timeRange,
      filters.language === 'all' ? undefined : filters.language
    );
    
    setCachedData(cacheKey, developers);
    return developers;
  }, [filters, opts.cacheTimeout]);

  const fetchTopicsData = useCallback(async (): Promise<GitHubTopic[]> => {
    const cacheKey = getCacheKey('topics', filters);
    const cached = getCachedData<GitHubTopic[]>(cacheKey);
    if (cached) return cached;

    const topics = await fetchTrendingTopics(filters.timeRange);
    
    setCachedData(cacheKey, topics);
    return topics;
  }, [filters, opts.cacheTimeout]);

  // Main data fetching function
  const fetchData = useCallback(async (searchQuery?: string) => {
    if (loading) return;
    
    setLoading(true);
    setError(null);

    try {
      const newData = { ...data };

      // Fetch based on current content type or fetch all if needed
      if (filters.contentType === 'repositories' || !data.repositories.length) {
        newData.repositories = await fetchRepositories(searchQuery);
      }
      
      if (filters.contentType === 'developers' || !data.developers.length) {
        newData.developers = await fetchDevelopersData();
      }
      
      if (filters.contentType === 'topics' || !data.topics.length) {
        newData.topics = await fetchTopicsData();
      }

      setData(newData);
      setLastUpdated(new Date());
    } catch (err) {
      const message = err instanceof Error ? err.message : 'Failed to fetch GitHub data';
      setError(message);
      console.error('GitHub API Error:', err);
    } finally {
      setLoading(false);
    }
  }, [
    loading, 
    data, 
    filters.contentType, 
    fetchRepositories, 
    fetchDevelopersData, 
    fetchTopicsData
  ]);

  // Actions
  const setFilters = useCallback((newFilters: Partial<GitHubTrendingFilters>) => {
    setFiltersState(prev => ({ ...prev, ...newFilters }));
  }, []);

  const refresh = useCallback(async () => {
    // Clear relevant cache entries
    const patterns = ['repos', 'developers', 'topics'];
    for (const [key] of cache) {
      if (patterns.some(pattern => key.includes(pattern))) {
        cache.delete(key);
      }
    }
    await fetchData();
  }, [fetchData]);

  const search = useCallback(async (query: string) => {
    if (filters.contentType === 'repositories') {
      await fetchData(query);
    }
  }, [filters.contentType, fetchData]);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Auto-refresh effect
  useEffect(() => {
    if (!opts.autoRefresh) return;

    const interval = setInterval(() => {
      refresh();
    }, opts.refreshInterval * 60 * 1000);

    return () => clearInterval(interval);
  }, [opts.autoRefresh, opts.refreshInterval, refresh]);

  // Initial data fetch and filter changes
  useEffect(() => {
    fetchData();
  }, [filters.contentType, filters.timeRange, filters.language, filters.sort]);

  return {
    data,
    loading,
    error,
    filters,
    setFilters,
    refresh,
    search,
    clearError,
    totalRepos: data.repositories.length,
    totalDevelopers: data.developers.length,
    totalTopics: data.topics.length,
    lastUpdated
  };
}

// Helper hook for widget-specific usage
export function useGitHubTrendingWidget(
  widgetSettings: {
    timeRange?: TimeRange;
    language?: string;
    repoLimit?: number;
    includeStarsToday?: boolean;
  } = {}
) {
  const trending = useGitHubTrending(
    {
      contentType: 'repositories',
      timeRange: widgetSettings.timeRange || 'weekly',
      language: widgetSettings.language || 'all',
      sort: 'stars'
    },
    {
      autoRefresh: true,
      refreshInterval: 60, // 1 hour for widgets
      cacheTimeout: 30,    // 30 minutes cache
      enhanced: true
    }
  );

  // Limit repositories if specified
  const limitedData = {
    ...trending.data,
    repositories: widgetSettings.repoLimit 
      ? trending.data.repositories.slice(0, widgetSettings.repoLimit)
      : trending.data.repositories
  };

  return {
    ...trending,
    data: limitedData
  };
}