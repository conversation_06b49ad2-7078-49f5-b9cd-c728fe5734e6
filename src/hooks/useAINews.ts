import { useState, useEffect, useCallback, useRef } from 'react';
import { invoke } from '@tauri-apps/api/core';
import type { AINewsItem, AINewsWidgetSettings } from '@/types/hub';

interface UseAINewsOptions {
  settings: AINewsWidgetSettings;
  enabled: boolean;
  cacheKey: string;
}

interface UseAINewsReturn {
  articles: AINewsItem[];
  isLoading: boolean;
  error: string | null;
  lastUpdated: Date | null;
  refresh: () => Promise<void>;
  bookmarkArticle: (articleId: string) => void;
  removeBookmark: (articleId: string) => void;
  searchArticles: (query: string) => AINewsItem[];
}



// AI/ML relevance keywords with scoring
const AI_KEYWORDS = {
  high: ['artificial intelligence', 'machine learning', 'deep learning', 'neural network', 'llm', 'large language model', 'gpt', 'claude', 'openai', 'anthropic', 'transformer', 'attention', 'bert', 'nlp', 'computer vision', 'reinforcement learning', 'generative ai', 'diffusion', 'stable diffusion', 'chatgpt'],
  medium: ['ai', 'ml', 'algorithm', 'data science', 'python', 'tensorflow', 'pytorch', 'hugging face', 'model', 'training', 'inference', 'embedding', 'vector', 'semantic', 'classification', 'regression', 'clustering', 'supervised', 'unsupervised'],
  low: ['automation', 'prediction', 'analytics', 'intelligence', 'smart', 'cognitive', 'neural', 'learning', 'pattern', 'recognition', 'optimization']
};

// Calculate AI relevance score for content
const calculateRelevanceScore = (title: string, description?: string): number => {
  const content = `${title} ${description || ''}`.toLowerCase();
  let score = 0;
  
  AI_KEYWORDS.high.forEach(keyword => {
    if (content.includes(keyword)) score += 10;
  });
  
  AI_KEYWORDS.medium.forEach(keyword => {
    if (content.includes(keyword)) score += 5;
  });
  
  AI_KEYWORDS.low.forEach(keyword => {
    if (content.includes(keyword)) score += 2;
  });
  
  return score;
};

// Categorize content based on keywords
const categorizeContent = (title: string, description?: string): AINewsItem['category'] => {
  const content = `${title} ${description || ''}`.toLowerCase();
  
  if (content.includes('nlp') || content.includes('natural language') || content.includes('text') || content.includes('language model')) {
    return 'nlp';
  }
  if (content.includes('computer vision') || content.includes('image') || content.includes('vision') || content.includes('cv')) {
    return 'cv';
  }
  if (content.includes('robot') || content.includes('autonomous') || content.includes('control')) {
    return 'robotics';
  }
  if (content.includes('machine learning') || content.includes('ml') || content.includes('supervised') || content.includes('unsupervised')) {
    return 'ml';
  }
  if (content.includes('artificial intelligence') || content.includes('ai') || content.includes('neural') || content.includes('deep learning')) {
    return 'ai';
  }
  
  return 'general';
};

// Extract tags from content
const extractTags = (title: string, description?: string): string[] => {
  const content = `${title} ${description || ''}`.toLowerCase();
  const tags: string[] = [];
  
  const allKeywords = [...AI_KEYWORDS.high, ...AI_KEYWORDS.medium, ...AI_KEYWORDS.low];
  allKeywords.forEach(keyword => {
    if (content.includes(keyword) && !tags.includes(keyword)) {
      tags.push(keyword);
    }
  });
  
  return tags.slice(0, 5); // Limit to 5 tags
};

// Filter articles by time range
const isWithinTimeRange = (publishedAt: Date, timeRange: string): boolean => {
  const now = new Date();
  const diffMs = now.getTime() - publishedAt.getTime();
  
  switch (timeRange) {
    case 'day':
      return diffMs <= 24 * 60 * 60 * 1000;
    case 'week':
      return diffMs <= 7 * 24 * 60 * 60 * 1000;
    case 'month':
      return diffMs <= 30 * 24 * 60 * 60 * 1000;
    default:
      return true;
  }
};

// Cache management
const cache = new Map<string, { data: AINewsItem[]; timestamp: number; expiry: number }>();
const CACHE_DURATION = 45 * 60 * 1000; // 45 minutes

export const useAINews = (options: UseAINewsOptions): UseAINewsReturn => {
  const { settings, enabled, cacheKey } = options;
  const [articles, setArticles] = useState<AINewsItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const [bookmarkedArticles, setBookmarkedArticles] = useState<Set<string>>(new Set());
  
  const abortController = useRef<AbortController | null>(null);

  // Load bookmarks from localStorage
  useEffect(() => {
    const savedBookmarks = localStorage.getItem('ai-news-bookmarks');
    if (savedBookmarks) {
      try {
        const bookmarks = JSON.parse(savedBookmarks);
        setBookmarkedArticles(new Set(bookmarks));
      } catch (error) {
        console.error('Failed to load bookmarks:', error);
      }
    }
  }, []);

  // Save bookmarks to localStorage
  const saveBookmarks = useCallback((bookmarks: Set<string>) => {
    localStorage.setItem('ai-news-bookmarks', JSON.stringify([...bookmarks]));
  }, []);

  // Fetch Hacker News AI-related stories
  const fetchHackerNewsData = async (): Promise<AINewsItem[]> => {
    try {
      // Search for AI-related stories
      const searchQueries = ['artificial intelligence', 'machine learning', 'AI', 'LLM', 'GPT', 'neural network'];
      const allArticles: AINewsItem[] = [];
      
      for (const query of searchQueries) {
        try {
          const searchUrl = `https://hn.algolia.com/api/v1/search?query=${encodeURIComponent(query)}&tags=story&hitsPerPage=20&numericFilters=created_at_i>${Math.floor((Date.now() - 7 * 24 * 60 * 60 * 1000) / 1000)}`;
          const response = await invoke<string>('fetch_url', { url: searchUrl });
          const data = JSON.parse(response);
          
          if (data.hits) {
            const articles = data.hits
              .filter((item: any) => {
                const relevanceScore = calculateRelevanceScore(item.title, item.story_text);
                return relevanceScore >= 10; // Only include highly relevant content
              })
              .map((item: any): AINewsItem => ({
                id: `hn-${item.objectID}`,
                title: item.title,
                description: item.story_text || '',
                url: item.url || `https://news.ycombinator.com/item?id=${item.objectID}`,
                source: 'Hacker News',
                publishedAt: new Date(item.created_at),
                category: categorizeContent(item.title, item.story_text),
                tags: extractTags(item.title, item.story_text),
                score: item.points || 0,
                isBookmarked: bookmarkedArticles.has(`hn-${item.objectID}`)
              }));
            
            allArticles.push(...articles);
          }
        } catch (error) {
          console.warn(`Failed to fetch HN data for query "${query}":`, error);
        }
      }
      
      return allArticles;
    } catch (error) {
      console.error('Failed to fetch Hacker News data:', error);
      return [];
    }
  };

  // Fetch ArXiv AI papers
  const fetchArxivData = async (): Promise<AINewsItem[]> => {
    try {
      const categories = ['cs.AI', 'cs.LG', 'cs.CL', 'cs.CV', 'cs.NE'];
      const allPapers: AINewsItem[] = [];
      
      for (const category of categories) {
        try {
          const query = `cat:${category}`;
          const url = `http://export.arxiv.org/api/query?search_query=${encodeURIComponent(query)}&start=0&max_results=20&sortBy=submittedDate&sortOrder=descending`;
          
          const response = await invoke<string>('fetch_url', { url });
          
          // Parse XML response (simplified - in real implementation you'd use a proper XML parser)
          const entries = response.split('<entry>').slice(1);
          
          const papers = entries.map((entry: string): AINewsItem | null => {
            try {
              const titleMatch = entry.match(/<title>(.*?)<\/title>/s);
              const summaryMatch = entry.match(/<summary>(.*?)<\/summary>/s);
              const publishedMatch = entry.match(/<published>(.*?)<\/published>/);
              const linkMatch = entry.match(/<id>(.*?)<\/id>/);
              
              if (!titleMatch || !summaryMatch || !publishedMatch || !linkMatch) {
                return null;
              }
              
              const title = titleMatch[1].trim();
              const summary = summaryMatch[1].trim().replace(/\s+/g, ' ');
              const published = new Date(publishedMatch[1]);
              const arxivId = linkMatch[1].split('/').pop() || '';
              
              return {
                id: `arxiv-${arxivId}`,
                title,
                description: summary,
                url: linkMatch[1],
                source: 'arXiv',
                publishedAt: published,
                category: categorizeContent(title, summary),
                tags: extractTags(title, summary),
                score: calculateRelevanceScore(title, summary),
                isBookmarked: bookmarkedArticles.has(`arxiv-${arxivId}`)
              };
            } catch (error) {
              console.warn('Failed to parse arXiv entry:', error);
              return null;
            }
          }).filter((paper: AINewsItem | null): paper is AINewsItem => paper !== null);
          
          allPapers.push(...papers);
        } catch (error) {
          console.warn(`Failed to fetch arXiv data for category "${category}":`, error);
        }
      }
      
      return allPapers;
    } catch (error) {
      console.error('Failed to fetch arXiv data:', error);
      return [];
    }
  };

  // Fetch RSS feeds (OpenAI, Anthropic, etc.)
  const fetchRSSData = async (): Promise<AINewsItem[]> => {
    const rssFeeds = [
      { url: 'https://openai.com/blog/rss.xml', source: 'OpenAI Blog' },
      { url: 'https://www.anthropic.com/news/rss.xml', source: 'Anthropic News' },
    ];
    
    const allArticles: AINewsItem[] = [];
    
    for (const feed of rssFeeds) {
      try {
        const response = await invoke<string>('fetch_url', { url: feed.url });
        
        // Parse RSS XML (simplified)
        const items = response.split('<item>').slice(1);
        
        const articles = items.map((item: string): AINewsItem | null => {
          try {
            const titleMatch = item.match(/<title><!\[CDATA\[(.*?)\]\]><\/title>/) || item.match(/<title>(.*?)<\/title>/);
            const descMatch = item.match(/<description><!\[CDATA\[(.*?)\]\]><\/description>/) || item.match(/<description>(.*?)<\/description>/);
            const linkMatch = item.match(/<link>(.*?)<\/link>/);
            const pubDateMatch = item.match(/<pubDate>(.*?)<\/pubDate>/);
            
            if (!titleMatch || !linkMatch || !pubDateMatch) {
              return null;
            }
            
            const title = titleMatch[1].trim();
            const description = descMatch ? descMatch[1].trim().replace(/<[^>]*>/g, '') : '';
            const url = linkMatch[1].trim();
            const publishedAt = new Date(pubDateMatch[1]);
            
            // Only include AI-relevant content
            const relevanceScore = calculateRelevanceScore(title, description);
            if (relevanceScore < 5) return null;
            
            return {
              id: `rss-${btoa(url).slice(0, 12)}`,
              title,
              description,
              url,
              source: feed.source,
              publishedAt,
              category: categorizeContent(title, description),
              tags: extractTags(title, description),
              score: relevanceScore,
              isBookmarked: bookmarkedArticles.has(`rss-${btoa(url).slice(0, 12)}`)
            };
          } catch (error) {
            console.warn('Failed to parse RSS item:', error);
            return null;
          }
        }).filter((article: AINewsItem | null): article is AINewsItem => article !== null);
        
        allArticles.push(...articles);
      } catch (error) {
        console.warn(`Failed to fetch RSS feed "${feed.source}":`, error);
      }
    }
    
    return allArticles;
  };

  // Main fetch function
  const fetchData = useCallback(async (): Promise<AINewsItem[]> => {
    const allArticles: AINewsItem[] = [];
    
    // Fetch from enabled sources
    if (settings.sources.includes('hackernews')) {
      const hnArticles = await fetchHackerNewsData();
      allArticles.push(...hnArticles);
    }
    
    if (settings.sources.includes('arxiv')) {
      const arxivArticles = await fetchArxivData();
      allArticles.push(...arxivArticles);
    }
    
    // Add RSS sources if they include AI blogs
    if (settings.sources.some(source => ['aiweekly', 'openai-blog'].includes(source))) {
      const rssArticles = await fetchRSSData();
      allArticles.push(...rssArticles);
    }
    
    // Filter and sort articles
    let filteredArticles = allArticles
      // Remove duplicates by URL
      .filter((article, index, self) => 
        index === self.findIndex(a => a.url === article.url)
      )
      // Filter by time range
      .filter(article => isWithinTimeRange(article.publishedAt, settings.timeRange))
      // Filter by categories
      .filter(article => 
        settings.categories.length === 0 || settings.categories.includes(article.category)
      )
      // Filter by keywords if specified
      .filter(article => {
        if (settings.keywords.length === 0) return true;
        const content = `${article.title} ${article.description}`.toLowerCase();
        return settings.keywords.some(keyword => 
          content.includes(keyword.toLowerCase())
        );
      })
      // Sort by relevance score and date
      .sort((a, b) => {
        const scoreA = (a.score || 0) + (a.publishedAt.getTime() / 1000000);
        const scoreB = (b.score || 0) + (b.publishedAt.getTime() / 1000000);
        return scoreB - scoreA;
      })
      // Limit results
      .slice(0, settings.itemLimit);
    
    return filteredArticles;
  }, [settings, bookmarkedArticles]);

  // Refresh function
  const refresh = useCallback(async () => {
    if (!enabled || isLoading) return;
    
    // Cancel previous request
    if (abortController.current) {
      abortController.current.abort();
    }
    abortController.current = new AbortController();
    
    setIsLoading(true);
    setError(null);
    
    try {
      // Check cache first
      const cached = cache.get(cacheKey);
      if (cached && Date.now() < cached.expiry) {
        setArticles(cached.data);
        setLastUpdated(new Date(cached.timestamp));
        setIsLoading(false);
        return;
      }
      
      const newArticles = await fetchData();
      
      // Cache the results
      cache.set(cacheKey, {
        data: newArticles,
        timestamp: Date.now(),
        expiry: Date.now() + CACHE_DURATION
      });
      
      setArticles(newArticles);
      setLastUpdated(new Date());
    } catch (error: any) {
      if (error.name !== 'AbortError') {
        console.error('Failed to fetch AI news:', error);
        setError(error.message || 'Failed to fetch AI news');
        
        // Fallback to cached data if available
        const cached = cache.get(cacheKey);
        if (cached) {
          setArticles(cached.data);
          setLastUpdated(new Date(cached.timestamp));
        }
      }
    } finally {
      setIsLoading(false);
    }
  }, [enabled, isLoading, cacheKey, fetchData]);

  // Bookmark management
  const bookmarkArticle = useCallback((articleId: string) => {
    const newBookmarks = new Set(bookmarkedArticles);
    newBookmarks.add(articleId);
    setBookmarkedArticles(newBookmarks);
    saveBookmarks(newBookmarks);
    
    // Update articles to reflect bookmark status
    setArticles(prev => prev.map(article => 
      article.id === articleId ? { ...article, isBookmarked: true } : article
    ));
  }, [bookmarkedArticles, saveBookmarks]);

  const removeBookmark = useCallback((articleId: string) => {
    const newBookmarks = new Set(bookmarkedArticles);
    newBookmarks.delete(articleId);
    setBookmarkedArticles(newBookmarks);
    saveBookmarks(newBookmarks);
    
    // Update articles to reflect bookmark status
    setArticles(prev => prev.map(article => 
      article.id === articleId ? { ...article, isBookmarked: false } : article
    ));
  }, [bookmarkedArticles, saveBookmarks]);

  // Search function
  const searchArticles = useCallback((query: string): AINewsItem[] => {
    if (!query.trim()) return articles;
    
    const searchTerm = query.toLowerCase();
    return articles.filter(article =>
      article.title.toLowerCase().includes(searchTerm) ||
      article.description.toLowerCase().includes(searchTerm) ||
      article.tags.some(tag => tag.toLowerCase().includes(searchTerm)) ||
      article.source.toLowerCase().includes(searchTerm)
    );
  }, [articles]);

  // Auto-refresh on settings change
  useEffect(() => {
    if (enabled) {
      refresh();
    }
  }, [enabled, settings.sources, settings.categories, settings.timeRange, settings.keywords]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortController.current) {
        abortController.current.abort();
      }
    };
  }, []);

  return {
    articles,
    isLoading,
    error,
    lastUpdated,
    refresh,
    bookmarkArticle,
    removeBookmark,
    searchArticles
  };
};