/**
 * Accessibility hook for Hub Dashboard WCAG 2.1 AA compliance
 */

import { useEffect, useCallback, useState } from 'react';
import { useHubStore } from '@/stores/hubStore';

export interface AccessibilityIssue {
  id: string;
  severity: 'error' | 'warning' | 'info';
  type: 'keyboard' | 'screen-reader' | 'color-contrast' | 'focus' | 'semantic';
  element?: HTMLElement;
  message: string;
  recommendation: string;
  wcagReference: string;
}

export interface AccessibilityMetrics {
  totalIssues: number;
  errorCount: number;
  warningCount: number;
  infoCount: number;
  complianceScore: number; // 0-100
  lastAudit: Date;
}

class AccessibilityAuditor {
  private issues: AccessibilityIssue[] = [];
  private observers: Set<(metrics: AccessibilityMetrics) => void> = new Set();

  // WCAG 2.1 AA Color contrast requirements
  private readonly MIN_CONTRAST_NORMAL = 4.5;
  private readonly MIN_CONTRAST_LARGE = 3.0;

  public auditPage(): AccessibilityIssue[] {
    this.issues = [];
    
    // Check keyboard navigation
    this.auditKeyboardNavigation();
    
    // Check color contrast
    this.auditColorContrast();
    
    // Check semantic HTML
    this.auditSemanticStructure();
    
    // Check ARIA attributes
    this.auditAriaLabels();
    
    // Check focus indicators
    this.auditFocusIndicators();
    
    // Check heading structure
    this.auditHeadingStructure();
    
    // Check alt text for images
    this.auditImageAltText();
    
    // Check form labels
    this.auditFormLabels();

    this.notifyObservers();
    return [...this.issues];
  }

  private auditKeyboardNavigation() {
    // Check for keyboard traps
    const interactiveElements = document.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );

    let hasKeyboardTrap = false;
    interactiveElements.forEach((element, index) => {
      const el = element as HTMLElement;
      
      // Check if element is focusable but has tabindex="-1"
      if (el.tabIndex === -1 && !el.hasAttribute('aria-hidden')) {
        this.addIssue({
          id: `keyboard-${index}`,
          severity: 'warning',
          type: 'keyboard',
          element: el,
          message: 'Interactive element is not keyboard accessible',
          recommendation: 'Remove tabindex="-1" or add proper keyboard event handlers',
          wcagReference: 'WCAG 2.1.1'
        });
      }

      // Check for missing keyboard event handlers on non-semantic elements
      if (el.tagName === 'DIV' && el.onclick && !el.onkeydown) {
        this.addIssue({
          id: `keyboard-handler-${index}`,
          severity: 'error',
          type: 'keyboard',
          element: el,
          message: 'Clickable element missing keyboard event handler',
          recommendation: 'Add onKeyDown handler or use semantic button element',
          wcagReference: 'WCAG 2.1.1'
        });
      }
    });

    // Check for skip links
    const skipLinks = document.querySelectorAll('a[href^="#"]');
    if (skipLinks.length === 0) {
      this.addIssue({
        id: 'skip-links',
        severity: 'warning',
        type: 'keyboard',
        message: 'No skip links found',
        recommendation: 'Add skip links for keyboard navigation',
        wcagReference: 'WCAG 2.4.1'
      });
    }
  }

  private auditColorContrast() {
    const elementsToCheck = document.querySelectorAll('*');
    
    elementsToCheck.forEach((element, index) => {
      const el = element as HTMLElement;
      const style = window.getComputedStyle(el);
      const textColor = style.color;
      const backgroundColor = style.backgroundColor;
      
      // Skip elements without text or transparent backgrounds
      if (!textColor || backgroundColor === 'rgba(0, 0, 0, 0)' || !el.textContent?.trim()) {
        return;
      }

      const contrast = this.calculateContrast(textColor, backgroundColor);
      const fontSize = parseFloat(style.fontSize);
      const fontWeight = style.fontWeight;
      
      // Determine if text is "large" (18pt+ or 14pt+ bold)
      const isLargeText = fontSize >= 18 || (fontSize >= 14 && (fontWeight === 'bold' || parseInt(fontWeight) >= 700));
      const minContrast = isLargeText ? this.MIN_CONTRAST_LARGE : this.MIN_CONTRAST_NORMAL;
      
      if (contrast < minContrast) {
        this.addIssue({
          id: `contrast-${index}`,
          severity: 'error',
          type: 'color-contrast',
          element: el,
          message: `Insufficient color contrast: ${contrast.toFixed(2)}:1 (minimum ${minContrast}:1)`,
          recommendation: 'Increase color contrast between text and background',
          wcagReference: 'WCAG 1.4.3'
        });
      }
    });
  }

  private auditSemanticStructure() {
    // Check for proper landmark usage
    const landmarks = document.querySelectorAll('main, nav, aside, section, article, header, footer');
    if (landmarks.length === 0) {
      this.addIssue({
        id: 'landmarks',
        severity: 'warning',
        type: 'semantic',
        message: 'No semantic landmarks found',
        recommendation: 'Use semantic HTML5 elements like main, nav, section, etc.',
        wcagReference: 'WCAG 1.3.1'
      });
    }

    // Check for multiple main elements
    const mainElements = document.querySelectorAll('main');
    if (mainElements.length > 1) {
      this.addIssue({
        id: 'multiple-main',
        severity: 'error',
        type: 'semantic',
        message: 'Multiple main elements found',
        recommendation: 'Use only one main element per page',
        wcagReference: 'WCAG 1.3.1'
      });
    }
  }

  private auditAriaLabels() {
    // Check buttons without accessible names
    const buttons = document.querySelectorAll('button');
    buttons.forEach((button, index) => {
      const hasText = button.textContent?.trim();
      const hasAriaLabel = button.getAttribute('aria-label');
      const hasAriaLabelledBy = button.getAttribute('aria-labelledby');
      
      if (!hasText && !hasAriaLabel && !hasAriaLabelledBy) {
        this.addIssue({
          id: `button-label-${index}`,
          severity: 'error',
          type: 'screen-reader',
          element: button as HTMLElement,
          message: 'Button has no accessible name',
          recommendation: 'Add text content, aria-label, or aria-labelledby attribute',
          wcagReference: 'WCAG 4.1.2'
        });
      }
    });

    // Check images without alt text
    const images = document.querySelectorAll('img');
    images.forEach((img, index) => {
      if (!img.hasAttribute('alt')) {
        this.addIssue({
          id: `img-alt-${index}`,
          severity: 'error',
          type: 'screen-reader',
          element: img as HTMLElement,
          message: 'Image missing alt attribute',
          recommendation: 'Add descriptive alt text or alt="" for decorative images',
          wcagReference: 'WCAG 1.1.1'
        });
      }
    });

    // Check for aria-hidden on focusable elements
    const hiddenFocusable = document.querySelectorAll('[aria-hidden="true"]:is(button, [href], input, select, textarea, [tabindex])');
    hiddenFocusable.forEach((element, index) => {
      this.addIssue({
        id: `hidden-focusable-${index}`,
        severity: 'error',
        type: 'screen-reader',
        element: element as HTMLElement,
        message: 'Focusable element is hidden from screen readers',
        recommendation: 'Remove aria-hidden or make element non-focusable',
        wcagReference: 'WCAG 4.1.2'
      });
    });
  }

  private auditFocusIndicators() {
    const focusableElements = document.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );

    focusableElements.forEach((element, index) => {
      const el = element as HTMLElement;
      const style = window.getComputedStyle(el, ':focus');
      
      // Check if focus indicator is visible
      const outlineStyle = style.outline;
      const outlineWidth = style.outlineWidth;
      const boxShadow = style.boxShadow;
      
      if (outlineStyle === 'none' && outlineWidth === '0px' && !boxShadow.includes('rgb')) {
        this.addIssue({
          id: `focus-indicator-${index}`,
          severity: 'warning',
          type: 'focus',
          element: el,
          message: 'Element lacks visible focus indicator',
          recommendation: 'Add CSS focus styles with sufficient contrast',
          wcagReference: 'WCAG 2.4.7'
        });
      }
    });
  }

  private auditHeadingStructure() {
    const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
    let previousLevel = 0;
    let hasH1 = false;

    headings.forEach((heading, index) => {
      const level = parseInt(heading.tagName[1]);
      
      if (level === 1) {
        hasH1 = true;
        if (document.querySelectorAll('h1').length > 1) {
          this.addIssue({
            id: `multiple-h1-${index}`,
            severity: 'warning',
            type: 'semantic',
            element: heading as HTMLElement,
            message: 'Multiple H1 elements found',
            recommendation: 'Use only one H1 per page',
            wcagReference: 'WCAG 1.3.1'
          });
        }
      }

      // Check for skipped heading levels
      if (previousLevel > 0 && level > previousLevel + 1) {
        this.addIssue({
          id: `heading-skip-${index}`,
          severity: 'warning',
          type: 'semantic',
          element: heading as HTMLElement,
          message: `Heading level skipped from H${previousLevel} to H${level}`,
          recommendation: 'Use consecutive heading levels without skipping',
          wcagReference: 'WCAG 1.3.1'
        });
      }

      previousLevel = level;
    });

    if (!hasH1 && headings.length > 0) {
      this.addIssue({
        id: 'missing-h1',
        severity: 'warning',
        type: 'semantic',
        message: 'Page missing H1 heading',
        recommendation: 'Add a main heading (H1) to the page',
        wcagReference: 'WCAG 1.3.1'
      });
    }
  }

  private auditImageAltText() {
    const images = document.querySelectorAll('img[alt]');
    images.forEach((img, index) => {
      const alt = img.getAttribute('alt');
      const src = img.getAttribute('src');
      
      // Check for redundant alt text
      if (alt && src && alt.toLowerCase().includes('image') || alt?.toLowerCase().includes('picture')) {
        this.addIssue({
          id: `redundant-alt-${index}`,
          severity: 'info',
          type: 'screen-reader',
          element: img as HTMLElement,
          message: 'Alt text contains redundant words like "image" or "picture"',
          recommendation: 'Remove redundant words from alt text',
          wcagReference: 'WCAG 1.1.1'
        });
      }

      // Check for very long alt text
      if (alt && alt.length > 125) {
        this.addIssue({
          id: `long-alt-${index}`,
          severity: 'warning',
          type: 'screen-reader',
          element: img as HTMLElement,
          message: 'Alt text is very long (over 125 characters)',
          recommendation: 'Consider using shorter alt text or longdesc attribute',
          wcagReference: 'WCAG 1.1.1'
        });
      }
    });
  }

  private auditFormLabels() {
    const inputs = document.querySelectorAll('input, select, textarea');
    inputs.forEach((input, index) => {
      const el = input as HTMLInputElement;
      
      // Skip hidden inputs
      if (el.type === 'hidden') return;
      
      const id = el.id;
      const hasLabel = id ? document.querySelector(`label[for="${id}"]`) : null;
      const hasAriaLabel = el.getAttribute('aria-label');
      const hasAriaLabelledBy = el.getAttribute('aria-labelledby');
      
      if (!hasLabel && !hasAriaLabel && !hasAriaLabelledBy) {
        this.addIssue({
          id: `form-label-${index}`,
          severity: 'error',
          type: 'screen-reader',
          element: el,
          message: 'Form control has no associated label',
          recommendation: 'Add a label element, aria-label, or aria-labelledby attribute',
          wcagReference: 'WCAG 1.3.1'
        });
      }
    });
  }

  private calculateContrast(foreground: string, background: string): number {
    // Simplified contrast calculation - in a real implementation,
    // you'd want to use a proper color contrast library
    const getLuminance = (color: string): number => {
      // Basic RGB extraction and luminance calculation
      const rgb = color.match(/\d+/g);
      if (!rgb || rgb.length < 3) return 0;
      
      const [r, g, b] = rgb.map(val => {
        const num = parseInt(val) / 255;
        return num <= 0.03928 ? num / 12.92 : Math.pow((num + 0.055) / 1.055, 2.4);
      });
      
      return 0.2126 * r + 0.7152 * g + 0.0722 * b;
    };

    const l1 = getLuminance(foreground);
    const l2 = getLuminance(background);
    const lighter = Math.max(l1, l2);
    const darker = Math.min(l1, l2);
    
    return (lighter + 0.05) / (darker + 0.05);
  }

  private addIssue(issue: Omit<AccessibilityIssue, 'id'> & { id: string }) {
    this.issues.push(issue);
  }

  private notifyObservers() {
    const metrics = this.getMetrics();
    this.observers.forEach(observer => observer(metrics));
  }

  public getMetrics(): AccessibilityMetrics {
    const errorCount = this.issues.filter(i => i.severity === 'error').length;
    const warningCount = this.issues.filter(i => i.severity === 'warning').length;
    const infoCount = this.issues.filter(i => i.severity === 'info').length;
    
    // Calculate compliance score (100 - penalties)
    const errorPenalty = errorCount * 20; // Each error reduces score by 20
    const warningPenalty = warningCount * 5; // Each warning reduces score by 5
    const infoPenalty = infoCount * 1; // Each info reduces score by 1
    
    const complianceScore = Math.max(0, 100 - errorPenalty - warningPenalty - infoPenalty);

    return {
      totalIssues: this.issues.length,
      errorCount,
      warningCount,
      infoCount,
      complianceScore,
      lastAudit: new Date()
    };
  }

  public getIssues(): AccessibilityIssue[] {
    return [...this.issues];
  }

  public subscribe(callback: (metrics: AccessibilityMetrics) => void) {
    this.observers.add(callback);
    return () => this.observers.delete(callback);
  }
}

// Singleton instance
const accessibilityAuditor = new AccessibilityAuditor();

// React hook
export const useAccessibility = () => {
  const [metrics, setMetrics] = useState<AccessibilityMetrics>({
    totalIssues: 0,
    errorCount: 0,
    warningCount: 0,
    infoCount: 0,
    complianceScore: 100,
    lastAudit: new Date()
  });
  const [issues, setIssues] = useState<AccessibilityIssue[]>([]);
  
  const { settings } = useHubStore();

  const runAudit = useCallback(() => {
    const foundIssues = accessibilityAuditor.auditPage();
    setIssues(foundIssues);
    setMetrics(accessibilityAuditor.getMetrics());
  }, []);

  const fixIssue = useCallback((issueId: string) => {
    // In a real implementation, this would attempt to auto-fix certain issues
    console.log(`Attempting to fix issue: ${issueId}`);
  }, []);

  const generateReport = useCallback(() => {
    const currentMetrics = accessibilityAuditor.getMetrics();
    const currentIssues = accessibilityAuditor.getIssues();
    
    const report = {
      timestamp: new Date().toISOString(),
      url: window.location.href,
      metrics: currentMetrics,
      issues: currentIssues,
      recommendations: currentIssues.map(issue => issue.recommendation),
      wcagReferences: [...new Set(currentIssues.map(issue => issue.wcagReference))]
    };

    // Download report as JSON
    const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `accessibility-report-${Date.now()}.json`;
    a.click();
    URL.revokeObjectURL(url);
  }, []);

  // Auto-audit when accessibility features are enabled
  useEffect(() => {
    if (settings.accessibility.screenReaderSupport || settings.accessibility.keyboardNavigation) {
      const unsubscribe = accessibilityAuditor.subscribe(setMetrics);
      
      // Run initial audit
      runAudit();
      
      // Set up periodic audits
      const interval = setInterval(runAudit, 30000); // Every 30 seconds
      
      return () => {
        unsubscribe();
        clearInterval(interval);
      };
    }
  }, [settings.accessibility, runAudit]);

  // Listen for DOM changes and re-audit
  useEffect(() => {
    if (!settings.accessibility.screenReaderSupport) return;

    const observer = new MutationObserver(() => {
      // Debounce re-audits
      setTimeout(runAudit, 1000);
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['aria-label', 'aria-labelledby', 'aria-hidden', 'alt', 'id']
    });

    return () => observer.disconnect();
  }, [settings.accessibility.screenReaderSupport, runAudit]);

  return {
    metrics,
    issues,
    runAudit,
    fixIssue,
    generateReport
  };
};

export default accessibilityAuditor;