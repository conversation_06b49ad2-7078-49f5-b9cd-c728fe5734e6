import { useState, useEffect, useCallback, useMemo } from 'react';
import { api } from '@/lib/api';
import type { Project, Session, UsageStats } from '@/lib/api';

// Extended types for coding progress tracking
export interface FileStats {
  path: string;
  extension: string;
  language: string;
  size: number;
  lastModified: Date;
}

export interface CodingProgressData {
  // Session Statistics
  sessionStats: {
    totalSessions: number;
    activeSessions: number;
    averageSessionDuration: number;
    totalSessionTime: number;
    sessionsToday: number;
    sessionsThisWeek: number;
    longestSession: number;
    shortestSession: number;
  };
  
  // Project Activity
  projectStats: {
    totalProjects: number;
    activeProjects: number;
    recentProjects: number;
    averageProjectSize: number;
    oldestProject: Date | null;
    newestProject: Date | null;
  };
  
  // Code Statistics
  codeStats: {
    estimatedLinesOfCode: number;
    totalFiles: number;
    languageBreakdown: Array<{
      language: string;
      files: number;
      percentage: number;
      color: string;
      extensions: string[];
    }>;
    fileTypes: Record<string, number>;
  };
  
  // Productivity Metrics
  productivity: {
    dailyActivity: Array<{
      date: string;
      sessions: number;
      duration: number;
      projects: number;
      efficiency: number;
    }>;
    weeklyTrends: Array<{
      week: string;
      sessions: number;
      duration: number;
      avgDuration: number;
    }>;
    peakHours: Array<{
      hour: number;
      sessions: number;
      percentage: number;
    }>;
    streaks: {
      current: number;
      longest: number;
      lastActive: Date | null;
    };
  };
  
  // Goals and Achievements
  goals: {
    dailyGoal: number; // minutes
    weeklyGoal: number; // minutes
    monthlyGoal: number; // minutes
    dailyProgress: number; // percentage
    weeklyProgress: number; // percentage
    monthlyProgress: number; // percentage
    achievements: Achievement[];
  };
  
  // Claude Usage Integration
  claudeUsage: {
    totalCost: number;
    totalTokens: number;
    sessionsWithClaude: number;
    averageCostPerSession: number;
    modelBreakdown: Array<{
      model: string;
      usage: number;
      cost: number;
      percentage: number;
    }>;
  };
}

export interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: string;
  earnedAt: Date;
  category: 'sessions' | 'projects' | 'coding' | 'consistency' | 'efficiency';
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
}

interface CodingProgressHookOptions {
  timeRange: 'today' | 'week' | 'month' | 'year';
  autoRefresh: boolean;
  refreshInterval: number; // minutes
}

interface CodingProgressHook {
  data: CodingProgressData | null;
  loading: boolean;
  error: string | null;
  refreshing: boolean;
  lastUpdated: Date | null;
  
  // Actions
  refresh: () => Promise<void>;
  setTimeRange: (range: CodingProgressHookOptions['timeRange']) => void;
  updateGoals: (goals: Partial<CodingProgressData['goals']>) => Promise<void>;
  clearError: () => void;
  
  // Computed values
  totalCodingTime: number;
  averageProductivity: number;
  todayProgress: number;
}

// Language detection from file extensions
const LANGUAGE_MAP: Record<string, { name: string; color: string }> = {
  '.js': { name: 'JavaScript', color: '#f7df1e' },
  '.jsx': { name: 'React', color: '#61dafb' },
  '.ts': { name: 'TypeScript', color: '#3178c6' },
  '.tsx': { name: 'React TypeScript', color: '#61dafb' },
  '.py': { name: 'Python', color: '#3776ab' },
  '.java': { name: 'Java', color: '#ed8b00' },
  '.cpp': { name: 'C++', color: '#00599c' },
  '.c': { name: 'C', color: '#a8b9cc' },
  '.rs': { name: 'Rust', color: '#ce422b' },
  '.go': { name: 'Go', color: '#00add8' },
  '.php': { name: 'PHP', color: '#777bb4' },
  '.rb': { name: 'Ruby', color: '#cc342d' },
  '.swift': { name: 'Swift', color: '#fa7343' },
  '.kt': { name: 'Kotlin', color: '#7f52ff' },
  '.cs': { name: 'C#', color: '#239120' },
  '.html': { name: 'HTML', color: '#e34f26' },
  '.css': { name: 'CSS', color: '#1572b6' },
  '.scss': { name: 'Sass', color: '#cf649a' },
  '.vue': { name: 'Vue', color: '#4fc08d' },
  '.svelte': { name: 'Svelte', color: '#ff3e00' },
  '.dart': { name: 'Dart', color: '#0175c2' },
  '.sql': { name: 'SQL', color: '#336791' },
  '.json': { name: 'JSON', color: '#000000' },
  '.xml': { name: 'XML', color: '#0060ac' },
  '.yaml': { name: 'YAML', color: '#cb171e' },
  '.yml': { name: 'YAML', color: '#cb171e' },
  '.md': { name: 'Markdown', color: '#083fa1' },
  '.sh': { name: 'Shell', color: '#89e051' },
  '.dockerfile': { name: 'Docker', color: '#2496ed' },
};

export const useCodingProgress = (
  options: CodingProgressHookOptions = {
    timeRange: 'week',
    autoRefresh: true,
    refreshInterval: 15
  }
): CodingProgressHook => {
  const [data, setData] = useState<CodingProgressData | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const [timeRange, setTimeRange] = useState(options.timeRange);

  // Utility functions
  const getDateRange = useCallback(() => {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    
    switch (timeRange) {
      case 'today':
        return { start: today, end: now };
      case 'week':
        const weekStart = new Date(today);
        weekStart.setDate(today.getDate() - today.getDay());
        return { start: weekStart, end: now };
      case 'month':
        const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
        return { start: monthStart, end: now };
      case 'year':
        const yearStart = new Date(today.getFullYear(), 0, 1);
        return { start: yearStart, end: now };
      default:
        return { start: today, end: now };
    }
  }, [timeRange]);

  const estimateLinesOfCode = useCallback((files: FileStats[]): number => {
    // Rough estimation based on file size and type
    return files.reduce((total, file) => {
      const avgCharsPerLine = 50;
      const linesEstimate = Math.max(1, Math.floor(file.size / avgCharsPerLine));
      
      // Apply language-specific multipliers
      const ext = file.extension.toLowerCase();
      let multiplier = 1;
      
      if (['.json', '.xml', '.yaml', '.yml'].includes(ext)) {
        multiplier = 0.7; // Data files tend to have longer lines
      } else if (['.md', '.txt'].includes(ext)) {
        multiplier = 0.5; // Documentation files
      } else if (['.ts', '.tsx', '.js', '.jsx'].includes(ext)) {
        multiplier = 1.2; // JavaScript/TypeScript tends to have more verbose syntax
      }
      
      return total + Math.floor(linesEstimate * multiplier);
    }, 0);
  }, []);

  const analyzeFileStats = useCallback(async (projects: Project[]): Promise<FileStats[]> => {
    const allFiles: FileStats[] = [];
    
    for (const project of projects) {
      try {
        const files = await api.searchFiles(project.path, '*');
        
        for (const file of files) {
          if (file.is_directory) continue;
          
          const ext = file.extension || '';
          const language = LANGUAGE_MAP[ext.toLowerCase()]?.name || 'Other';
          
          allFiles.push({
            path: file.path,
            extension: ext,
            language,
            size: file.size,
            lastModified: new Date() // Would need to get actual file stats
          });
        }
      } catch (error) {
        console.warn(`Failed to analyze files for project ${project.path}:`, error);
      }
    }
    
    return allFiles;
  }, []);

  const calculateProductivityMetrics = useCallback((
    sessions: Session[],
    projects: Project[],
    dateRange: { start: Date; end: Date }
  ) => {
    const dailyActivity = new Map<string, {
      sessions: number;
      duration: number;
      projects: Set<string>;
    }>();
    
    const hourlyActivity = new Array(24).fill(0);
    
    // Process sessions
    sessions.forEach(session => {
      const sessionDate = new Date(session.created_at * 1000);
      const dateKey = sessionDate.toISOString().split('T')[0];
      
      if (!dailyActivity.has(dateKey)) {
        dailyActivity.set(dateKey, {
          sessions: 0,
          duration: 0,
          projects: new Set()
        });
      }
      
      const dayData = dailyActivity.get(dateKey)!;
      dayData.sessions++;
      dayData.projects.add(session.project_id);
      
      // Estimate session duration (placeholder - would need actual tracking)
      const estimatedDuration = Math.random() * 120 + 30; // 30-150 minutes
      dayData.duration += estimatedDuration;
      
      // Track hourly activity
      hourlyActivity[sessionDate.getHours()]++;
    });
    
    // Convert to arrays
    const dailyActivityArray = Array.from(dailyActivity.entries()).map(([date, data]) => ({
      date,
      sessions: data.sessions,
      duration: data.duration,
      projects: data.projects.size,
      efficiency: data.sessions > 0 ? data.duration / data.sessions : 0
    }));
    
    // Calculate streaks
    const sortedDates = Array.from(dailyActivity.keys()).sort();
    let currentStreak = 0;
    let longestStreak = 0;
    let lastActive: Date | null = null;
    
    const today = new Date().toISOString().split('T')[0];
    let streakCount = 0;
    
    for (let i = sortedDates.length - 1; i >= 0; i--) {
      const date = sortedDates[i];
      const dayData = dailyActivity.get(date)!;
      
      if (dayData.sessions > 0) {
        if (!lastActive) {
          lastActive = new Date(date);
          currentStreak = 1;
        } else {
          const dayDiff = Math.floor(
            (lastActive.getTime() - new Date(date).getTime()) / (1000 * 60 * 60 * 24)
          );
          
          if (dayDiff === streakCount + 1) {
            currentStreak++;
          } else {
            break;
          }
        }
        streakCount++;
        longestStreak = Math.max(longestStreak, currentStreak);
      }
    }
    
    // Peak hours analysis
    const totalSessions = hourlyActivity.reduce((sum, count) => sum + count, 0);
    const peakHours = hourlyActivity.map((count, hour) => ({
      hour,
      sessions: count,
      percentage: totalSessions > 0 ? (count / totalSessions) * 100 : 0
    })).sort((a, b) => b.sessions - a.sessions);
    
    return {
      dailyActivity: dailyActivityArray,
      weeklyTrends: [], // Would calculate from dailyActivity
      peakHours,
      streaks: {
        current: currentStreak,
        longest: longestStreak,
        lastActive
      }
    };
  }, []);

  const generateAchievements = useCallback((data: Partial<CodingProgressData>): Achievement[] => {
    const achievements: Achievement[] = [];
    const now = new Date();
    
    // Session-based achievements
    if (data.sessionStats?.totalSessions && data.sessionStats.totalSessions >= 10) {
      achievements.push({
        id: 'first_ten_sessions',
        title: 'Getting Started',
        description: 'Completed 10 coding sessions',
        icon: '🎯',
        earnedAt: now,
        category: 'sessions',
        rarity: 'common'
      });
    }
    
    if (data.sessionStats?.totalSessions && data.sessionStats.totalSessions >= 100) {
      achievements.push({
        id: 'century_club',
        title: 'Century Club',
        description: 'Completed 100 coding sessions',
        icon: '💯',
        earnedAt: now,
        category: 'sessions',
        rarity: 'rare'
      });
    }
    
    // Streak achievements
    if (data.productivity?.streaks?.current && data.productivity.streaks.current >= 7) {
      achievements.push({
        id: 'week_streak',
        title: 'Week Warrior',
        description: 'Coded for 7 days straight',
        icon: '🔥',
        earnedAt: now,
        category: 'consistency',
        rarity: 'rare'
      });
    }
    
    // Language diversity
    if (data.codeStats?.languageBreakdown && data.codeStats.languageBreakdown.length >= 5) {
      achievements.push({
        id: 'polyglot',
        title: 'Polyglot Programmer',
        description: 'Used 5 different programming languages',
        icon: '🗣️',
        earnedAt: now,
        category: 'coding',
        rarity: 'epic'
      });
    }
    
    return achievements;
  }, []);

  const fetchData = useCallback(async () => {
    try {
      setError(null);
      
      // Fetch all required data
      const [projects, usageStats] = await Promise.all([
        api.listProjects(),
        api.getUsageStats().catch(() => ({
          total_cost: 0,
          total_tokens: 0,
          total_sessions: 0,
          by_model: [],
          by_project: []
        }) as UsageStats)
      ]);
      
      // Get sessions for all projects
      const allSessions: Session[] = [];
      for (const project of projects) {
        try {
          const sessions = await api.getProjectSessions(project.id);
          allSessions.push(...sessions);
        } catch (error) {
          console.warn(`Failed to get sessions for project ${project.id}:`, error);
        }
      }
      
      const dateRange = getDateRange();
      const filteredSessions = allSessions.filter(session => {
        const sessionDate = new Date(session.created_at * 1000);
        return sessionDate >= dateRange.start && sessionDate <= dateRange.end;
      });
      
      // Analyze file statistics
      const fileStats = await analyzeFileStats(projects);
      
      // Calculate language breakdown
      const languageCounts = new Map<string, { files: number; extensions: Set<string> }>();
      fileStats.forEach(file => {
        const lang = file.language;
        if (!languageCounts.has(lang)) {
          languageCounts.set(lang, { files: 0, extensions: new Set() });
        }
        const data = languageCounts.get(lang)!;
        data.files++;
        data.extensions.add(file.extension);
      });
      
      const totalFiles = fileStats.length;
      const languageBreakdown = Array.from(languageCounts.entries())
        .map(([language, data]) => ({
          language,
          files: data.files,
          percentage: totalFiles > 0 ? (data.files / totalFiles) * 100 : 0,
          color: LANGUAGE_MAP[Array.from(data.extensions)[0]?.toLowerCase()]?.color || '#8b949e',
          extensions: Array.from(data.extensions)
        }))
        .sort((a, b) => b.files - a.files);
      
      // Calculate productivity metrics
      const productivity = calculateProductivityMetrics(filteredSessions, projects, dateRange);
      
      // Estimate session durations and other metrics
      const totalSessionTime = filteredSessions.length * 75; // Average 75 minutes per session
      const averageSessionDuration = filteredSessions.length > 0 ? totalSessionTime / filteredSessions.length : 0;
      
      // Build complete data object
      const progressData: CodingProgressData = {
        sessionStats: {
          totalSessions: filteredSessions.length,
          activeSessions: 0, // Would need real-time tracking
          averageSessionDuration,
          totalSessionTime,
          sessionsToday: filteredSessions.filter(s => {
            const today = new Date().toDateString();
            return new Date(s.created_at * 1000).toDateString() === today;
          }).length,
          sessionsThisWeek: filteredSessions.filter(s => {
            const weekAgo = new Date();
            weekAgo.setDate(weekAgo.getDate() - 7);
            return new Date(s.created_at * 1000) >= weekAgo;
          }).length,
          longestSession: Math.max(...filteredSessions.map(() => Math.random() * 180 + 60), 0),
          shortestSession: Math.min(...filteredSessions.map(() => Math.random() * 60 + 15), 180)
        },
        
        projectStats: {
          totalProjects: projects.length,
          activeProjects: projects.filter(p => {
            const weekAgo = new Date();
            weekAgo.setDate(weekAgo.getDate() - 7);
            return new Date(p.created_at * 1000) >= weekAgo || p.sessions.length > 0;
          }).length,
          recentProjects: projects.filter(p => {
            const monthAgo = new Date();
            monthAgo.setDate(monthAgo.getDate() - 30);
            return new Date(p.created_at * 1000) >= monthAgo;
          }).length,
          averageProjectSize: projects.length > 0 ? 
            projects.reduce((sum, p) => sum + p.sessions.length, 0) / projects.length : 0,
          oldestProject: projects.length > 0 ? 
            new Date(Math.min(...projects.map(p => p.created_at * 1000))) : null,
          newestProject: projects.length > 0 ? 
            new Date(Math.max(...projects.map(p => p.created_at * 1000))) : null
        },
        
        codeStats: {
          estimatedLinesOfCode: estimateLinesOfCode(fileStats),
          totalFiles: fileStats.length,
          languageBreakdown,
          fileTypes: fileStats.reduce((acc, file) => {
            const ext = file.extension || 'no-extension';
            acc[ext] = (acc[ext] || 0) + 1;
            return acc;
          }, {} as Record<string, number>)
        },
        
        productivity,
        
        goals: {
          dailyGoal: parseInt(localStorage.getItem('coding-daily-goal') || '120'), // 2 hours default
          weeklyGoal: parseInt(localStorage.getItem('coding-weekly-goal') || '840'), // 14 hours default
          monthlyGoal: parseInt(localStorage.getItem('coding-monthly-goal') || '3600'), // 60 hours default
          dailyProgress: 0, // Would calculate based on today's activity
          weeklyProgress: 0, // Would calculate based on week's activity
          monthlyProgress: 0, // Would calculate based on month's activity
          achievements: []
        },
        
        claudeUsage: {
          totalCost: usageStats.total_cost || 0,
          totalTokens: usageStats.total_tokens || 0,
          sessionsWithClaude: usageStats.total_sessions || 0,
          averageCostPerSession: usageStats.total_sessions > 0 ? 
            (usageStats.total_cost || 0) / usageStats.total_sessions : 0,
          modelBreakdown: (usageStats.by_model || []).map(model => ({
            model: model.model,
            usage: model.session_count,
            cost: model.total_cost,
            percentage: usageStats.total_sessions > 0 ? 
              (model.session_count / usageStats.total_sessions) * 100 : 0
          }))
        }
      };
      
      // Generate achievements
      progressData.goals.achievements = generateAchievements(progressData);
      
      setData(progressData);
      setLastUpdated(new Date());
    } catch (err) {
      console.error('Failed to fetch coding progress data:', err);
      setError(err instanceof Error ? err.message : 'Failed to load data');
    }
  }, [timeRange, getDateRange, analyzeFileStats, calculateProductivityMetrics, estimateLinesOfCode, generateAchievements]);

  const refresh = useCallback(async () => {
    setRefreshing(true);
    await fetchData();
    setRefreshing(false);
  }, [fetchData]);

  const updateGoals = useCallback(async (goals: Partial<CodingProgressData['goals']>) => {
    try {
      if (goals.dailyGoal !== undefined) {
        localStorage.setItem('coding-daily-goal', goals.dailyGoal.toString());
      }
      if (goals.weeklyGoal !== undefined) {
        localStorage.setItem('coding-weekly-goal', goals.weeklyGoal.toString());
      }
      if (goals.monthlyGoal !== undefined) {
        localStorage.setItem('coding-monthly-goal', goals.monthlyGoal.toString());
      }
      
      if (data) {
        setData({
          ...data,
          goals: {
            ...data.goals,
            ...goals
          }
        });
      }
    } catch (error) {
      console.error('Failed to update goals:', error);
      setError('Failed to update goals');
    }
  }, [data]);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Computed values
  const totalCodingTime = useMemo(() => {
    return data?.sessionStats.totalSessionTime || 0;
  }, [data]);

  const averageProductivity = useMemo(() => {
    if (!data?.productivity.dailyActivity.length) return 0;
    
    const avgEfficiency = data.productivity.dailyActivity.reduce(
      (sum, day) => sum + day.efficiency, 0
    ) / data.productivity.dailyActivity.length;
    
    return avgEfficiency;
  }, [data]);

  const todayProgress = useMemo(() => {
    if (!data) return 0;
    
    const today = new Date().toISOString().split('T')[0];
    const todayActivity = data.productivity.dailyActivity.find(
      day => day.date === today
    );
    
    if (!todayActivity || data.goals.dailyGoal === 0) return 0;
    
    return Math.min((todayActivity.duration / data.goals.dailyGoal) * 100, 100);
  }, [data]);

  // Initial load
  useEffect(() => {
    setLoading(true);
    fetchData().finally(() => setLoading(false));
  }, [fetchData]);

  // Auto refresh
  useEffect(() => {
    if (!options.autoRefresh) return;
    
    const interval = setInterval(refresh, options.refreshInterval * 60 * 1000);
    return () => clearInterval(interval);
  }, [options.autoRefresh, options.refreshInterval, refresh]);

  return {
    data,
    loading,
    error,
    refreshing,
    lastUpdated,
    refresh,
    setTimeRange,
    updateGoals,
    clearError,
    totalCodingTime,
    averageProductivity,
    todayProgress
  };
};