import { useCallback, useEffect, useState } from 'react';
import { useHubStore } from '@/stores/hubStore';
import type { HubSettings, LayoutPreset } from '@/types/hub';

interface UseHubSettingsReturn {
  settings: HubSettings;
  updateSettings: (updates: Partial<HubSettings>) => void;
  resetSettings: () => void;
  exportSettings: () => Promise<void>;
  importSettings: (file: File) => Promise<boolean>;
  
  // Layout presets
  createLayoutPreset: (name: string, description: string) => void;
  applyLayoutPreset: (presetId: string) => void;
  deleteLayoutPreset: (presetId: string) => void;
  
  // Backup and restore
  createBackup: () => Promise<void>;
  restoreFromBackup: (backup: any) => Promise<boolean>;
  
  // Validation
  validateSettings: (settings: Partial<HubSettings>) => { isValid: boolean; errors: string[] };
}

export const useHubSettings = (): UseHubSettingsReturn => {
  const { settings, updateSettings: storeUpdateSettings, widgets } = useHubStore();
  const [isExporting, setIsExporting] = useState(false);
  const [isImporting, setIsImporting] = useState(false);

  const updateSettings = useCallback((updates: Partial<HubSettings>) => {
    // Validate settings before applying
    const validation = validateSettings(updates);
    if (!validation.isValid) {
      console.warn('Invalid settings update:', validation.errors);
      return;
    }

    storeUpdateSettings(updates);
  }, [storeUpdateSettings]);

  const resetSettings = useCallback(() => {
    const { DEFAULT_HUB_SETTINGS } = require('@/types/hub');
    storeUpdateSettings(DEFAULT_HUB_SETTINGS);
  }, [storeUpdateSettings]);

  const exportSettings = useCallback(async () => {
    setIsExporting(true);
    try {
      const exportData = {
        version: '1.0.0',
        timestamp: new Date().toISOString(),
        settings,
        widgets: widgets.map(w => ({
          ...w,
          // Remove runtime data
          lastUpdated: undefined,
        })),
      };

      const blob = new Blob([JSON.stringify(exportData, null, 2)], { 
        type: 'application/json' 
      });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `claudia-hub-settings-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Failed to export settings:', error);
      throw error;
    } finally {
      setIsExporting(false);
    }
  }, [settings, widgets]);

  const importSettings = useCallback(async (file: File): Promise<boolean> => {
    setIsImporting(true);
    try {
      const text = await file.text();
      const importData = JSON.parse(text);

      // Validate import data structure
      if (!importData.settings || typeof importData.settings !== 'object') {
        throw new Error('Invalid settings file format');
      }

      // Apply imported settings
      storeUpdateSettings(importData.settings);

      // TODO: Optionally import widgets as well
      // if (importData.widgets && Array.isArray(importData.widgets)) {
      //   // Import widgets logic
      // }

      return true;
    } catch (error) {
      console.error('Failed to import settings:', error);
      return false;
    } finally {
      setIsImporting(false);
    }
  }, [storeUpdateSettings]);

  const createLayoutPreset = useCallback((name: string, description: string) => {
    const newPreset: LayoutPreset = {
      id: `preset-${Date.now()}`,
      name,
      description,
      layout: widgets.map(w => w.position),
      widgetTypes: widgets.map(w => w.type),
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    const updatedPresets = [...settings.layout.layoutPresets, newPreset];
    updateSettings({
      layout: {
        ...settings.layout,
        layoutPresets: updatedPresets,
      },
    });
  }, [settings.layout, widgets, updateSettings]);

  const applyLayoutPreset = useCallback((presetId: string) => {
    const preset = settings.layout.layoutPresets.find(p => p.id === presetId);
    if (!preset) return;

    // TODO: Apply the preset layout to current widgets
    // This would require updating widget positions
    updateSettings({
      layout: {
        ...settings.layout,
        currentPreset: presetId,
      },
    });
  }, [settings.layout, updateSettings]);

  const deleteLayoutPreset = useCallback((presetId: string) => {
    const updatedPresets = settings.layout.layoutPresets.filter(p => p.id !== presetId);
    updateSettings({
      layout: {
        ...settings.layout,
        layoutPresets: updatedPresets,
        currentPreset: settings.layout.currentPreset === presetId 
          ? undefined 
          : settings.layout.currentPreset,
      },
    });
  }, [settings.layout, updateSettings]);

  const createBackup = useCallback(async () => {
    if (!settings.advanced.backupSettings.autoBackup) return;

    const backupData = {
      version: '1.0.0',
      timestamp: new Date().toISOString(),
      type: 'auto-backup',
      settings,
      widgets,
    };

    // Store in localStorage with rotation
    const backupKey = `claudia-backup-${Date.now()}`;
    localStorage.setItem(backupKey, JSON.stringify(backupData));

    // Clean up old backups
    const allKeys = Object.keys(localStorage);
    const backupKeys = allKeys
      .filter(key => key.startsWith('claudia-backup-'))
      .sort()
      .reverse();

    const maxBackups = settings.advanced.backupSettings.maxBackups;
    const keysToDelete = backupKeys.slice(maxBackups);
    keysToDelete.forEach(key => localStorage.removeItem(key));
  }, [settings, widgets]);

  const restoreFromBackup = useCallback(async (backup: any): Promise<boolean> => {
    try {
      if (backup.settings) {
        storeUpdateSettings(backup.settings);
      }
      
      // TODO: Restore widgets if needed
      
      return true;
    } catch (error) {
      console.error('Failed to restore from backup:', error);
      return false;
    }
  }, [storeUpdateSettings]);

  const validateSettings = useCallback((settings: Partial<HubSettings>) => {
    const errors: string[] = [];

    // Validate refresh interval
    if (settings.refreshInterval !== undefined) {
      if (settings.refreshInterval < 5 || settings.refreshInterval > 1440) {
        errors.push('Refresh interval must be between 5 and 1440 minutes');
      }
    }

    // Validate cache size
    if (settings.performance?.cacheSize !== undefined) {
      if (settings.performance.cacheSize < 10 || settings.performance.cacheSize > 200) {
        errors.push('Cache size must be between 10 and 200 MB');
      }
    }

    // Validate theme mode
    if (settings.theme?.mode !== undefined) {
      const validModes = ['light', 'dark', 'auto'];
      if (!validModes.includes(settings.theme.mode)) {
        errors.push('Invalid theme mode');
      }
    }

    // Validate experience level
    if (settings.userProfile?.experienceLevel !== undefined) {
      const validLevels = ['beginner', 'intermediate', 'advanced', 'expert'];
      if (!validLevels.includes(settings.userProfile.experienceLevel)) {
        errors.push('Invalid experience level');
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }, []);

  // Auto-backup on settings change
  useEffect(() => {
    if (settings.advanced.backupSettings.autoBackup) {
      const interval = setInterval(() => {
        createBackup();
      }, 24 * 60 * 60 * 1000); // Daily

      return () => clearInterval(interval);
    }
  }, [settings.advanced.backupSettings.autoBackup, createBackup]);

  return {
    settings,
    updateSettings,
    resetSettings,
    exportSettings,
    importSettings,
    createLayoutPreset,
    applyLayoutPreset,
    deleteLayoutPreset,
    createBackup,
    restoreFromBackup,
    validateSettings,
  };
};