/**
 * Comprehensive test suite for the recommendation system
 * Run these tests to verify the system is working correctly
 */

import { describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import { recommendationAPI } from '../lib/recommendation-api';
import { featureDiscovery } from '../lib/feature-discovery';
import { useRecommendationTracking } from '../lib/recommendation-tracking';
import { usePreferenceLearning } from '../lib/preference-learning';
import { abTestingService } from '../lib/ab-testing';

// <PERSON><PERSON> invoke
const mockInvoke = jest.fn();
jest.mock('@tauri-apps/api/core', () => ({
  invoke: mockInvoke
}));

describe('Recommendation System Integration Tests', () => {
  beforeEach(() => {
    mockInvoke.mockClear();
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('Recommendation API', () => {
    it('should generate user profile hash', async () => {
      mockInvoke.mockResolvedValue('profile_abc123');
      
      const profile = await recommendationAPI.getUserProfile();
      
      expect(profile).toBe('profile_abc123');
      expect(mockInvoke).toHaveBeenCalledWith('generate_user_profile', {
        userSessionHash: expect.any(String),
        lookbackDays: 30
      });
    });

    it('should get content-based recommendations', async () => {
      const mockRecommendations = [
        {
          userProfileHash: 'test-hash',
          recommendationType: 'agent',
          itemId: 'test-agent',
          relevanceScore: 0.8,
          confidenceScore: 0.7,
          priorityScore: 0.85
        }
      ];
      
      mockInvoke.mockResolvedValue(mockRecommendations);
      
      const recommendations = await recommendationAPI.getRecommendations('agent', {
        algorithm: 'content_based',
        limit: 5
      });
      
      expect(recommendations).toHaveLength(1);
      expect(recommendations[0].itemId).toBe('test-agent');
      expect(mockInvoke).toHaveBeenCalledWith('get_content_based_recommendations', {
        userProfileHash: expect.any(String),
        projectContext: null,
        limit: 5
      });
    });

    it('should handle recommendation feedback', async () => {
      mockInvoke.mockResolvedValue(undefined);
      
      await recommendationAPI.submitFeedback({
        recommendationId: 123,
        action: 'adopted',
        rating: 5,
        context: { source: 'test' }
      });
      
      expect(mockInvoke).toHaveBeenCalledWith('update_recommendation_feedback', {
        recommendationId: 123,
        action: 'adopted',
        rating: 5,
        feedbackText: undefined,
        context: { source: 'test' }
      });
    });
  });

  describe('Feature Discovery', () => {
    it('should return all available features', () => {
      const features = featureDiscovery.getAllFeatures();
      
      expect(features.length).toBeGreaterThan(0);
      expect(features[0]).toHaveProperty('id');
      expect(features[0]).toHaveProperty('name');
      expect(features[0]).toHaveProperty('description');
      expect(features[0]).toHaveProperty('category');
    });

    it('should get feature by id', () => {
      const feature = featureDiscovery.getFeature('session_templates');
      
      expect(feature).toBeDefined();
      expect(feature?.id).toBe('session_templates');
      expect(feature?.name).toBe('Session Templates');
      expect(feature?.category).toBe('productivity');
    });

    it('should generate contextual suggestions', async () => {
      mockInvoke.mockResolvedValue([]);
      
      const suggestions = await featureDiscovery.getContextualSuggestions(
        'session_start',
        { projectType: 'javascript', sessionCount: 5 }
      );
      
      expect(Array.isArray(suggestions)).toBe(true);
    });

    it('should check if feature should be nudged', async () => {
      mockInvoke.mockResolvedValue({
        featureId: 'session_templates',
        totalUses: 0,
        userAdopted: false
      });
      
      const shouldNudge = await featureDiscovery.shouldNudgeFeature(
        'session_templates',
        'test-user-hash',
        { projectType: 'javascript' }
      );
      
      expect(typeof shouldNudge).toBe('boolean');
    });
  });

  describe('Interaction Tracking', () => {
    it('should track user interactions', async () => {
      const tracking = useRecommendationTracking.getState();
      
      await tracking.trackInteraction({
        interactionType: 'agent_execute',
        featureId: 'test-agent',
        featureCategory: 'agent',
        successIndicator: true,
        durationSeconds: 30
      });
      
      expect(tracking.pendingInteractions.length).toBeGreaterThan(0);
    });

    it('should flush pending interactions', async () => {
      mockInvoke.mockResolvedValue(undefined);
      
      const tracking = useRecommendationTracking.getState();
      
      // Add some interactions
      await tracking.trackInteraction({
        interactionType: 'feature_use',
        featureId: 'test-feature',
        featureCategory: 'ui_feature'
      });
      
      await tracking.flushPendingInteractions();
      
      expect(mockInvoke).toHaveBeenCalledWith('bulk_insert_user_interactions', {
        interactions: expect.any(Array)
      });
    });

    it('should track recommendation feedback', async () => {
      mockInvoke.mockResolvedValue(undefined);
      
      const tracking = useRecommendationTracking.getState();
      
      await tracking.trackRecommendationFeedback({
        recommendationId: 456,
        action: 'clicked',
        context: { test: true }
      });
      
      expect(mockInvoke).toHaveBeenCalledWith('update_recommendation_feedback', {
        recommendationId: 456,
        action: 'clicked',
        rating: undefined,
        feedbackText: undefined,
        context: { test: true }
      });
    });
  });

  describe('Preference Learning', () => {
    it('should initialize user model', async () => {
      mockInvoke.mockResolvedValue(null); // No existing model
      
      const preferences = usePreferenceLearning.getState();
      await preferences.initializeModel('test-user-hash');
      
      expect(preferences.userModel).toBeDefined();
      expect(preferences.userModel?.userHash).toBe('test-user-hash');
    });

    it('should record preference signals', async () => {
      const preferences = usePreferenceLearning.getState();
      await preferences.initializeModel('test-user-hash');
      
      await preferences.recordSignal({
        signalType: 'explicit',
        action: 'liked',
        itemId: 'test-item',
        category: 'agent',
        strength: 1
      });
      
      expect(preferences.pendingSignals.length).toBeGreaterThan(0);
    });

    it('should adapt recommendations based on preferences', async () => {
      const preferences = usePreferenceLearning.getState();
      await preferences.initializeModel('test-user-hash');
      
      const recommendations = [
        {
          itemId: 'test-agent',
          recommendationType: 'agent',
          relevanceScore: 0.5,
          priorityScore: 0.5,
          confidenceScore: 0.8
        }
      ];
      
      const adapted = await preferences.adaptRecommendations(recommendations);
      
      expect(adapted).toHaveLength(1);
      expect(adapted[0].itemId).toBe('test-agent');
    });
  });

  describe('A/B Testing', () => {
    it('should create experiment', async () => {
      mockInvoke.mockResolvedValue(undefined);
      
      const experiment = await abTestingService.createExperiment('recommendation_algorithm', {
        name: 'Test Algorithm Comparison'
      });
      
      expect(experiment.id).toBeDefined();
      expect(experiment.name).toBe('Test Algorithm Comparison');
      expect(experiment.type).toBe('algorithm_comparison');
    });

    it('should assign user to treatment group', async () => {
      mockInvoke.mockResolvedValue(undefined);
      
      const experiment = await abTestingService.createExperiment('ui_layout');
      await abTestingService.startExperiment(experiment.id);
      
      const treatment = await abTestingService.getUserTreatment(
        experiment.id,
        'test-user-hash'
      );
      
      expect(treatment).toBeDefined();
      expect(['control', 'compact_list']).toContain(treatment);
    });

    it('should track experiment interactions', async () => {
      mockInvoke.mockResolvedValue(undefined);
      
      await abTestingService.trackInteraction(
        'exp-123',
        'test-user-hash',
        'recommendation_clicked',
        { itemId: 'test-item' }
      );
      
      expect(mockInvoke).toHaveBeenCalledWith('track_experiment_interaction', {
        experimentId: 'exp-123',
        userHash: 'test-user-hash',
        event: expect.objectContaining({
          eventType: 'recommendation_clicked',
          eventData: { itemId: 'test-item' }
        })
      });
    });

    it('should manage feature flags', () => {
      abTestingService.setFeatureFlag('test_flag', true);
      
      const value = abTestingService.getFeatureFlag('test_flag');
      expect(value).toBe(true);
      
      const defaultValue = abTestingService.getFeatureFlag('nonexistent_flag', false);
      expect(defaultValue).toBe(false);
    });
  });

  describe('Integration Tests', () => {
    it('should handle end-to-end recommendation flow', async () => {
      // Mock all necessary backend calls
      mockInvoke
        .mockResolvedValueOnce('profile_test') // generate_user_profile
        .mockResolvedValueOnce([{ // get_content_based_recommendations
          userProfileHash: 'profile_test',
          recommendationType: 'agent',
          itemId: 'recommended-agent',
          relevanceScore: 0.9,
          confidenceScore: 0.8,
          priorityScore: 0.85
        }])
        .mockResolvedValueOnce(undefined) // cache_recommendations
        .mockResolvedValueOnce(undefined); // update_recommendation_feedback
      
      // Get recommendations
      const recommendations = await recommendationAPI.getRecommendations('agent', {
        algorithm: 'content_based',
        limit: 3
      });
      
      expect(recommendations).toHaveLength(1);
      
      // Simulate user feedback
      await recommendationAPI.submitFeedback({
        recommendationId: 1,
        action: 'adopted'
      });
      
      // Verify all calls were made
      expect(mockInvoke).toHaveBeenCalledTimes(4);
    });

    it('should handle error cases gracefully', async () => {
      mockInvoke.mockRejectedValue(new Error('Database error'));
      
      const recommendations = await recommendationAPI.getRecommendations('agent');
      
      // Should return empty array on error
      expect(recommendations).toEqual([]);
    });

    it('should respect feature flags', () => {
      abTestingService.setFeatureFlag('enable_agent_recommendations', false);
      
      const enabled = abTestingService.getFeatureFlag('enable_agent_recommendations');
      expect(enabled).toBe(false);
      
      // Recommendation system should respect this flag
      abTestingService.setFeatureFlag('enable_agent_recommendations', true);
      const enabledAgain = abTestingService.getFeatureFlag('enable_agent_recommendations');
      expect(enabledAgain).toBe(true);
    });
  });
});

/**
 * Performance tests for recommendation system
 */
describe('Recommendation System Performance Tests', () => {
  it('should handle large numbers of interactions efficiently', async () => {
    const tracking = useRecommendationTracking.getState();
    const startTime = Date.now();
    
    // Simulate 1000 interactions
    for (let i = 0; i < 1000; i++) {
      await tracking.trackInteraction({
        interactionType: 'feature_use',
        featureId: `feature-${i}`,
        featureCategory: 'ui_feature'
      });
    }
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    // Should complete in reasonable time (less than 1 second)
    expect(duration).toBeLessThan(1000);
    expect(tracking.pendingInteractions.length).toBe(1000);
  });

  it('should cache recommendations effectively', async () => {
    mockInvoke.mockResolvedValue([]);
    
    const startTime = Date.now();
    
    // First call - should hit backend
    await recommendationAPI.getRecommendations('agent');
    
    // Second call - should use cache
    await recommendationAPI.getRecommendations('agent');
    
    const endTime = Date.now();
    
    // Should only call backend once due to caching
    expect(mockInvoke).toHaveBeenCalledTimes(2); // One for profile, one for recommendations
  });
});

export {};