/**
 * A/B Testing Framework for Recommendation System
 * Enables experimentation with different algorithms and UI approaches
 */

import { invoke } from "@tauri-apps/api/core";
import { useInteractionTracker } from './recommendation-tracking';

export interface Experiment {
  id: string;
  name: string;
  description: string;
  type: 'algorithm_comparison' | 'ui_variation' | 'content_test' | 'feature_rollout';
  status: 'draft' | 'active' | 'paused' | 'completed';
  
  // Configuration
  controlConfig: ExperimentConfig;
  treatmentConfigs: ExperimentConfig[];
  
  // Targeting
  targetCriteria: TargetingCriteria;
  sampleSizeTarget?: number;
  
  // Timing
  startDate?: Date;
  endDate?: Date;
  duration?: number; // days
  
  // Metrics
  primaryMetric: string;
  secondaryMetrics: string[];
  
  // Results
  participantsCount: number;
  conversionData?: ConversionData;
  statisticalSignificance?: number;
  
  // Metadata
  createdBy?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface ExperimentConfig {
  id: string;
  name: string;
  description: string;
  parameters: Record<string, any>;
  weight: number; // For traffic allocation (0-1)
}

export interface TargetingCriteria {
  userSegments?: string[];
  featureFlags?: Record<string, boolean>;
  userProperties?: Record<string, any>;
  geographicRestrictions?: string[];
  deviceTypes?: string[];
  minSessionCount?: number;
  excludeUserHashes?: string[];
}

export interface ConversionData {
  control: GroupMetrics;
  treatments: Record<string, GroupMetrics>;
}

export interface GroupMetrics {
  participants: number;
  conversions: number;
  conversionRate: number;
  averageEngagement: number;
  bounceRate: number;
  retentionRate: number;
  customMetrics: Record<string, number>;
}

export interface ExperimentParticipation {
  experimentId: string;
  userHash: string;
  treatmentGroup: string;
  assignedAt: Date;
  interactions: InteractionEvent[];
  conversions: ConversionEvent[];
  isActive: boolean;
}

export interface InteractionEvent {
  eventType: string;
  eventData: Record<string, any>;
  timestamp: Date;
  value?: number;
}

export interface ConversionEvent {
  conversionType: string;
  conversionValue: number;
  timestamp: Date;
  metadata?: Record<string, any>;
}

// Predefined experiment templates
const EXPERIMENT_TEMPLATES = {
  recommendation_algorithm: {
    name: 'Recommendation Algorithm Comparison',
    type: 'algorithm_comparison' as const,
    description: 'Compare different recommendation algorithms',
    primaryMetric: 'click_through_rate',
    secondaryMetrics: ['adoption_rate', 'user_satisfaction', 'engagement_time'],
    defaultConfigs: [
      {
        id: 'control',
        name: 'Current Algorithm',
        description: 'Existing hybrid recommendation approach',
        parameters: { algorithm: 'hybrid', weights: { content: 0.6, collaborative: 0.4 } },
        weight: 0.5
      },
      {
        id: 'content_focused',
        name: 'Content-Based Focus',
        description: 'Emphasize content-based recommendations',
        parameters: { algorithm: 'hybrid', weights: { content: 0.8, collaborative: 0.2 } },
        weight: 0.5
      }
    ]
  },
  
  ui_layout: {
    name: 'Recommendation Panel Layout',
    type: 'ui_variation' as const,
    description: 'Test different layouts for recommendation panel',
    primaryMetric: 'engagement_rate',
    secondaryMetrics: ['click_through_rate', 'time_on_panel', 'dismissal_rate'],
    defaultConfigs: [
      {
        id: 'control',
        name: 'Current Layout',
        description: 'Existing card-based layout',
        parameters: { layout: 'cards', showExplanations: true, maxItems: 5 },
        weight: 0.5
      },
      {
        id: 'compact_list',
        name: 'Compact List',
        description: 'Compact list with more items',
        parameters: { layout: 'list', showExplanations: false, maxItems: 8 },
        weight: 0.5
      }
    ]
  },
  
  feature_discovery: {
    name: 'Feature Discovery Approach',
    type: 'content_test' as const,
    description: 'Test different approaches to feature discovery',
    primaryMetric: 'feature_adoption_rate',
    secondaryMetrics: ['tutorial_completion_rate', 'feature_retention', 'user_feedback'],
    defaultConfigs: [
      {
        id: 'control',
        name: 'Subtle Suggestions',
        description: 'Gentle feature suggestions in sidebar',
        parameters: { approach: 'subtle', frequency: 'low', placement: 'sidebar' },
        weight: 0.5
      },
      {
        id: 'guided_tour',
        name: 'Guided Tour',
        description: 'Interactive guided tour for new features',
        parameters: { approach: 'guided', frequency: 'medium', placement: 'modal' },
        weight: 0.5
      }
    ]
  }
};

class ABTestingService {
  private experiments = new Map<string, Experiment>();
  private userAssignments = new Map<string, Map<string, string>>();
  private featureFlags = new Map<string, any>();

  constructor() {
    this.loadExperiments();
    this.loadFeatureFlags();
  }

  /**
   * Create a new experiment
   */
  async createExperiment(
    template: keyof typeof EXPERIMENT_TEMPLATES,
    customizations?: Partial<Experiment>
  ): Promise<Experiment> {
    const templateConfig = EXPERIMENT_TEMPLATES[template];
    
    const experiment: Experiment = {
      id: this.generateExperimentId(),
      name: customizations?.name || templateConfig.name,
      description: customizations?.description || templateConfig.description,
      type: templateConfig.type,
      status: 'draft',
      controlConfig: templateConfig.defaultConfigs[0],
      treatmentConfigs: templateConfig.defaultConfigs.slice(1),
      targetCriteria: customizations?.targetCriteria || {
        userSegments: ['all']
      },
      primaryMetric: templateConfig.primaryMetric,
      secondaryMetrics: templateConfig.secondaryMetrics,
      participantsCount: 0,
      createdAt: new Date(),
      updatedAt: new Date(),
      ...customizations
    };

    try {
      await invoke('create_experiment', { experiment });
      this.experiments.set(experiment.id, experiment);
      return experiment;
    } catch (error) {
      console.error('Failed to create experiment:', error);
      throw error;
    }
  }

  /**
   * Start an experiment
   */
  async startExperiment(experimentId: string): Promise<void> {
    const experiment = this.experiments.get(experimentId);
    if (!experiment) {
      throw new Error(`Experiment ${experimentId} not found`);
    }

    if (experiment.status !== 'draft') {
      throw new Error(`Experiment ${experimentId} is not in draft status`);
    }

    try {
      experiment.status = 'active';
      experiment.startDate = new Date();
      
      if (experiment.duration) {
        experiment.endDate = new Date(Date.now() + experiment.duration * 24 * 60 * 60 * 1000);
      }

      await invoke('update_experiment', { experiment });
      this.experiments.set(experimentId, experiment);
    } catch (error) {
      console.error('Failed to start experiment:', error);
      throw error;
    }
  }

  /**
   * Get user's treatment assignment for an experiment
   */
  async getUserTreatment(
    experimentId: string,
    userHash: string,
    context?: Record<string, any>
  ): Promise<string | null> {
    const experiment = this.experiments.get(experimentId);
    if (!experiment || experiment.status !== 'active') {
      return null;
    }

    // Check if user meets targeting criteria
    if (!this.meetsTargetingCriteria(experiment.targetCriteria, userHash, context)) {
      return null;
    }

    // Check existing assignment
    const existingAssignment = this.userAssignments.get(userHash)?.get(experimentId);
    if (existingAssignment) {
      return existingAssignment;
    }

    // Assign user to treatment group
    const assignment = this.assignUserToTreatment(experiment, userHash);
    
    // Save assignment
    if (!this.userAssignments.has(userHash)) {
      this.userAssignments.set(userHash, new Map());
    }
    this.userAssignments.get(userHash)!.set(experimentId, assignment);

    try {
      await invoke('record_experiment_participation', {
        experimentId,
        userHash,
        treatmentGroup: assignment,
        context
      });
    } catch (error) {
      console.warn('Failed to record experiment participation:', error);
    }

    return assignment;
  }

  /**
   * Track an interaction event for an experiment
   */
  async trackInteraction(
    experimentId: string,
    userHash: string,
    eventType: string,
    eventData: Record<string, any>,
    value?: number
  ): Promise<void> {
    const assignment = this.userAssignments.get(userHash)?.get(experimentId);
    if (!assignment) {
      return; // User not participating in this experiment
    }

    const event: InteractionEvent = {
      eventType,
      eventData,
      timestamp: new Date(),
      value
    };

    try {
      await invoke('track_experiment_interaction', {
        experimentId,
        userHash,
        event
      });
    } catch (error) {
      console.warn('Failed to track experiment interaction:', error);
    }
  }

  /**
   * Track a conversion event for an experiment
   */
  async trackConversion(
    experimentId: string,
    userHash: string,
    conversionType: string,
    conversionValue: number,
    metadata?: Record<string, any>
  ): Promise<void> {
    const assignment = this.userAssignments.get(userHash)?.get(experimentId);
    if (!assignment) {
      return;
    }

    const conversion: ConversionEvent = {
      conversionType,
      conversionValue,
      timestamp: new Date(),
      metadata
    };

    try {
      await invoke('track_experiment_conversion', {
        experimentId,
        userHash,
        conversion
      });
    } catch (error) {
      console.warn('Failed to track experiment conversion:', error);
    }
  }

  /**
   * Get experiment results
   */
  async getExperimentResults(experimentId: string): Promise<ConversionData | null> {
    try {
      const results = await invoke('get_experiment_results', { experimentId });
      return results as ConversionData;
    } catch (error) {
      console.error('Failed to get experiment results:', error);
      return null;
    }
  }

  /**
   * Calculate statistical significance
   */
  calculateSignificance(control: GroupMetrics, treatment: GroupMetrics): number {
    // Simplified statistical significance calculation
    // In practice, you'd use a proper statistical test
    
    const n1 = control.participants;
    const n2 = treatment.participants;
    const p1 = control.conversionRate;
    const p2 = treatment.conversionRate;
    
    if (n1 < 30 || n2 < 30) {
      return 0; // Insufficient sample size
    }
    
    const pooledProp = (control.conversions + treatment.conversions) / (n1 + n2);
    const se = Math.sqrt(pooledProp * (1 - pooledProp) * (1/n1 + 1/n2));
    
    if (se === 0) return 0;
    
    const zScore = Math.abs(p1 - p2) / se;
    
    // Convert z-score to significance level (simplified)
    if (zScore > 2.58) return 0.99; // 99% confidence
    if (zScore > 1.96) return 0.95; // 95% confidence
    if (zScore > 1.64) return 0.90; // 90% confidence
    
    return 0;
  }

  /**
   * Get active experiments for a user
   */
  getActiveExperiments(userHash: string, context?: Record<string, any>): Experiment[] {
    const activeExperiments: Experiment[] = [];
    
    for (const experiment of this.experiments.values()) {
      if (experiment.status === 'active' && 
          this.meetsTargetingCriteria(experiment.targetCriteria, userHash, context)) {
        activeExperiments.push(experiment);
      }
    }
    
    return activeExperiments;
  }

  /**
   * Set feature flag value
   */
  setFeatureFlag(flagName: string, value: any): void {
    this.featureFlags.set(flagName, value);
  }

  /**
   * Get feature flag value
   */
  getFeatureFlag(flagName: string, defaultValue: any = false): any {
    return this.featureFlags.get(flagName) ?? defaultValue;
  }

  /**
   * Check if user meets targeting criteria
   */
  private meetsTargetingCriteria(
    criteria: TargetingCriteria,
    userHash: string,
    context?: Record<string, any>
  ): boolean {
    // Check exclusions
    if (criteria.excludeUserHashes?.includes(userHash)) {
      return false;
    }

    // Check minimum session count
    if (criteria.minSessionCount && context?.sessionCount < criteria.minSessionCount) {
      return false;
    }

    // Check user segments
    if (criteria.userSegments && !criteria.userSegments.includes('all')) {
      const userSegment = context?.userSegment;
      if (!userSegment || !criteria.userSegments.includes(userSegment)) {
        return false;
      }
    }

    // Check feature flags
    if (criteria.featureFlags) {
      for (const [flag, requiredValue] of Object.entries(criteria.featureFlags)) {
        if (this.getFeatureFlag(flag) !== requiredValue) {
          return false;
        }
      }
    }

    return true;
  }

  /**
   * Assign user to treatment group using deterministic hashing
   */
  private assignUserToTreatment(experiment: Experiment, userHash: string): string {
    // Create deterministic hash for this experiment and user
    const combinedHash = this.hashString(`${experiment.id}:${userHash}`);
    const hashValue = combinedHash % 1000000;
    
    // Calculate treatment boundaries based on weights
    const configs = [experiment.controlConfig, ...experiment.treatmentConfigs];
    let boundary = 0;
    
    for (const config of configs) {
      boundary += config.weight * 1000000;
      if (hashValue < boundary) {
        return config.id;
      }
    }
    
    // Fallback to control
    return experiment.controlConfig.id;
  }

  /**
   * Simple string hashing function
   */
  private hashString(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32bit integer
    }
    return Math.abs(hash);
  }

  /**
   * Generate unique experiment ID
   */
  private generateExperimentId(): string {
    return `exp_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
  }

  /**
   * Load experiments from storage
   */
  private async loadExperiments(): Promise<void> {
    try {
      const experiments = await invoke('get_all_experiments');
      for (const exp of experiments as Experiment[]) {
        this.experiments.set(exp.id, exp);
      }
    } catch (error) {
      console.warn('Failed to load experiments:', error);
    }
  }

  /**
   * Load feature flags from storage
   */
  private async loadFeatureFlags(): Promise<void> {
    try {
      const flags = await invoke('get_all_feature_flags');
      for (const [name, value] of Object.entries(flags as Record<string, any>)) {
        this.featureFlags.set(name, value);
      }
    } catch (error) {
      console.warn('Failed to load feature flags:', error);
    }
  }
}

/**
 * React hook for A/B testing
 */
export const useABTesting = (userHash: string) => {
  const abService = new ABTestingService();

  return {
    getUserTreatment: (experimentId: string, context?: Record<string, any>) =>
      abService.getUserTreatment(experimentId, userHash, context),
    
    trackInteraction: (experimentId: string, eventType: string, eventData: Record<string, any>, value?: number) =>
      abService.trackInteraction(experimentId, userHash, eventType, eventData, value),
    
    trackConversion: (experimentId: string, conversionType: string, conversionValue: number, metadata?: Record<string, any>) =>
      abService.trackConversion(experimentId, userHash, conversionType, conversionValue, metadata),
    
    getActiveExperiments: (context?: Record<string, any>) =>
      abService.getActiveExperiments(userHash, context),
    
    getFeatureFlag: (flagName: string, defaultValue?: any) =>
      abService.getFeatureFlag(flagName, defaultValue)
  };
};

// Export singleton instance
export const abTestingService = new ABTestingService();