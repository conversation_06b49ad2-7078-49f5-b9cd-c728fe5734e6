/**
 * Feature Discovery Service
 * Analyzes user behavior and suggests underutilized features
 */

import { invoke } from "@tauri-apps/api/core";
import { useInteractionTracker } from './recommendation-tracking';

export interface FeatureDefinition {
  id: string;
  name: string;
  description: string;
  category: 'productivity' | 'automation' | 'collaboration' | 'advanced' | 'integration';
  icon: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  estimatedTimeSaved: string; // e.g., "30 minutes per day"
  prerequisites?: string[];
  tutorialUrl?: string;
  demoUrl?: string;
  relatedFeatures?: string[];
  usagePatterns: {
    triggerConditions: string[];
    userSegments: string[];
    contextualHints: string[];
  };
}

export interface FeatureUsageData {
  featureId: string;
  totalUses: number;
  lastUsed?: Date;
  userAdopted: boolean;
  avgSessionsToAdopt: number;
  successRate: number;
}

export interface DiscoveryRecommendation {
  feature: FeatureDefinition;
  relevanceScore: number;
  reasoning: string[];
  potentialImpact: 'low' | 'medium' | 'high';
  confidence: number;
  triggerContext: Record<string, any>;
}

// Comprehensive feature definitions
const FEATURES: FeatureDefinition[] = [
  {
    id: 'session_templates',
    name: 'Session Templates',
    description: 'Save and reuse common session configurations, prompts, and settings',
    category: 'productivity',
    icon: '🏗️',
    difficulty: 'beginner',
    estimatedTimeSaved: '15 minutes per session',
    tutorialUrl: '/docs/session-templates',
    usagePatterns: {
      triggerConditions: [
        'user creates multiple similar sessions',
        'user repeats same prompt patterns',
        'user manually sets same configurations repeatedly'
      ],
      userSegments: ['frequent_users', 'power_users'],
      contextualHints: [
        'When starting a new session similar to previous ones',
        'After creating 3+ sessions with similar patterns'
      ]
    }
  },
  {
    id: 'slash_commands',
    name: 'Slash Commands',
    description: 'Quick commands to execute common actions without clicking through menus',
    category: 'productivity',
    icon: '⚡',
    difficulty: 'beginner',
    estimatedTimeSaved: '5-10 seconds per action',
    tutorialUrl: '/docs/slash-commands',
    usagePatterns: {
      triggerConditions: [
        'user frequently navigates through menus',
        'user performs repetitive actions',
        'user shows power-user behavior patterns'
      ],
      userSegments: ['power_users', 'developers'],
      contextualHints: [
        'Type / in any input field to see available commands',
        'When performing repetitive navigation actions'
      ]
    }
  },
  {
    id: 'checkpoint_system',
    name: 'Checkpoint System',
    description: 'Save session states and create branches to explore different conversation paths',
    category: 'advanced',
    icon: '🔄',
    difficulty: 'intermediate',
    estimatedTimeSaved: '30+ minutes when experimenting',
    prerequisites: ['session_management'],
    tutorialUrl: '/docs/checkpoints',
    usagePatterns: {
      triggerConditions: [
        'user has long sessions',
        'user shows experimental behavior',
        'user restarts conversations frequently'
      ],
      userSegments: ['power_users', 'researchers', 'developers'],
      contextualHints: [
        'Before making major changes to your approach',
        'When you want to explore multiple solutions'
      ]
    }
  },
  {
    id: 'mcp_servers',
    name: 'MCP Server Integration',
    description: 'Connect external tools and services for enhanced capabilities',
    category: 'integration',
    icon: '🔌',
    difficulty: 'advanced',
    estimatedTimeSaved: 'Hours of manual work',
    prerequisites: ['basic_usage'],
    tutorialUrl: '/docs/mcp-integration',
    relatedFeatures: ['agent_marketplace'],
    usagePatterns: {
      triggerConditions: [
        'user works with external APIs',
        'user needs tool integration',
        'user has development background'
      ],
      userSegments: ['developers', 'power_users', 'integrators'],
      contextualHints: [
        'When working with APIs or external services',
        'To enhance agent capabilities'
      ]
    }
  },
  {
    id: 'agent_orchestration',
    name: 'Agent Orchestration',
    description: 'Chain multiple agents together for complex workflows',
    category: 'automation',
    icon: '🤖',
    difficulty: 'advanced',
    estimatedTimeSaved: '1-2 hours for complex tasks',
    prerequisites: ['agent_marketplace', 'session_management'],
    tutorialUrl: '/docs/agent-orchestration',
    usagePatterns: {
      triggerConditions: [
        'user frequently uses multiple agents in sequence',
        'user has complex multi-step workflows',
        'user shows automation interest'
      ],
      userSegments: ['power_users', 'automation_enthusiasts'],
      contextualHints: [
        'When you need to combine multiple agent capabilities',
        'For complex multi-step workflows'
      ]
    }
  },
  {
    id: 'keyboard_shortcuts',
    name: 'Keyboard Shortcuts',
    description: 'Navigate and control the app efficiently using keyboard combinations',
    category: 'productivity',
    icon: '⌨️',
    difficulty: 'beginner',
    estimatedTimeSaved: '2-5 seconds per action',
    tutorialUrl: '/docs/keyboard-shortcuts',
    usagePatterns: {
      triggerConditions: [
        'user shows power-user behavior',
        'user frequently uses mouse for navigation',
        'user has development background'
      ],
      userSegments: ['power_users', 'developers', 'frequent_users'],
      contextualHints: [
        'Press Ctrl+K to open command palette',
        'Use Tab to navigate between panels'
      ]
    }
  },
  {
    id: 'session_analytics',
    name: 'Session Analytics',
    description: 'Track your usage patterns, costs, and productivity metrics',
    category: 'productivity',
    icon: '📊',
    difficulty: 'beginner',
    estimatedTimeSaved: 'Better cost and time management',
    tutorialUrl: '/docs/analytics',
    usagePatterns: {
      triggerConditions: [
        'user has been using app for 1+ weeks',
        'user shows cost consciousness',
        'user wants to optimize usage'
      ],
      userSegments: ['frequent_users', 'business_users'],
      contextualHints: [
        'View your usage patterns in the analytics panel',
        'Track costs and optimize your workflow'
      ]
    }
  },
  {
    id: 'hooks_system',
    name: 'Hooks & Automation',
    description: 'Set up automated responses to events and customize behavior',
    category: 'automation',
    icon: '🎣',
    difficulty: 'advanced',
    estimatedTimeSaved: 'Automates repetitive tasks',
    prerequisites: ['advanced_configuration'],
    tutorialUrl: '/docs/hooks',
    usagePatterns: {
      triggerConditions: [
        'user shows advanced technical skills',
        'user has repetitive patterns',
        'user wants deep customization'
      ],
      userSegments: ['developers', 'power_users'],
      contextualHints: [
        'When you need automated responses to events',
        'For deep customization of app behavior'
      ]
    }
  },
  {
    id: 'collaborative_features',
    name: 'Sharing & Collaboration',
    description: 'Share sessions, agents, and collaborate with others',
    category: 'collaboration',
    icon: '👥',
    difficulty: 'intermediate',
    estimatedTimeSaved: 'Enhanced team productivity',
    tutorialUrl: '/docs/collaboration',
    usagePatterns: {
      triggerConditions: [
        'user mentions teamwork or collaboration',
        'user creates valuable content',
        'user shows sharing behavior'
      ],
      userSegments: ['team_users', 'content_creators'],
      contextualHints: [
        'Share your successful agents with teammates',
        'Export session data for collaboration'
      ]
    }
  },
  {
    id: 'advanced_prompting',
    name: 'Advanced Prompting Techniques',
    description: 'Learn sophisticated prompting strategies for better results',
    category: 'advanced',
    icon: '🎯',
    difficulty: 'intermediate',
    estimatedTimeSaved: 'Better quality outputs',
    tutorialUrl: '/docs/advanced-prompting',
    usagePatterns: {
      triggerConditions: [
        'user shows experimental prompting',
        'user has complex use cases',
        'user wants to improve output quality'
      ],
      userSegments: ['power_users', 'researchers', 'content_creators'],
      contextualHints: [
        'When you need more precise or complex outputs',
        'To improve the quality of AI responses'
      ]
    }
  }
];

class FeatureDiscoveryService {
  private features = new Map<string, FeatureDefinition>();

  constructor() {
    FEATURES.forEach(feature => {
      this.features.set(feature.id, feature);
    });
  }

  /**
   * Analyze user behavior and suggest underutilized features
   */
  async getFeatureRecommendations(
    userSessionHash: string,
    context?: Record<string, any>
  ): Promise<DiscoveryRecommendation[]> {
    try {
      // Get user's feature usage patterns
      const usageData = await this.getUserFeatureUsage(userSessionHash);
      
      // Get user behavior patterns
      const behaviorPatterns = await this.getUserBehaviorPatterns(userSessionHash);
      
      // Analyze and score features
      const recommendations: DiscoveryRecommendation[] = [];
      
      for (const feature of this.features.values()) {
        const recommendation = await this.analyzeFeatureRelevance(
          feature,
          usageData,
          behaviorPatterns,
          context
        );
        
        if (recommendation && recommendation.relevanceScore > 0.3) {
          recommendations.push(recommendation);
        }
      }
      
      // Sort by relevance and potential impact
      recommendations.sort((a, b) => {
        const aScore = a.relevanceScore * this.getImpactMultiplier(a.potentialImpact);
        const bScore = b.relevanceScore * this.getImpactMultiplier(b.potentialImpact);
        return bScore - aScore;
      });
      
      return recommendations.slice(0, 5); // Return top 5 recommendations
      
    } catch (error) {
      console.error('Failed to get feature recommendations:', error);
      return [];
    }
  }

  /**
   * Get contextual feature suggestions based on current user action
   */
  async getContextualSuggestions(
    action: string,
    context: Record<string, any>
  ): Promise<DiscoveryRecommendation[]> {
    const relevantFeatures = this.getFeaturesByContext(action, context);
    const suggestions: DiscoveryRecommendation[] = [];
    
    for (const feature of relevantFeatures) {
      const suggestion: DiscoveryRecommendation = {
        feature,
        relevanceScore: 0.8, // High relevance for contextual suggestions
        reasoning: [`Triggered by: ${action}`, ...feature.usagePatterns.contextualHints],
        potentialImpact: this.estimateImpact(feature, context),
        confidence: 0.7,
        triggerContext: { action, ...context }
      };
      
      suggestions.push(suggestion);
    }
    
    return suggestions.slice(0, 3); // Return top 3 contextual suggestions
  }

  /**
   * Check if user should be nudged about a specific feature
   */
  async shouldNudgeFeature(
    featureId: string,
    userSessionHash: string,
    context?: Record<string, any>
  ): Promise<boolean> {
    const feature = this.features.get(featureId);
    if (!feature) return false;
    
    const usageData = await this.getFeatureUsageData(featureId, userSessionHash);
    
    // Don't nudge if already using the feature
    if (usageData.userAdopted || usageData.totalUses > 2) {
      return false;
    }
    
    // Check trigger conditions
    const behaviorPatterns = await this.getUserBehaviorPatterns(userSessionHash);
    return this.checkTriggerConditions(feature, behaviorPatterns, context);
  }

  private async getUserFeatureUsage(userSessionHash: string): Promise<Map<string, FeatureUsageData>> {
    try {
      const usageData = await invoke('get_user_feature_usage', {
        userSessionHash,
        features: Array.from(this.features.keys())
      });
      
      return new Map(usageData);
    } catch (error) {
      console.warn('Failed to get feature usage data:', error);
      return new Map();
    }
  }

  private async getUserBehaviorPatterns(userSessionHash: string): Promise<any[]> {
    try {
      return await invoke('analyze_user_behavior_patterns', {
        userSessionHash,
        analysisDepth: 'standard'
      });
    } catch (error) {
      console.warn('Failed to get behavior patterns:', error);
      return [];
    }
  }

  private async getFeatureUsageData(featureId: string, userSessionHash: string): Promise<FeatureUsageData> {
    try {
      return await invoke('get_feature_usage_data', {
        featureId,
        userSessionHash
      });
    } catch (error) {
      return {
        featureId,
        totalUses: 0,
        userAdopted: false,
        avgSessionsToAdopt: 0,
        successRate: 0
      };
    }
  }

  private async analyzeFeatureRelevance(
    feature: FeatureDefinition,
    usageData: Map<string, FeatureUsageData>,
    behaviorPatterns: any[],
    context?: Record<string, any>
  ): Promise<DiscoveryRecommendation | null> {
    const usage = usageData.get(feature.id);
    
    // Skip if user already adopted this feature
    if (usage?.userAdopted || (usage?.totalUses || 0) > 5) {
      return null;
    }
    
    let relevanceScore = 0;
    const reasoning: string[] = [];
    
    // Check trigger conditions
    const triggerScore = this.checkTriggerConditions(feature, behaviorPatterns, context);
    relevanceScore += triggerScore * 0.4;
    
    if (triggerScore > 0.5) {
      reasoning.push('Your usage patterns suggest this feature would be helpful');
    }
    
    // Check prerequisites
    const prerequisiteScore = this.checkPrerequisites(feature, usageData);
    relevanceScore += prerequisiteScore * 0.2;
    
    if (prerequisiteScore > 0.8) {
      reasoning.push('You have the necessary experience to use this feature');
    }
    
    // Check user segment match
    const segmentScore = this.checkUserSegment(feature, behaviorPatterns);
    relevanceScore += segmentScore * 0.3;
    
    if (segmentScore > 0.6) {
      reasoning.push('This feature is popular among users with similar patterns');
    }
    
    // Check potential impact
    const impact = this.estimateImpact(feature, context);
    relevanceScore += this.getImpactMultiplier(impact) * 0.1;
    
    if (impact === 'high') {
      reasoning.push('This feature could significantly improve your workflow');
    }
    
    // Calculate confidence based on data quality
    const confidence = Math.min(0.9, (behaviorPatterns.length * 0.1) + 0.5);
    
    return {
      feature,
      relevanceScore: Math.min(1.0, relevanceScore),
      reasoning,
      potentialImpact: impact,
      confidence,
      triggerContext: context || {}
    };
  }

  private checkTriggerConditions(
    feature: FeatureDefinition,
    behaviorPatterns: any[],
    context?: Record<string, any>
  ): number {
    let score = 0;
    let totalConditions = feature.usagePatterns.triggerConditions.length;
    
    for (const condition of feature.usagePatterns.triggerConditions) {
      if (this.evaluateCondition(condition, behaviorPatterns, context)) {
        score += 1 / totalConditions;
      }
    }
    
    return score;
  }

  private evaluateCondition(
    condition: string,
    behaviorPatterns: any[],
    context?: Record<string, any>
  ): boolean {
    // Simple pattern matching for conditions
    // In a real implementation, this would be more sophisticated
    
    if (condition.includes('multiple similar sessions')) {
      return behaviorPatterns.some(p => 
        p.pattern_type === 'workflow_sequence' && p.frequency_score > 0.6
      );
    }
    
    if (condition.includes('repetitive actions')) {
      return behaviorPatterns.some(p => 
        p.pattern_type === 'feature_combination' && p.frequency_score > 0.7
      );
    }
    
    if (condition.includes('long sessions')) {
      return context?.sessionDuration && context.sessionDuration > 1800; // 30 minutes
    }
    
    if (condition.includes('development background')) {
      return context?.projectType && ['javascript', 'python', 'rust', 'go'].includes(context.projectType);
    }
    
    if (condition.includes('external APIs')) {
      return context?.fileExtensions?.some((ext: string) => 
        ['json', 'yaml', 'yml'].includes(ext)
      );
    }
    
    return false;
  }

  private checkPrerequisites(
    feature: FeatureDefinition,
    usageData: Map<string, FeatureUsageData>
  ): number {
    if (!feature.prerequisites?.length) {
      return 1.0; // No prerequisites
    }
    
    let metPrerequisites = 0;
    for (const prereq of feature.prerequisites) {
      const usage = usageData.get(prereq);
      if (usage?.userAdopted || (usage?.totalUses || 0) > 2) {
        metPrerequisites++;
      }
    }
    
    return metPrerequisites / feature.prerequisites.length;
  }

  private checkUserSegment(
    feature: FeatureDefinition,
    behaviorPatterns: any[]
  ): number {
    // Simplified segment detection
    const segments = this.detectUserSegments(behaviorPatterns);
    
    let matchScore = 0;
    for (const segment of feature.usagePatterns.userSegments) {
      if (segments.includes(segment)) {
        matchScore += 1 / feature.usagePatterns.userSegments.length;
      }
    }
    
    return matchScore;
  }

  private detectUserSegments(behaviorPatterns: any[]): string[] {
    const segments: string[] = [];
    
    const totalInteractions = behaviorPatterns.reduce((sum, p) => sum + (p.frequency_score || 0), 0);
    
    if (totalInteractions > 50) segments.push('frequent_users');
    if (totalInteractions > 100) segments.push('power_users');
    
    const hasComplexPatterns = behaviorPatterns.some(p => 
      p.pattern_type === 'workflow_sequence' && p.confidence_level > 0.8
    );
    if (hasComplexPatterns) segments.push('advanced_users');
    
    return segments;
  }

  private estimateImpact(
    feature: FeatureDefinition,
    context?: Record<string, any>
  ): 'low' | 'medium' | 'high' {
    if (feature.category === 'productivity' && feature.difficulty === 'beginner') {
      return 'high';
    }
    
    if (feature.category === 'automation') {
      return 'high';
    }
    
    if (feature.difficulty === 'advanced') {
      return context?.userExperience === 'advanced' ? 'high' : 'medium';
    }
    
    return 'medium';
  }

  private getImpactMultiplier(impact: 'low' | 'medium' | 'high'): number {
    switch (impact) {
      case 'high': return 1.5;
      case 'medium': return 1.0;
      case 'low': return 0.7;
    }
  }

  private getFeaturesByContext(action: string, context: Record<string, any>): FeatureDefinition[] {
    const contextualFeatures: FeatureDefinition[] = [];
    
    for (const feature of this.features.values()) {
      const hints = feature.usagePatterns.contextualHints;
      
      if (hints.some(hint => this.matchesContext(hint, action, context))) {
        contextualFeatures.push(feature);
      }
    }
    
    return contextualFeatures;
  }

  private matchesContext(hint: string, action: string, context: Record<string, any>): boolean {
    // Simple context matching
    if (hint.includes('starting a new session') && action === 'session_start') {
      return true;
    }
    
    if (hint.includes('repetitive navigation') && action === 'menu_navigation') {
      return true;
    }
    
    if (hint.includes('experimental behavior') && context.experimentalPatterns) {
      return true;
    }
    
    return false;
  }

  /**
   * Get all available features for admin/debug purposes
   */
  getAllFeatures(): FeatureDefinition[] {
    return Array.from(this.features.values());
  }

  /**
   * Get specific feature by ID
   */
  getFeature(id: string): FeatureDefinition | undefined {
    return this.features.get(id);
  }
}

// Export singleton instance
export const featureDiscovery = new FeatureDiscoveryService();