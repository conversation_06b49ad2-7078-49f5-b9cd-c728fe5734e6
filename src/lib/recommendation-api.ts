import { invoke } from "@tauri-apps/api/core";

/**
 * Recommendation types
 */
export type RecommendationType = 'agent' | 'feature' | 'workflow' | 'configuration';

/**
 * Algorithm types for generating recommendations
 */
export type RecommendationAlgorithm = 'content_based' | 'collaborative' | 'hybrid' | 'feature_discovery';

/**
 * Recommendation data structure
 */
export interface Recommendation {
  id?: number;
  userProfileHash: string;
  recommendationType: RecommendationType;
  itemId: string;
  itemCategory?: string;
  relevanceScore: number;
  confidenceScore: number;
  priorityScore: number;
  reasoningData?: Record<string, any>;
  contextTriggers?: Record<string, any>;
  expectedBenefit?: Record<string, any>;
  algorithmVersion?: string;
  
  // UI display data (enriched on frontend)
  title?: string;
  description?: string;
  icon?: string;
  actionText?: string;
  learnMoreUrl?: string;
}

/**
 * Recommendation request options
 */
export interface RecommendationOptions {
  algorithm?: RecommendationAlgorithm;
  limit?: number;
  minConfidence?: number;
  includeExplanations?: boolean;
  projectContext?: Record<string, any>;
  userPreferences?: Record<string, any>;
  excludeItemIds?: string[];
}

/**
 * Recommendation feedback data
 */
export interface RecommendationFeedback {
  recommendationId: number;
  action: 'shown' | 'clicked' | 'adopted' | 'dismissed' | 'rated';
  rating?: number; // 1-5
  feedbackText?: string;
  context?: Record<string, any>;
}

/**
 * User behavior analysis result
 */
export interface BehaviorAnalysis {
  patterns: BehaviorPattern[];
  insights: string[];
  recommendations: string[];
  lastAnalyzed: string;
}

/**
 * Behavior pattern structure
 */
export interface BehaviorPattern {
  patternType: 'workflow_sequence' | 'feature_combination' | 'time_based' | 'project_based';
  patternData: Record<string, any>;
  frequencyScore: number;
  successScore: number;
  efficiencyScore: number;
  confidenceLevel: number;
}

/**
 * Recommendation service API
 */
class RecommendationAPI {
  private userProfileHash: string | null = null;
  private cacheExpiry: number = 5 * 60 * 1000; // 5 minutes
  private cache: Map<string, { data: any; timestamp: number }> = new Map();

  /**
   * Get or generate user profile hash
   */
  async getUserProfile(forceRefresh: boolean = false): Promise<string> {
    if (this.userProfileHash && !forceRefresh) {
      return this.userProfileHash;
    }

    try {
      this.userProfileHash = await invoke('generate_user_profile', {
        userSessionHash: this.getCurrentSessionHash(),
        lookbackDays: 30
      });
      return this.userProfileHash;
    } catch (error) {
      console.error('Failed to generate user profile:', error);
      // Fallback to session hash
      return this.getCurrentSessionHash();
    }
  }

  /**
   * Get personalized recommendations
   */
  async getRecommendations(
    type: RecommendationType,
    options: RecommendationOptions = {}
  ): Promise<Recommendation[]> {
    const cacheKey = `recommendations_${type}_${JSON.stringify(options)}`;
    const cached = this.getCached(cacheKey);
    if (cached) return cached;

    const userProfile = await this.getUserProfile();
    let recommendations: Recommendation[] = [];

    try {
      switch (options.algorithm || 'hybrid') {
        case 'content_based':
          recommendations = await this.getContentBasedRecommendations(userProfile, options);
          break;
        case 'collaborative':
          recommendations = await this.getCollaborativeRecommendations(userProfile, options);
          break;
        case 'hybrid':
          recommendations = await this.getHybridRecommendations(userProfile, options);
          break;
        case 'feature_discovery':
          recommendations = await this.getFeatureDiscoveryRecommendations(userProfile, options);
          break;
      }

      // Enrich recommendations with UI data
      recommendations = await this.enrichRecommendations(recommendations);

      // Filter and sort
      recommendations = this.filterAndSortRecommendations(recommendations, options);

      // Cache recommendations
      await this.cacheRecommendations(recommendations);

      this.setCached(cacheKey, recommendations);
      return recommendations;

    } catch (error) {
      console.error('Failed to get recommendations:', error);
      return [];
    }
  }

  /**
   * Get content-based recommendations
   */
  private async getContentBasedRecommendations(
    userProfile: string,
    options: RecommendationOptions
  ): Promise<Recommendation[]> {
    return await invoke('get_content_based_recommendations', {
      userProfileHash: userProfile,
      projectContext: options.projectContext ? JSON.stringify(options.projectContext) : null,
      limit: options.limit || 10
    });
  }

  /**
   * Get collaborative filtering recommendations
   */
  private async getCollaborativeRecommendations(
    userProfile: string,
    options: RecommendationOptions
  ): Promise<Recommendation[]> {
    return await invoke('get_collaborative_recommendations', {
      userProfileHash: userProfile,
      limit: options.limit || 10
    });
  }

  /**
   * Get hybrid recommendations (combines multiple algorithms)
   */
  private async getHybridRecommendations(
    userProfile: string,
    options: RecommendationOptions
  ): Promise<Recommendation[]> {
    const [contentBased, collaborative] = await Promise.all([
      this.getContentBasedRecommendations(userProfile, { ...options, limit: 15 }),
      this.getCollaborativeRecommendations(userProfile, { ...options, limit: 15 })
    ]);

    // Combine and re-score recommendations
    const combined = [...contentBased, ...collaborative];
    const merged = this.mergeAndRescoreRecommendations(combined);
    
    return merged.slice(0, options.limit || 10);
  }

  /**
   * Get feature discovery recommendations
   */
  private async getFeatureDiscoveryRecommendations(
    userProfile: string,
    options: RecommendationOptions
  ): Promise<Recommendation[]> {
    // This would analyze unused features and suggest them
    // For now, return some mock feature discovery recommendations
    return [
      {
        userProfileHash: userProfile,
        recommendationType: 'feature',
        itemId: 'session_templates',
        itemCategory: 'productivity',
        relevanceScore: 0.8,
        confidenceScore: 0.7,
        priorityScore: 0.75,
        reasoningData: {
          match_type: 'feature_discovery',
          underutilized: true,
          potential_benefit: 'high'
        },
        expectedBenefit: {
          time_saved: '30%',
          workflow_improvement: true
        },
        algorithmVersion: 'feature_discovery_v1.0'
      }
    ];
  }

  /**
   * Enrich recommendations with UI display data
   */
  private async enrichRecommendations(recommendations: Recommendation[]): Promise<Recommendation[]> {
    const enriched = await Promise.all(
      recommendations.map(async (rec) => {
        try {
          if (rec.recommendationType === 'agent') {
            // Get agent details from marketplace
            const agentDetails = await invoke('marketplace_get_agent', { agentId: rec.itemId });
            return {
              ...rec,
              title: agentDetails.name,
              description: agentDetails.description,
              icon: agentDetails.icon,
              actionText: 'Install Agent',
              learnMoreUrl: agentDetails.source_url
            };
          } else if (rec.recommendationType === 'feature') {
            return this.enrichFeatureRecommendation(rec);
          }
          return rec;
        } catch (error) {
          console.warn('Failed to enrich recommendation:', rec.itemId, error);
          return rec;
        }
      })
    );

    return enriched;
  }

  /**
   * Enrich feature recommendations with UI data
   */
  private enrichFeatureRecommendation(rec: Recommendation): Recommendation {
    const featureMetadata: Record<string, any> = {
      session_templates: {
        title: 'Session Templates',
        description: 'Save and reuse common session configurations',
        icon: '🏗️',
        actionText: 'Explore Templates'
      },
      slash_commands: {
        title: 'Slash Commands',
        description: 'Quick commands to speed up your workflow',
        icon: '⚡',
        actionText: 'Learn Commands'
      },
      checkpoint_system: {
        title: 'Checkpoint System',
        description: 'Save session states and branch your conversations',
        icon: '🔄',
        actionText: 'Enable Checkpoints'
      },
      marketplace_browse: {
        title: 'Agent Marketplace',
        description: 'Discover powerful agents created by the community',
        icon: '🏪',
        actionText: 'Browse Marketplace'
      }
    };

    const metadata = featureMetadata[rec.itemId] || {
      title: rec.itemId.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
      description: 'Discover this powerful feature',
      icon: '✨',
      actionText: 'Learn More'
    };

    return { ...rec, ...metadata };
  }

  /**
   * Filter and sort recommendations
   */
  private filterAndSortRecommendations(
    recommendations: Recommendation[],
    options: RecommendationOptions
  ): Recommendation[] {
    let filtered = recommendations;

    // Filter by minimum confidence
    if (options.minConfidence) {
      filtered = filtered.filter(rec => rec.confidenceScore >= options.minConfidence!);
    }

    // Exclude specific items
    if (options.excludeItemIds?.length) {
      filtered = filtered.filter(rec => !options.excludeItemIds!.includes(rec.itemId));
    }

    // Sort by priority score
    filtered.sort((a, b) => b.priorityScore - a.priorityScore);

    // Apply limit
    if (options.limit) {
      filtered = filtered.slice(0, options.limit);
    }

    return filtered;
  }

  /**
   * Merge and re-score recommendations from multiple algorithms
   */
  private mergeAndRescoreRecommendations(recommendations: Recommendation[]): Recommendation[] {
    const merged = new Map<string, Recommendation>();

    for (const rec of recommendations) {
      const existing = merged.get(rec.itemId);
      
      if (existing) {
        // Combine scores (weighted average)
        existing.relevanceScore = (existing.relevanceScore + rec.relevanceScore) / 2;
        existing.confidenceScore = Math.max(existing.confidenceScore, rec.confidenceScore);
        existing.priorityScore = (existing.priorityScore + rec.priorityScore) / 2;
      } else {
        merged.set(rec.itemId, { ...rec });
      }
    }

    return Array.from(merged.values());
  }

  /**
   * Cache recommendations in database
   */
  private async cacheRecommendations(recommendations: Recommendation[]): Promise<void> {
    try {
      await invoke('cache_recommendations', { recommendations });
    } catch (error) {
      console.warn('Failed to cache recommendations:', error);
    }
  }

  /**
   * Submit recommendation feedback
   */
  async submitFeedback(feedback: RecommendationFeedback): Promise<void> {
    try {
      await invoke('update_recommendation_feedback', {
        recommendationId: feedback.recommendationId,
        action: feedback.action,
        rating: feedback.rating,
        feedbackText: feedback.feedbackText,
        context: feedback.context
      });

      // Clear relevant cache entries
      this.clearCache();
    } catch (error) {
      console.error('Failed to submit feedback:', error);
      throw error;
    }
  }

  /**
   * Analyze user behavior patterns
   */
  async analyzeBehavior(depth: 'standard' | 'deep' = 'standard'): Promise<BehaviorAnalysis> {
    try {
      const patterns = await invoke('analyze_user_behavior_patterns', {
        userSessionHash: this.getCurrentSessionHash(),
        analysisDepth: depth
      });

      return {
        patterns,
        insights: this.generateInsights(patterns),
        recommendations: this.generateBehaviorRecommendations(patterns),
        lastAnalyzed: new Date().toISOString()
      };
    } catch (error) {
      console.error('Failed to analyze behavior:', error);
      return {
        patterns: [],
        insights: [],
        recommendations: [],
        lastAnalyzed: new Date().toISOString()
      };
    }
  }

  /**
   * Generate insights from behavior patterns
   */
  private generateInsights(patterns: BehaviorPattern[]): string[] {
    const insights: string[] = [];

    const workflowPatterns = patterns.filter(p => p.patternType === 'workflow_sequence');
    if (workflowPatterns.length > 0) {
      insights.push(`You have ${workflowPatterns.length} consistent workflow patterns`);
    }

    const highEfficiencyPatterns = patterns.filter(p => p.efficiencyScore > 0.8);
    if (highEfficiencyPatterns.length > 0) {
      insights.push(`${highEfficiencyPatterns.length} of your patterns are highly efficient`);
    }

    const timePatterns = patterns.filter(p => p.patternType === 'time_based');
    if (timePatterns.length > 0) {
      insights.push('We detected time-based usage patterns in your workflow');
    }

    return insights;
  }

  /**
   * Generate behavior-based recommendations
   */
  private generateBehaviorRecommendations(patterns: BehaviorPattern[]): string[] {
    const recommendations: string[] = [];

    const inefficientPatterns = patterns.filter(p => p.efficiencyScore < 0.6);
    if (inefficientPatterns.length > 0) {
      recommendations.push('Consider using session templates to streamline repetitive workflows');
    }

    const sequentialPatterns = patterns.filter(p => 
      p.patternType === 'workflow_sequence' && p.frequencyScore > 0.7
    );
    if (sequentialPatterns.length > 0) {
      recommendations.push('Try slash commands to speed up common action sequences');
    }

    return recommendations;
  }

  /**
   * Get current session hash (fallback method)
   */
  private getCurrentSessionHash(): string {
    // This should match the logic in recommendation-tracking.ts
    const factors = [
      navigator.userAgent,
      screen.width,
      screen.height,
      new Date().toDateString()
    ].join('|');
    
    let hash = 0;
    for (let i = 0; i < factors.length; i++) {
      const char = factors.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    
    return Math.abs(hash).toString(36);
  }

  /**
   * Cache management
   */
  private getCached(key: string): any | null {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.cacheExpiry) {
      return cached.data;
    }
    return null;
  }

  private setCached(key: string, data: any): void {
    this.cache.set(key, { data, timestamp: Date.now() });
  }

  private clearCache(): void {
    this.cache.clear();
  }
}

// Export singleton instance
export const recommendationAPI = new RecommendationAPI();