/**
 * Validation schemas for Marketplace functionality
 * Follows existing validation patterns and provides runtime type safety
 */

import type {
  MarketplaceAgent,
  AgentCategory,
  AgentDependency,
  AgentCollection,
  MarketplaceSearchParams,
  AgentInstallRequest,
  AgentRatingSubmission,
  UserPreference,
  MarketplaceConfig
} from '@/types/marketplace';

// ============================================================================
// Validation Result Types
// ============================================================================

export interface ValidationResult<T = unknown> {
  success: boolean;
  data?: T;
  errors: ValidationError[];
  warnings: ValidationWarning[];
}

export interface ValidationError {
  field: string;
  message: string;
  code: string;
  value?: unknown;
}

export interface ValidationWarning {
  field: string;
  message: string;
  code: string;
  value?: unknown;
}

// ============================================================================
// Base Validation Utilities
// ============================================================================

class Validator<T = unknown> {
  private errors: ValidationError[] = [];
  private warnings: ValidationWarning[] = [];

  constructor(private data: T) {}

  required(field: keyof T, message?: string): this {
    const value = this.data[field];
    if (value === null || value === undefined || value === '') {
      this.errors.push({
        field: String(field),
        message: message || `${String(field)} is required`,
        code: 'REQUIRED',
        value
      });
    }
    return this;
  }

  string(field: keyof T, options?: { minLength?: number; maxLength?: number; pattern?: RegExp }): this {
    const value = this.data[field];
    if (value !== undefined && value !== null) {
      if (typeof value !== 'string') {
        this.errors.push({
          field: String(field),
          message: `${String(field)} must be a string`,
          code: 'TYPE_STRING',
          value
        });
        return this;
      }

      if (options?.minLength && value.length < options.minLength) {
        this.errors.push({
          field: String(field),
          message: `${String(field)} must be at least ${options.minLength} characters`,
          code: 'MIN_LENGTH',
          value
        });
      }

      if (options?.maxLength && value.length > options.maxLength) {
        this.errors.push({
          field: String(field),
          message: `${String(field)} must be at most ${options.maxLength} characters`,
          code: 'MAX_LENGTH',
          value
        });
      }

      if (options?.pattern && !options.pattern.test(value)) {
        this.errors.push({
          field: String(field),
          message: `${String(field)} format is invalid`,
          code: 'PATTERN',
          value
        });
      }
    }
    return this;
  }

  number(field: keyof T, options?: { min?: number; max?: number; integer?: boolean }): this {
    const value = this.data[field];
    if (value !== undefined && value !== null) {
      if (typeof value !== 'number' || isNaN(value)) {
        this.errors.push({
          field: String(field),
          message: `${String(field)} must be a number`,
          code: 'TYPE_NUMBER',
          value
        });
        return this;
      }

      if (options?.integer && !Number.isInteger(value)) {
        this.errors.push({
          field: String(field),
          message: `${String(field)} must be an integer`,
          code: 'INTEGER',
          value
        });
      }

      if (options?.min !== undefined && value < options.min) {
        this.errors.push({
          field: String(field),
          message: `${String(field)} must be at least ${options.min}`,
          code: 'MIN_VALUE',
          value
        });
      }

      if (options?.max !== undefined && value > options.max) {
        this.errors.push({
          field: String(field),
          message: `${String(field)} must be at most ${options.max}`,
          code: 'MAX_VALUE',
          value
        });
      }
    }
    return this;
  }

  boolean(field: keyof T): this {
    const value = this.data[field];
    if (value !== undefined && value !== null && typeof value !== 'boolean') {
      this.errors.push({
        field: String(field),
        message: `${String(field)} must be a boolean`,
        code: 'TYPE_BOOLEAN',
        value
      });
    }
    return this;
  }

  array(field: keyof T, options?: { minLength?: number; maxLength?: number }): this {
    const value = this.data[field];
    if (value !== undefined && value !== null) {
      if (!Array.isArray(value)) {
        this.errors.push({
          field: String(field),
          message: `${String(field)} must be an array`,
          code: 'TYPE_ARRAY',
          value
        });
        return this;
      }

      if (options?.minLength && value.length < options.minLength) {
        this.errors.push({
          field: String(field),
          message: `${String(field)} must have at least ${options.minLength} items`,
          code: 'ARRAY_MIN_LENGTH',
          value
        });
      }

      if (options?.maxLength && value.length > options.maxLength) {
        this.errors.push({
          field: String(field),
          message: `${String(field)} must have at most ${options.maxLength} items`,
          code: 'ARRAY_MAX_LENGTH',
          value
        });
      }
    }
    return this;
  }

  oneOf<K extends keyof T>(field: K, values: Array<T[K]>, message?: string): this {
    const value = this.data[field];
    if (value !== undefined && value !== null && !values.includes(value)) {
      this.errors.push({
        field: String(field),
        message: message || `${String(field)} must be one of: ${values.join(', ')}`,
        code: 'ONE_OF',
        value
      });
    }
    return this;
  }

  url(field: keyof T, message?: string): this {
    const value = this.data[field];
    if (value !== undefined && value !== null) {
      if (typeof value !== 'string') {
        this.errors.push({
          field: String(field),
          message: `${String(field)} must be a string`,
          code: 'TYPE_STRING',
          value
        });
        return this;
      }

      try {
        new URL(value);
      } catch {
        this.errors.push({
          field: String(field),
          message: message || `${String(field)} must be a valid URL`,
          code: 'INVALID_URL',
          value
        });
      }
    }
    return this;
  }

  semver(field: keyof T, message?: string): this {
    const value = this.data[field];
    if (value !== undefined && value !== null) {
      if (typeof value !== 'string') {
        this.errors.push({
          field: String(field),
          message: `${String(field)} must be a string`,
          code: 'TYPE_STRING',
          value
        });
        return this;
      }

      // Basic semver validation (major.minor.patch with optional pre-release)
      const semverPattern = /^(0|[1-9]\d*)\.(0|[1-9]\d*)\.(0|[1-9]\d*)(?:-((?:0|[1-9]\d*|\d*[a-zA-Z-][0-9a-zA-Z-]*)(?:\.(?:0|[1-9]\d*|\d*[a-zA-Z-][0-9a-zA-Z-]*))*))?(?:\+([0-9a-zA-Z-]+(?:\.[0-9a-zA-Z-]+)*))?$/;
      
      if (!semverPattern.test(value)) {
        this.errors.push({
          field: String(field),
          message: message || `${String(field)} must be a valid semantic version (e.g., 1.2.3)`,
          code: 'INVALID_SEMVER',
          value
        });
      }
    }
    return this;
  }

  warn(field: keyof T, message: string, code: string): this {
    this.warnings.push({
      field: String(field),
      message,
      code,
      value: this.data[field]
    });
    return this;
  }

  custom(field: keyof T, validator: (value: T[keyof T]) => string | null): this {
    const value = this.data[field];
    const error = validator(value);
    if (error) {
      this.errors.push({
        field: String(field),
        message: error,
        code: 'CUSTOM',
        value
      });
    }
    return this;
  }

  result(): ValidationResult<T> {
    return {
      success: this.errors.length === 0,
      data: this.errors.length === 0 ? this.data : undefined,
      errors: this.errors,
      warnings: this.warnings
    };
  }
}

// ============================================================================
// Marketplace-specific Validators
// ============================================================================

export function validateMarketplaceAgent(data: Partial<MarketplaceAgent>): ValidationResult<MarketplaceAgent> {
  return new Validator(data as MarketplaceAgent)
    // Required fields
    .required('remote_id')
    .required('name')
    .required('icon')
    .required('system_prompt')
    .required('model')
    .required('author')
    .required('description')
    .required('version')
    .required('download_url')
    
    // String validations
    .string('remote_id', { minLength: 1, maxLength: 255 })
    .string('name', { minLength: 1, maxLength: 255 })
    .string('icon', { minLength: 1, maxLength: 50 })
    .string('system_prompt', { minLength: 10 })
    .string('author', { minLength: 1, maxLength: 255 })
    .string('description', { minLength: 10, maxLength: 500 })
    .string('long_description', { maxLength: 5000 })
    .string('license', { maxLength: 50 })
    .string('model', { minLength: 1, maxLength: 50 })
    
    // Version validation
    .semver('version')
    
    // URL validations
    .url('download_url')
    .url('source_url')
    .url('documentation_url')
    .url('homepage_url')
    .url('author_url')
    
    // Numeric validations
    .number('category_id', { min: 1, integer: true })
    .number('download_count', { min: 0, integer: true })
    .number('rating_average', { min: 0, max: 5 })
    .number('rating_count', { min: 0, integer: true })
    .number('file_size', { min: 0, integer: true })
    
    // Boolean validations
    .boolean('is_installed')
    
    // Enum validations
    .oneOf('installation_source', ['github', 'marketplace', 'manual'])
    
    // Custom validations
    .custom('tags', (value) => {
      if (value && !Array.isArray(value)) {
        return 'tags must be an array';
      }
      if (value && Array.isArray(value) && value.some((tag: unknown) => typeof tag !== 'string')) {
        return 'all tags must be strings';
      }
      if (value && Array.isArray(value) && value.length > 20) {
        return 'cannot have more than 20 tags';
      }
      return null;
    })
    
    .custom('rating_average', (value) => {
      const ratingCount = (data as MarketplaceAgent).rating_count || 0;
      if (value && typeof value === 'number' && value > 0 && ratingCount === 0) {
        return 'cannot have rating_average without rating_count';
      }
      return null;
    })
    
    .result();
}

export function validateAgentCategory(data: Partial<AgentCategory>): ValidationResult<AgentCategory> {
  return new Validator(data as AgentCategory)
    .required('name')
    .required('slug')
    
    .string('name', { minLength: 1, maxLength: 100 })
    .string('slug', { minLength: 1, maxLength: 100, pattern: /^[a-z0-9-]+$/ })
    .string('description', { maxLength: 500 })
    .string('icon', { maxLength: 10 })
    
    .number('parent_id', { min: 1, integer: true })
    .number('sort_order', { min: 0, integer: true })
    
    .boolean('is_active')
    
    .result();
}

export function validateAgentRating(data: Partial<AgentRatingSubmission>): ValidationResult<AgentRatingSubmission> {
  return new Validator(data as AgentRatingSubmission)
    .required('marketplace_agent_id')
    .required('rating')
    
    .number('marketplace_agent_id', { min: 1, integer: true })
    .number('rating', { min: 1, max: 5, integer: true })
    
    .string('review_title', { maxLength: 200 })
    .string('review_content', { maxLength: 2000 })
    
    .result();
}

export function validateAgentDependency(data: Partial<AgentDependency>): ValidationResult<AgentDependency> {
  return new Validator(data as AgentDependency)
    .required('marketplace_agent_id')
    .required('dependency_type')
    .required('dependency_name')
    
    .number('marketplace_agent_id', { min: 1, integer: true })
    .oneOf('dependency_type', ['agent', 'tool', 'binary', 'npm_package'])
    .string('dependency_name', { minLength: 1, maxLength: 255 })
    .string('dependency_version', { maxLength: 100 })
    
    .boolean('is_required')
    
    .result();
}

export function validateAgentCollection(data: Partial<AgentCollection>): ValidationResult<AgentCollection> {
  return new Validator(data as AgentCollection)
    .required('name')
    
    .string('name', { minLength: 1, maxLength: 255 })
    .string('description', { maxLength: 1000 })
    .string('curator', { maxLength: 255 })
    
    .number('sort_order', { min: 0, integer: true })
    
    .boolean('is_featured')
    .boolean('is_public')
    
    .result();
}

export function validateMarketplaceSearchParams(data: Partial<MarketplaceSearchParams>): ValidationResult<MarketplaceSearchParams> {
  return new Validator(data as MarketplaceSearchParams)
    .string('query', { maxLength: 500 })
    .string('author', { maxLength: 255 })
    
    .number('category_id', { min: 1, integer: true })
    .number('min_rating', { min: 1, max: 5, integer: true })
    .number('page', { min: 1, integer: true })
    .number('limit', { min: 1, max: 100, integer: true })
    
    .oneOf('sort_by', ['downloads', 'rating', 'updated', 'alphabetical', 'newest'])
    .oneOf('sort_order', ['asc', 'desc'])
    
    .boolean('include_installed')
    .boolean('include_experimental')
    
    .array('tags', { maxLength: 10 })
    
    .custom('tags', (value) => {
      if (value && Array.isArray(value) && value.some((tag: unknown) => typeof tag !== 'string')) {
        return 'all tags must be strings';
      }
      return null;
    })
    
    .result();
}

export function validateAgentInstallRequest(data: Partial<AgentInstallRequest>): ValidationResult<AgentInstallRequest> {
  return new Validator(data as AgentInstallRequest)
    .required('remote_id')
    .required('source')
    
    .string('remote_id', { minLength: 1, maxLength: 255 })
    .oneOf('source', ['github', 'marketplace'])
    
    .boolean('force_reinstall')
    .boolean('install_dependencies')
    
    .result();
}

export function validateUserPreference(data: Partial<UserPreference>): ValidationResult<UserPreference> {
  return new Validator(data as UserPreference)
    .required('key')
    .required('value')
    .required('value_type')
    
    .string('key', { minLength: 1, maxLength: 255, pattern: /^[a-z_][a-z0-9_]*$/ })
    .string('value')
    .oneOf('value_type', ['string', 'number', 'boolean', 'json'])
    
    .custom('value', (value) => {
      const valueType = (data as UserPreference).value_type;
      if (valueType === 'json') {
        try {
          JSON.parse(value as string);
        } catch {
          return 'value must be valid JSON when value_type is json';
        }
      }
      return null;
    })
    
    .result();
}

export function validateMarketplaceConfig(data: Partial<MarketplaceConfig>): ValidationResult<MarketplaceConfig> {
  return new Validator(data as MarketplaceConfig)
    .boolean('enabled')
    .boolean('auto_update_agents')
    .boolean('download_confirmations')
    .boolean('show_experimental')
    .boolean('anonymous_analytics')
    
    .number('cache_expiry_hours', { min: 1, max: 168, integer: true }) // Max 1 week
    
    .oneOf('default_sort_order', ['downloads', 'rating', 'updated', 'alphabetical'])
    
    .array('preferred_categories')
    
    .string('github_token', { maxLength: 255 })
    .string('github_repo', { minLength: 1, maxLength: 255 })
    .string('github_branch', { minLength: 1, maxLength: 100 })
    .string('marketplace_api_url', { maxLength: 500 })
    .string('marketplace_api_key', { maxLength: 255 })
    
    .url('marketplace_api_url')
    
    .custom('github_repo', (value) => {
      if (value && typeof value === 'string' && !/^[a-zA-Z0-9_.-]+\/[a-zA-Z0-9_.-]+$/.test(value)) {
        return 'github_repo must be in format owner/repo';
      }
      return null;
    })
    
    .result();
}

// ============================================================================
// Bulk Validation Utilities
// ============================================================================

export function validateMarketplaceAgents(agents: Partial<MarketplaceAgent>[]): {
  valid: MarketplaceAgent[];
  invalid: Array<{ agent: Partial<MarketplaceAgent>; errors: ValidationError[] }>;
  warnings: ValidationWarning[];
} {
  const valid: MarketplaceAgent[] = [];
  const invalid: Array<{ agent: Partial<MarketplaceAgent>; errors: ValidationError[] }> = [];
  const warnings: ValidationWarning[] = [];

  for (const agent of agents) {
    const result = validateMarketplaceAgent(agent);
    warnings.push(...result.warnings);
    
    if (result.success && result.data) {
      valid.push(result.data);
    } else {
      invalid.push({ agent, errors: result.errors });
    }
  }

  return { valid, invalid, warnings };
}

// ============================================================================
// Validation Middleware for API Calls
// ============================================================================

export function createValidationMiddleware<T>(
  validator: (data: Partial<T>) => ValidationResult<T>
) {
  return (data: Partial<T>) => {
    const result = validator(data);
    if (!result.success) {
      throw new Error(`Validation failed: ${result.errors.map(e => e.message).join(', ')}`);
    }
    return result.data!;
  };
}

// Pre-configured validation functions for common use cases
export const validateAndThrow = {
  marketplaceAgent: createValidationMiddleware(validateMarketplaceAgent),
  agentCategory: createValidationMiddleware(validateAgentCategory),
  agentRating: createValidationMiddleware(validateAgentRating),
  agentDependency: createValidationMiddleware(validateAgentDependency),
  agentCollection: createValidationMiddleware(validateAgentCollection),
  searchParams: createValidationMiddleware(validateMarketplaceSearchParams),
  installRequest: createValidationMiddleware(validateAgentInstallRequest),
  userPreference: createValidationMiddleware(validateUserPreference),
  config: createValidationMiddleware(validateMarketplaceConfig)
};