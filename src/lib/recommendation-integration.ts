/**
 * Recommendation System Integration
 * Initializes and coordinates all recommendation components
 */

import { invoke } from "@tauri-apps/api/core";
import { useRecommendationTracking, useInteractionTracker } from './recommendation-tracking';
import { recommendationAPI } from './recommendation-api';
import { featureDiscovery } from './feature-discovery';
import { abTestingService } from './ab-testing';
import { usePreferenceLearning } from './preference-learning';

/**
 * Initialize the recommendation system
 */
export const initializeRecommendationSystem = async (): Promise<void> => {
  try {
    console.log('🚀 Initializing Recommendation System...');

    // Run database migrations
    await runDatabaseMigrations();

    // Initialize feature flags
    await initializeFeatureFlags();

    // Initialize tracking
    const tracking = useRecommendationTracking.getState();
    tracking._startAutoFlush();

    // Initialize preference learning
    const preferences = usePreferenceLearning.getState();
    const userHash = tracking.currentSessionHash;
    await preferences.initializeModel(userHash);

    console.log('✅ Recommendation System initialized successfully');
  } catch (error) {
    console.error('❌ Failed to initialize Recommendation System:', error);
    throw error;
  }
};

/**
 * Run database migrations for recommendation system
 */
const runDatabaseMigrations = async (): Promise<void> => {
  try {
    // Check if migrations need to be run
    const migrationsRun = await invoke('check_recommendation_migrations');
    
    if (!migrationsRun) {
      console.log('🔄 Running recommendation system database migrations...');
      await invoke('run_recommendation_migrations');
      console.log('✅ Database migrations completed');
    }
  } catch (error) {
    console.warn('⚠️ Could not run migrations, they may need to be run manually:', error);
  }
};

/**
 * Initialize feature flags for recommendation system
 */
const initializeFeatureFlags = async (): Promise<void> => {
  try {
    // Set default feature flags
    abTestingService.setFeatureFlag('enable_agent_recommendations', true);
    abTestingService.setFeatureFlag('enable_workflow_suggestions', true);
    abTestingService.setFeatureFlag('enable_collaborative_filtering', false); // Gradual rollout
    abTestingService.setFeatureFlag('recommendation_refresh_interval', 300); // 5 minutes
    abTestingService.setFeatureFlag('max_recommendations_per_session', 5);
    abTestingService.setFeatureFlag('enable_recommendation_explanations', true);
    abTestingService.setFeatureFlag('enable_feature_discovery_nudges', true);
  } catch (error) {
    console.warn('⚠️ Could not initialize feature flags:', error);
  }
};

/**
 * Enhanced recommendation tracker with automatic context detection
 */
export class SmartRecommendationTracker {
  private tracker: ReturnType<typeof useInteractionTracker>;
  private lastProjectContext: Record<string, any> = {};

  constructor() {
    this.tracker = useInteractionTracker();
  }

  /**
   * Track agent execution with enhanced context
   */
  async trackAgentExecution(
    agentId: string, 
    success: boolean, 
    duration?: number, 
    tokens?: number,
    projectContext?: Record<string, any>
  ): Promise<void> {
    const context = {
      ...this.getEnhancedContext(),
      ...projectContext,
      executionSuccess: success,
      tokensUsed: tokens
    };

    await this.tracker.trackAgentExecution(agentId, success, duration, tokens);
    
    // Track recommendation feedback if this was from a recommendation
    await this.trackPotentialRecommendationOutcome(agentId, success, context);
  }

  /**
   * Track marketplace interactions with context
   */
  async trackMarketplaceInteraction(
    action: 'browse' | 'search' | 'install' | 'rate',
    agentId?: string,
    searchQuery?: string,
    rating?: number
  ): Promise<void> {
    const context = this.getEnhancedContext();

    switch (action) {
      case 'browse':
        await this.tracker.trackMarketplaceBrowse(undefined, searchQuery);
        break;
      case 'search':
        await this.tracker.trackMarketplaceBrowse(undefined, searchQuery);
        break;
      case 'install':
        if (agentId) {
          await this.tracker.trackAgentInstall(agentId, 'marketplace');
        }
        break;
      case 'rate':
        if (agentId && rating) {
          await this.tracker.trackInteraction({
            interactionType: 'marketplace_rate',
            featureId: agentId,
            featureCategory: 'marketplace',
            successIndicator: rating >= 3,
            userRating: rating,
            contextData: context
          });
        }
        break;
    }
  }

  /**
   * Track feature usage with automatic discovery detection
   */
  async trackFeatureUsage(
    featureName: string,
    category: string,
    success: boolean = true,
    duration?: number
  ): Promise<void> {
    const context = this.getEnhancedContext();
    
    await this.tracker.trackFeatureUse(featureName, category as any, {
      ...context,
      featureSuccess: success,
      usageDuration: duration
    });

    // Check if this feature was recently recommended
    await this.trackFeatureDiscoveryOutcome(featureName, success, context);
  }

  /**
   * Track session events for recommendation context
   */
  async trackSessionEvent(
    eventType: 'start' | 'end' | 'pause' | 'resume',
    sessionId: string,
    projectId?: string,
    context?: Record<string, any>
  ): Promise<void> {
    const enhancedContext = {
      ...this.getEnhancedContext(),
      ...context
    };

    if (eventType === 'start' && projectId) {
      await this.tracker.trackSessionStart(projectId, sessionId);
      this.updateProjectContext(enhancedContext);
    }

    await this.tracker.trackInteraction({
      interactionType: eventType === 'start' ? 'session_start' : 'session_end',
      featureId: sessionId,
      featureCategory: 'session_management',
      projectId,
      sessionId,
      successIndicator: true,
      contextData: enhancedContext
    });
  }

  /**
   * Get enhanced context for interactions
   */
  private getEnhancedContext(): Record<string, any> {
    return {
      timestamp: new Date().toISOString(),
      sessionDuration: this.getSessionDuration(),
      userAgent: navigator.userAgent,
      screenResolution: `${screen.width}x${screen.height}`,
      timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      language: navigator.language,
      ...this.lastProjectContext
    };
  }

  /**
   * Update project context for future interactions
   */
  private updateProjectContext(context: Record<string, any>): void {
    this.lastProjectContext = {
      projectType: context.projectType,
      fileExtensions: context.fileExtensions,
      projectPath: context.projectPath,
      lastActiveTime: new Date().toISOString()
    };
  }

  /**
   * Get current session duration
   */
  private getSessionDuration(): number {
    const tracking = useRecommendationTracking.getState();
    return (Date.now() - tracking.sessionStartTime.getTime()) / 1000;
  }

  /**
   * Track potential recommendation outcome
   */
  private async trackPotentialRecommendationOutcome(
    itemId: string,
    success: boolean,
    context: Record<string, any>
  ): Promise<void> {
    // Check if this agent was recently recommended
    // This would require checking recent recommendations cache
    // For now, we'll track it as potential recommendation feedback
    
    await this.tracker.trackInteraction({
      interactionType: 'recommendation_feedback',
      featureId: itemId,
      featureCategory: 'agent',
      successIndicator: success,
      contextData: {
        ...context,
        feedbackType: 'implicit_outcome',
        outcomeSuccess: success
      }
    });
  }

  /**
   * Track feature discovery outcome
   */
  private async trackFeatureDiscoveryOutcome(
    featureName: string,
    success: boolean,
    context: Record<string, any>
  ): Promise<void> {
    await this.tracker.trackInteraction({
      interactionType: 'feature_discovery_outcome',
      featureId: featureName,
      featureCategory: 'feature',
      successIndicator: success,
      contextData: {
        ...context,
        discoverySuccess: success,
        discoveryMethod: 'recommendation' // Could be 'exploration', 'documentation', etc.
      }
    });
  }
}

/**
 * Recommendation system health checker
 */
export const checkRecommendationSystemHealth = async (): Promise<{
  status: 'healthy' | 'degraded' | 'unhealthy';
  components: Record<string, boolean>;
  issues: string[];
}> => {
  const health = {
    status: 'healthy' as const,
    components: {
      database: false,
      tracking: false,
      recommendations: false,
      featureDiscovery: false,
      preferences: false
    },
    issues: [] as string[]
  };

  try {
    // Check database connectivity
    try {
      await invoke('bulk_insert_user_interactions', { interactions: [] });
      health.components.database = true;
    } catch (error) {
      health.issues.push('Database connectivity failed');
    }

    // Check tracking system
    try {
      const tracking = useRecommendationTracking.getState();
      health.components.tracking = tracking.trackingEnabled;
      if (!tracking.trackingEnabled) {
        health.issues.push('Tracking is disabled');
      }
    } catch (error) {
      health.issues.push('Tracking system error');
    }

    // Check recommendation API
    try {
      await recommendationAPI.getUserProfile();
      health.components.recommendations = true;
    } catch (error) {
      health.issues.push('Recommendation API error');
    }

    // Check feature discovery
    try {
      const features = featureDiscovery.getAllFeatures();
      health.components.featureDiscovery = features.length > 0;
    } catch (error) {
      health.issues.push('Feature discovery error');
    }

    // Check preference learning
    try {
      const preferences = usePreferenceLearning.getState();
      health.components.preferences = preferences.userModel !== null;
    } catch (error) {
      health.issues.push('Preference learning error');
    }

    // Determine overall status
    const healthyComponents = Object.values(health.components).filter(Boolean).length;
    const totalComponents = Object.keys(health.components).length;
    
    if (healthyComponents === totalComponents) {
      health.status = 'healthy';
    } else if (healthyComponents >= totalComponents * 0.6) {
      health.status = 'degraded';
    } else {
      health.status = 'unhealthy';
    }

  } catch (error) {
    health.status = 'unhealthy';
    health.issues.push(`System check failed: ${error}`);
  }

  return health;
};

/**
 * Export enhanced tracker instance
 */
export const smartTracker = new SmartRecommendationTracker();