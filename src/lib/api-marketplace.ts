/**
 * Marketplace API Extensions for <PERSON>
 * Extends the existing API client with marketplace functionality
 */

import { invoke } from "@tauri-apps/api/core";
import type {
  MarketplaceAgent,
  AgentCategory,
  AgentRating,
  AgentDependency,
  AgentCollection,
  MarketplaceSearchParams,
  MarketplaceSearchResult,
  AgentInstallRequest,
  AgentInstallResult,
  AgentRatingSubmission,
  AgentSyncRequest,
  AgentSyncResult,
  UserPreference,
  MarketplaceConfig,
  MarketplaceStats,
  EnhancedGitHubAgentFile,
  MarketplaceAgentExport,
  AgentComparison
} from '@/types/marketplace';
import { validateAndThrow } from '@/lib/validation/marketplace';

/**
 * Extended API client with marketplace functionality
 * Follows existing patterns from the main API client
 */
export const marketplaceApi = {
  // ============================================================================
  // Agent Discovery and Search
  // ============================================================================

  /**
   * Search marketplace agents with filtering and sorting
   */
  async searchMarketplaceAgents(params: MarketplaceSearchParams): Promise<MarketplaceSearchResult> {
    try {
      const validatedParams = validateAndThrow.searchParams(params);
      return await invoke<MarketplaceSearchResult>('marketplace_search_agents', validatedParams as any);
    } catch (error) {
      console.error("Failed to search marketplace agents:", error);
      throw error;
    }
  },

  /**
   * Get featured marketplace agents
   */
  async getFeaturedAgents(limit: number = 10): Promise<MarketplaceAgent[]> {
    try {
      return await invoke<MarketplaceAgent[]>('marketplace_get_featured_agents', { limit });
    } catch (error) {
      console.error("Failed to get featured agents:", error);
      throw error;
    }
  },

  /**
   * Get marketplace agent by remote ID
   */
  async getMarketplaceAgent(remoteId: string): Promise<MarketplaceAgent> {
    try {
      return await invoke<MarketplaceAgent>('marketplace_get_agent', { remoteId });
    } catch (error) {
      console.error("Failed to get marketplace agent:", error);
      throw error;
    }
  },

  /**
   * Get agent suggestions based on installed agents
   */
  async getAgentSuggestions(limit: number = 5): Promise<MarketplaceAgent[]> {
    try {
      return await invoke<MarketplaceAgent[]>('marketplace_get_suggestions', { limit });
    } catch (error) {
      console.error("Failed to get agent suggestions:", error);
      throw error;
    }
  },

  /**
   * Compare multiple agents side by side
   */
  async compareAgents(remoteIds: string[]): Promise<AgentComparison> {
    try {
      return await invoke<AgentComparison>('marketplace_compare_agents', { remoteIds });
    } catch (error) {
      console.error("Failed to compare agents:", error);
      throw error;
    }
  },

  // ============================================================================
  // Agent Installation and Management
  // ============================================================================

  /**
   * Install an agent from the marketplace
   */
  async installAgent(request: AgentInstallRequest): Promise<AgentInstallResult> {
    try {
      const validatedRequest = validateAndThrow.installRequest(request);
      return await invoke<AgentInstallResult>('marketplace_install_agent', validatedRequest as any);
    } catch (error) {
      console.error("Failed to install marketplace agent:", error);
      throw error;
    }
  },

  /**
   * Uninstall a marketplace agent
   */
  async uninstallAgent(remoteId: string, removeData: boolean = false): Promise<{ success: boolean; message: string }> {
    try {
      return await invoke<{ success: boolean; message: string }>('marketplace_uninstall_agent', { remoteId, removeData });
    } catch (error) {
      console.error("Failed to uninstall agent:", error);
      throw error;
    }
  },

  /**
   * Update an installed agent to the latest version
   */
  async updateAgent(remoteId: string): Promise<AgentInstallResult> {
    try {
      return await invoke<AgentInstallResult>('marketplace_update_agent', { remoteId });
    } catch (error) {
      console.error("Failed to update agent:", error);
      throw error;
    }
  },

  /**
   * Check for available updates for installed agents
   */
  async checkForUpdates(): Promise<Array<{ agent: MarketplaceAgent; newVersion: string; currentVersion: string }>> {
    try {
      return await invoke('marketplace_check_updates');
    } catch (error) {
      console.error("Failed to check for updates:", error);
      throw error;
    }
  },

  /**
   * Bulk update all installed agents
   */
  async updateAllAgents(): Promise<Array<{ remoteId: string; success: boolean; error?: string }>> {
    try {
      return await invoke('marketplace_update_all_agents');
    } catch (error) {
      console.error("Failed to update all agents:", error);
      throw error;
    }
  },

  // ============================================================================
  // Agent Categories
  // ============================================================================

  /**
   * List all agent categories
   */
  async getCategories(includeInactive: boolean = false): Promise<AgentCategory[]> {
    try {
      return await invoke<AgentCategory[]>('marketplace_get_categories', { includeInactive });
    } catch (error) {
      console.error("Failed to get categories:", error);
      throw error;
    }
  },

  /**
   * Get category by ID with agent count
   */
  async getCategory(id: number): Promise<AgentCategory> {
    try {
      return await invoke<AgentCategory>('marketplace_get_category', { id });
    } catch (error) {
      console.error("Failed to get category:", error);
      throw error;
    }
  },

  /**
   * Get agents in a specific category
   */
  async getAgentsByCategory(categoryId: number, params?: Partial<MarketplaceSearchParams>): Promise<MarketplaceSearchResult> {
    try {
      const searchParams = { ...params, category_id: categoryId };
      return await this.searchMarketplaceAgents(searchParams);
    } catch (error) {
      console.error("Failed to get agents by category:", error);
      throw error;
    }
  },

  // ============================================================================
  // Agent Ratings and Reviews
  // ============================================================================

  /**
   * Submit a rating and review for an agent
   */
  async submitRating(rating: AgentRatingSubmission): Promise<AgentRating> {
    try {
      // For now, just return a mock rating since the command isn't implemented yet
      return {
        id: Date.now(),
        marketplace_agent_id: 1,
        user_identifier: 'anonymous',
        rating: rating.rating,
        review_title: rating.review_title || undefined,
        review_content: rating.review_content || undefined,
        is_verified: false,
        helpful_votes: 0,
        total_votes: 0,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
    } catch (error) {
      console.error("Failed to submit rating:", error);
      throw error;
    }
  },

  /**
   * Get ratings for a specific agent
   */
  async getAgentRatings(_remoteId: string, _page: number = 1, _limit: number = 20): Promise<{
    ratings: AgentRating[];
    total: number;
    average: number;
    distribution: Record<1 | 2 | 3 | 4 | 5, number>;
  }> {
    try {
      // For now, return empty ratings since the command isn't implemented yet
      return {
        ratings: [],
        total: 0,
        average: 0,
        distribution: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 }
      };
    } catch (error) {
      console.error("Failed to get agent ratings:", error);
      throw error;
    }
  },

  /**
   * Update an existing rating
   */
  async updateRating(ratingId: number, updates: Partial<AgentRatingSubmission>): Promise<AgentRating> {
    try {
      return await invoke<AgentRating>('marketplace_update_rating', { ratingId, updates });
    } catch (error) {
      console.error("Failed to update rating:", error);
      throw error;
    }
  },

  /**
   * Delete a rating
   */
  async deleteRating(ratingId: number): Promise<{ success: boolean; message: string }> {
    try {
      return await invoke('marketplace_delete_rating', { ratingId });
    } catch (error) {
      console.error("Failed to delete rating:", error);
      throw error;
    }
  },

  /**
   * Vote on a review's helpfulness
   */
  async voteOnReview(ratingId: number, helpful: boolean): Promise<{ success: boolean }> {
    try {
      return await invoke('marketplace_vote_review', { ratingId, helpful });
    } catch (error) {
      console.error("Failed to vote on review:", error);
      throw error;
    }
  },

  // ============================================================================
  // Agent Collections
  // ============================================================================

  /**
   * Get featured collections
   */
  async getFeaturedCollections(): Promise<AgentCollection[]> {
    try {
      // For now, return empty array since the command isn't implemented yet
      return [];
    } catch (error) {
      console.error("Failed to get featured collections:", error);
      throw error;
    }
  },

  /**
   * Get all public collections
   */
  async getCollections(page: number = 1, limit: number = 20): Promise<{
    collections: AgentCollection[];
    total: number;
    page: number;
    limit: number;
  }> {
    try {
      return await invoke('marketplace_get_collections', { page, limit });
    } catch (error) {
      console.error("Failed to get collections:", error);
      throw error;
    }
  },

  /**
   * Get collection details with agents
   */
  async getCollection(id: number): Promise<AgentCollection & { agents: MarketplaceAgent[] }> {
    try {
      return await invoke('marketplace_get_collection', { id });
    } catch (error) {
      console.error("Failed to get collection:", error);
      throw error;
    }
  },

  // ============================================================================
  // Data Synchronization
  // ============================================================================

  /**
   * Sync agents from GitHub repository
   */
  async syncFromGitHub(request: AgentSyncRequest): Promise<AgentSyncResult> {
    try {
      return await invoke<AgentSyncResult>('marketplace_sync_github', request as any);
    } catch (error) {
      console.error("Failed to sync from GitHub:", error);
      throw error;
    }
  },

  /**
   * Sync agents from marketplace API
   */
  async syncFromMarketplace(request: AgentSyncRequest): Promise<AgentSyncResult> {
    try {
      return await invoke<AgentSyncResult>('marketplace_sync_api', request as any);
    } catch (error) {
      console.error("Failed to sync from marketplace:", error);
      throw error;
    }
  },

  /**
   * Get sync status
   */
  async getSyncStatus(): Promise<{
    lastSync: string;
    inProgress: boolean;
    nextScheduledSync?: string;
  }> {
    try {
      return await invoke('marketplace_get_sync_status');
    } catch (error) {
      console.error("Failed to get sync status:", error);
      throw error;
    }
  },

  // ============================================================================
  // Enhanced GitHub Integration
  // ============================================================================

  /**
   * Fetch enhanced GitHub agent files with metadata
   */
  async fetchEnhancedGitHubAgents(): Promise<EnhancedGitHubAgentFile[]> {
    try {
      return await invoke<EnhancedGitHubAgentFile[]>('marketplace_fetch_enhanced_github_agents');
    } catch (error) {
      console.error("Failed to fetch enhanced GitHub agents:", error);
      throw error;
    }
  },

  /**
   * Validate GitHub agent file
   */
  async validateGitHubAgent(downloadUrl: string): Promise<{
    valid: boolean;
    metadata: MarketplaceAgentExport;
    issues: string[];
  }> {
    try {
      return await invoke('marketplace_validate_github_agent', { downloadUrl });
    } catch (error) {
      console.error("Failed to validate GitHub agent:", error);
      throw error;
    }
  },

  // ============================================================================
  // User Preferences and Configuration
  // ============================================================================

  /**
   * Get marketplace configuration
   */
  async getConfig(): Promise<MarketplaceConfig> {
    try {
      return await invoke<MarketplaceConfig>('marketplace_get_config');
    } catch (error) {
      console.error("Failed to get marketplace config:", error);
      throw error;
    }
  },

  /**
   * Update marketplace configuration
   */
  async updateConfig(config: Partial<MarketplaceConfig>): Promise<MarketplaceConfig> {
    try {
      const validatedConfig = validateAndThrow.config(config);
      return await invoke<MarketplaceConfig>('marketplace_update_config', validatedConfig as any);
    } catch (error) {
      console.error("Failed to update marketplace config:", error);
      throw error;
    }
  },

  /**
   * Get specific user preference
   */
  async getUserPreference(key: string): Promise<UserPreference | null> {
    try {
      return await invoke<UserPreference | null>('marketplace_get_preference', { key });
    } catch (error) {
      console.error("Failed to get user preference:", error);
      throw error;
    }
  },

  /**
   * Set user preference
   */
  async setUserPreference(key: string, value: string, valueType: 'string' | 'number' | 'boolean' | 'json' = 'string'): Promise<UserPreference> {
    try {
      const preference = { key, value, value_type: valueType };
      const validatedPreference = validateAndThrow.userPreference(preference);
      return await invoke<UserPreference>('marketplace_set_preference', validatedPreference as any);
    } catch (error) {
      console.error("Failed to set user preference:", error);
      throw error;
    }
  },

  // ============================================================================
  // Analytics and Statistics
  // ============================================================================

  /**
   * Get marketplace statistics
   */
  async getStats(): Promise<MarketplaceStats> {
    try {
      return await invoke<MarketplaceStats>('marketplace_get_stats');
    } catch (error) {
      console.error("Failed to get marketplace stats:", error);
      throw error;
    }
  },

  /**
   * Get popular tags
   */
  async getPopularTags(limit: number = 20): Promise<Array<{ tag: string; count: number }>> {
    try {
      return await invoke('marketplace_get_popular_tags', { limit });
    } catch (error) {
      console.error("Failed to get popular tags:", error);
      throw error;
    }
  },

  /**
   * Get trending agents (popular in last 7 days)
   */
  async getTrendingAgents(limit: number = 10): Promise<MarketplaceAgent[]> {
    try {
      return await invoke<MarketplaceAgent[]>('marketplace_get_trending', { limit });
    } catch (error) {
      console.error("Failed to get trending agents:", error);
      throw error;
    }
  },

  /**
   * Record agent download (for analytics)
   */
  async recordDownload(remoteId: string, source: 'github' | 'marketplace'): Promise<void> {
    try {
      await invoke('marketplace_record_download', { remoteId, source });
    } catch (error) {
      console.error("Failed to record download:", error);
      // Don't throw - analytics failures shouldn't block functionality
    }
  },

  // ============================================================================
  // Cache Management
  // ============================================================================

  /**
   * Clear marketplace cache
   */
  async clearCache(cacheType?: string): Promise<{ clearedCount: number }> {
    try {
      return await invoke('marketplace_clear_cache', { cacheType });
    } catch (error) {
      console.error("Failed to clear cache:", error);
      throw error;
    }
  },

  /**
   * Get cache status
   */
  async getCacheStatus(): Promise<{
    totalEntries: number;
    totalSize: string;
    oldestEntry: string;
    newestEntry: string;
    typeBreakdown: Record<string, number>;
  }> {
    try {
      return await invoke('marketplace_get_cache_status');
    } catch (error) {
      console.error("Failed to get cache status:", error);
      throw error;
    }
  },

  /**
   * Preload cache with essential data
   */
  async preloadCache(): Promise<{ success: boolean; itemsLoaded: number }> {
    try {
      return await invoke('marketplace_preload_cache');
    } catch (error) {
      console.error("Failed to preload cache:", error);
      throw error;
    }
  },

  // ============================================================================
  // Dependency Management
  // ============================================================================

  /**
   * Get agent dependencies
   */
  async getAgentDependencies(remoteId: string): Promise<AgentDependency[]> {
    try {
      return await invoke<AgentDependency[]>('marketplace_get_dependencies', { remoteId });
    } catch (error) {
      console.error("Failed to get agent dependencies:", error);
      throw error;
    }
  },

  /**
   * Check dependency satisfaction
   */
  async checkDependencies(remoteId: string): Promise<{
    satisfied: AgentDependency[];
    unsatisfied: AgentDependency[];
    installable: AgentDependency[];
  }> {
    try {
      return await invoke('marketplace_check_dependencies', { remoteId });
    } catch (error) {
      console.error("Failed to check dependencies:", error);
      throw error;
    }
  },

  /**
   * Install agent dependencies
   */
  async installDependencies(remoteId: string, dependencyTypes?: string[]): Promise<{
    installed: string[];
    failed: Array<{ name: string; error: string }>;
    skipped: string[];
  }> {
    try {
      return await invoke('marketplace_install_dependencies', { remoteId, dependencyTypes });
    } catch (error) {
      console.error("Failed to install dependencies:", error);
      throw error;
    }
  },

  // ============================================================================
  // Import/Export Extensions
  // ============================================================================

  /**
   * Export marketplace agent with enhanced metadata
   */
  async exportMarketplaceAgent(remoteId: string): Promise<string> {
    try {
      return await invoke<string>('marketplace_export_agent', { remoteId });
    } catch (error) {
      console.error("Failed to export marketplace agent:", error);
      throw error;
    }
  },

  /**
   * Import marketplace agent from enhanced export
   */
  async importMarketplaceAgent(jsonData: string): Promise<MarketplaceAgent> {
    try {
      return await invoke<MarketplaceAgent>('marketplace_import_agent', { jsonData });
    } catch (error) {
      console.error("Failed to import marketplace agent:", error);
      throw error;
    }
  },

  /**
   * Bulk export installed agents
   */
  async exportInstalledAgents(includeLocalOnly: boolean = false): Promise<string> {
    try {
      return await invoke<string>('marketplace_export_installed', { includeLocalOnly });
    } catch (error) {
      console.error("Failed to export installed agents:", error);
      throw error;
    }
  }
};

// ============================================================================
// Utility Functions
// ============================================================================

/**
 * Type-safe API wrapper with validation
 */
export function createMarketplaceApiWrapper() {
  return new Proxy(marketplaceApi, {
    get(target, prop) {
      const originalMethod = target[prop as keyof typeof target];
      if (typeof originalMethod === 'function') {
        return async (...args: unknown[]) => {
          try {
            return await (originalMethod as any).apply(target, args);
          } catch (error) {
            // Enhanced error handling for marketplace operations
            if (error instanceof Error) {
              if (error.message.includes('network')) {
                throw new Error(`Network error: ${error.message}. Please check your internet connection.`);
              }
              if (error.message.includes('validation')) {
                throw new Error(`Invalid data: ${error.message}`);
              }
              if (error.message.includes('permission')) {
                throw new Error(`Permission denied: ${error.message}. Please check your API credentials.`);
              }
            }
            throw error;
          }
        };
      }
      return originalMethod;
    }
  });
}

/**
 * Cached API wrapper for performance
 */
export function createCachedMarketplaceApi(cacheTimeMs: number = 300000) { // 5 minutes default
  const cache = new Map<string, { data: unknown; timestamp: number }>();
  
  return new Proxy(marketplaceApi, {
    get(target, prop) {
      const originalMethod = target[prop as keyof typeof target];
      if (typeof originalMethod === 'function') {
        return async (...args: unknown[]) => {
          // Only cache read operations
          const isReadOperation = String(prop).startsWith('get') || 
                                  String(prop).startsWith('search') || 
                                  String(prop).startsWith('fetch');
          
          if (isReadOperation) {
            const cacheKey = `${String(prop)}:${JSON.stringify(args)}`;
            const cached = cache.get(cacheKey);
            
            if (cached && Date.now() - cached.timestamp < cacheTimeMs) {
              return cached.data;
            }
            
            const result = await (originalMethod as any).apply(target, args);
            cache.set(cacheKey, { data: result, timestamp: Date.now() });
            return result;
          }
          
          return await (originalMethod as any).apply(target, args);
        };
      }
      return originalMethod;
    }
  });
}

// Export default instance with validation
// Export individual functions for easier importing
export const {
  searchMarketplaceAgents,
  getFeaturedAgents,
  getMarketplaceAgent,
  getAgentSuggestions,
  compareAgents,
  installAgent,
  uninstallAgent,
  updateAgent,
  checkForUpdates,
  updateAllAgents,
  getCategories,
  getCategory,
  getAgentsByCategory,
  submitRating,
  getAgentRatings,
  updateRating,
  deleteRating,
  voteOnReview,
  getFeaturedCollections,
  getCollections,
  getCollection,
  syncFromGitHub,
  syncFromMarketplace,
  getSyncStatus,
  fetchEnhancedGitHubAgents,
  validateGitHubAgent,
  getConfig,
  updateConfig,
  getUserPreference,
  setUserPreference,
  getStats,
  getPopularTags,
  getTrendingAgents,
  recordDownload,
  clearCache,
  getCacheStatus,
  preloadCache,
  getAgentDependencies,
  checkDependencies,
  installDependencies,
  exportMarketplaceAgent,
  importMarketplaceAgent,
  exportInstalledAgents
} = marketplaceApi;

export default createMarketplaceApiWrapper();