import { invoke } from '@tauri-apps/api/core';

export interface GitHubRepo {
  id: number;
  name: string;
  full_name: string;
  owner: {
    login: string;
    avatar_url: string;
    html_url?: string;
    type?: string;
  };
  description: string | null;
  html_url: string;
  stargazers_count: number;
  forks_count: number;
  language: string | null;
  topics: string[];
  created_at: string;
  updated_at: string;
  size?: number;
  open_issues_count?: number;
  license?: {
    key: string;
    name: string;
    spdx_id: string;
  };
  default_branch?: string;
  archived?: boolean;
  disabled?: boolean;
  private?: boolean;
}

export interface TrendingRepo extends GitHubRepo {
  stars_today?: number;
  rank?: number;
  trend_score?: number;
  momentum?: 'rising' | 'stable' | 'declining';
  stars_gained_week?: number;
  forks_gained_week?: number;
}

export interface GitHubDeveloper {
  login: string;
  id: number;
  avatar_url: string;
  html_url: string;
  name?: string;
  company?: string;
  location?: string;
  email?: string;
  bio?: string;
  public_repos: number;
  public_gists: number;
  followers: number;
  following: number;
  created_at: string;
  updated_at: string;
  blog?: string;
  twitter_username?: string;
  repos_contributed?: TrendingRepo[];
  total_stars?: number;
  rank?: number;
}

export interface GitHubTopic {
  name: string;
  display_name?: string;
  short_description?: string;
  description?: string;
  created_by?: string;
  featured?: boolean;
  curated?: boolean;
  score?: number;
  repo_count?: number;
  trending_repos?: TrendingRepo[];
}

/**
 * Fetches trending GitHub repositories
 * Uses GitHub's search API to find popular repos from the last week
 */
export async function fetchTrendingRepos(
  timeRange: 'daily' | 'weekly' | 'monthly' = 'weekly',
  language?: string
): Promise<TrendingRepo[]> {
  try {
    // Calculate date range
    const date = new Date();
    if (timeRange === 'daily') {
      date.setDate(date.getDate() - 1);
    } else if (timeRange === 'weekly') {
      date.setDate(date.getDate() - 7);
    } else {
      date.setMonth(date.getMonth() - 1);
    }
    
    const dateString = date.toISOString().split('T')[0];
    
    // Build query - search for repos created after date, sorted by stars
    let query = `created:>${dateString}`;
    if (language && language !== 'all') {
      query += ` language:"${language}"`;
    }
    
    // Use Tauri command to make HTTP request
    const response = await invoke<any>('fetch_github_trending', {
      query,
      sort: 'stars',
      order: 'desc',
      perPage: 30
    });
    
    // Process and rank repos
    const repos: TrendingRepo[] = response.items.map((repo: GitHubRepo, index: number) => {
      // Calculate approximate stars gained (based on creation date)
      const createdDate = new Date(repo.created_at);
      const daysOld = Math.floor((Date.now() - createdDate.getTime()) / (1000 * 60 * 60 * 24));
      const starsPerDay = daysOld > 0 ? Math.floor(repo.stargazers_count / daysOld) : repo.stargazers_count;
      
      return {
        ...repo,
        rank: index + 1,
        stars_today: starsPerDay
      };
    });
    
    return repos;
  } catch (error) {
    console.error('Failed to fetch trending repos:', error);
    // For now, return mock data as fallback
    // In production, you might want to throw the error instead
    return getMockTrendingRepos();
  }
}

/**
 * Fetches trending repositories using a more sophisticated approach
 * Combines multiple queries to find truly trending repositories
 */
export async function fetchTrendingReposEnhanced(
  timeRange: 'daily' | 'weekly' | 'monthly' = 'weekly',
  language?: string
): Promise<TrendingRepo[]> {
  try {
    // Prepare query parameters
    
    // Query 1: Recently created popular repos
    const recentDate = new Date();
    if (timeRange === 'daily') {
      recentDate.setDate(recentDate.getDate() - 1);
    } else if (timeRange === 'weekly') {
      recentDate.setDate(recentDate.getDate() - 7);
    } else {
      recentDate.setMonth(recentDate.getMonth() - 1);
    }
    
    let baseQuery = `stars:>100 pushed:>${recentDate.toISOString().split('T')[0]}`;
    if (language && language !== 'all') {
      baseQuery += ` language:"${language}"`;
    }
    
    // Query 2: Most starred repos in the time period
    const starsQuery = `${baseQuery} sort:stars`;
    
    // Query 3: Most active repos (by recent commits)
    const activityQuery = `${baseQuery} sort:updated`;
    
    // Fetch both queries
    const [starsResponse, activityResponse] = await Promise.all([
      invoke<any>('fetch_github_trending', {
        query: starsQuery,
        sort: 'stars',
        order: 'desc',
        perPage: 20
      }),
      invoke<any>('fetch_github_trending', {
        query: activityQuery,
        sort: 'updated',
        order: 'desc',
        perPage: 20
      })
    ]);
    
    // Combine and deduplicate results
    const repoMap = new Map<number, TrendingRepo>();
    
    // Process starred repos
    starsResponse.items.forEach((repo: GitHubRepo, index: number) => {
      const createdDate = new Date(repo.created_at);
      const updatedDate = new Date(repo.updated_at);
      const hoursAgo = Math.floor((Date.now() - updatedDate.getTime()) / (1000 * 60 * 60));
      const daysOld = Math.floor((Date.now() - createdDate.getTime()) / (1000 * 60 * 60 * 24));
      
      // Calculate trend score based on stars, age, and recent activity
      const starsPerDay = daysOld > 0 ? repo.stargazers_count / daysOld : repo.stargazers_count;
      const activityBoost = hoursAgo < 24 ? 2 : hoursAgo < 168 ? 1.5 : 1;
      const trendScore = starsPerDay * activityBoost;
      
      repoMap.set(repo.id, {
        ...repo,
        rank: index + 1,
        stars_today: Math.round(starsPerDay),
        trend_score: trendScore
      } as TrendingRepo & { trend_score: number });
    });
    
    // Add activity-based repos
    activityResponse.items.forEach((repo: GitHubRepo) => {
      if (!repoMap.has(repo.id) && repo.stargazers_count > 50) {
        const createdDate = new Date(repo.created_at);
        const daysOld = Math.floor((Date.now() - createdDate.getTime()) / (1000 * 60 * 60 * 24));
        const starsPerDay = daysOld > 0 ? repo.stargazers_count / daysOld : repo.stargazers_count;
        
        repoMap.set(repo.id, {
          ...repo,
          rank: 999,
          stars_today: Math.round(starsPerDay),
          trend_score: starsPerDay
        } as TrendingRepo & { trend_score: number });
      }
    });
    
    // Sort by trend score and re-rank
    const sortedRepos = Array.from(repoMap.values())
      .sort((a, b) => (b as any).trend_score - (a as any).trend_score)
      .slice(0, 30)
      .map((repo, index) => ({
        ...repo,
        rank: index + 1
      }));
    
    return sortedRepos;
  } catch (error) {
    console.error('Failed to fetch enhanced trending repos:', error);
    // Try the simple method as fallback
    return fetchTrendingRepos(timeRange, language);
  }
}

/**
 * Fetches trending developers using GitHub search API
 */
export async function fetchTrendingDevelopers(
  timeRange: 'daily' | 'weekly' | 'monthly' = 'weekly',
  language?: string,
  location?: string
): Promise<GitHubDeveloper[]> {
  try {
    const date = new Date();
    if (timeRange === 'daily') {
      date.setDate(date.getDate() - 1);
    } else if (timeRange === 'weekly') {
      date.setDate(date.getDate() - 7);
    } else {
      date.setMonth(date.getMonth() - 1);
    }
    
    const dateString = date.toISOString().split('T')[0];
    
    // Build query for users who have pushed recently
    let query = `followers:>10 repos:>5 pushed:>${dateString}`;
    if (language && language !== 'all') {
      query += ` language:"${language}"`;
    }
    if (location) {
      query += ` location:"${location}"`;
    }
    
    const response = await invoke<any>('fetch_github_users', {
      query,
      sort: 'followers',
      order: 'desc',
      perPage: 20
    });
    
    // Process developers with ranking
    const developers: GitHubDeveloper[] = response.items.map((dev: GitHubDeveloper, index: number) => {
      return {
        ...dev,
        rank: index + 1,
        total_stars: 0 // Will be calculated separately if needed
      };
    });
    
    return developers;
  } catch (error) {
    console.error('Failed to fetch trending developers:', error);
    return getMockTrendingDevelopers();
  }
}

/**
 * Fetches trending topics/technologies
 */
export async function fetchTrendingTopics(
  timeRange: 'daily' | 'weekly' | 'monthly' = 'weekly'
): Promise<GitHubTopic[]> {
  try {
    // Predefined trending topics with search for recent activity
    const popularTopics = [
      'artificial-intelligence', 'machine-learning', 'deep-learning',
      'react', 'vue', 'angular', 'svelte',
      'python', 'javascript', 'typescript', 'rust', 'go',
      'blockchain', 'cryptocurrency', 'web3',
      'docker', 'kubernetes', 'devops',
      'mobile', 'ios', 'android',
      'game-development', 'unity', 'unreal-engine'
    ];
    
    const date = new Date();
    if (timeRange === 'daily') {
      date.setDate(date.getDate() - 1);
    } else if (timeRange === 'weekly') {
      date.setDate(date.getDate() - 7);
    } else {
      date.setMonth(date.getMonth() - 1);
    }
    
    const dateString = date.toISOString().split('T')[0];
    
    // Fetch repos for each topic and calculate trend scores
    const topicPromises = popularTopics.slice(0, 10).map(async (topic) => {
      try {
        const query = `topic:"${topic}" created:>${dateString} stars:>10`;
        const response = await invoke<any>('fetch_github_trending', {
          query,
          sort: 'stars',
          order: 'desc',
          perPage: 5
        });
        
        const repoCount = response.total_count || 0;
        const topRepos: TrendingRepo[] = response.items.slice(0, 3);
        
        return {
          name: topic,
          display_name: topic.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
          repo_count: repoCount,
          trending_repos: topRepos,
          score: repoCount + topRepos.reduce((sum, repo) => sum + repo.stargazers_count, 0)
        };
      } catch {
        return {
          name: topic,
          display_name: topic.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
          repo_count: 0,
          trending_repos: [],
          score: 0
        };
      }
    });
    
    const topics = await Promise.all(topicPromises);
    return topics
      .filter(topic => topic.score > 0)
      .sort((a, b) => b.score! - a.score!)
      .slice(0, 8);
      
  } catch (error) {
    console.error('Failed to fetch trending topics:', error);
    return getMockTrendingTopics();
  }
}

/**
 * Search repositories with advanced filters
 */
export async function searchRepositories(
  query: string,
  language?: string,
  sort: 'stars' | 'forks' | 'updated' = 'stars',
  timeRange?: 'daily' | 'weekly' | 'monthly'
): Promise<TrendingRepo[]> {
  try {
    let searchQuery = query;
    
    if (language && language !== 'all') {
      searchQuery += ` language:"${language}"`;
    }
    
    if (timeRange) {
      const date = new Date();
      if (timeRange === 'daily') {
        date.setDate(date.getDate() - 1);
      } else if (timeRange === 'weekly') {
        date.setDate(date.getDate() - 7);
      } else {
        date.setMonth(date.getMonth() - 1);
      }
      const dateString = date.toISOString().split('T')[0];
      searchQuery += ` pushed:>${dateString}`;
    }
    
    const response = await invoke<any>('fetch_github_trending', {
      query: searchQuery,
      sort,
      order: 'desc',
      perPage: 20
    });
    
    return response.items.map((repo: GitHubRepo, index: number) => ({
      ...repo,
      rank: index + 1
    }));
    
  } catch (error) {
    console.error('Failed to search repositories:', error);
    return [];
  }
}

/**
 * Fetch repository statistics and momentum
 */
export async function fetchRepositoryStats(repos: TrendingRepo[]): Promise<TrendingRepo[]> {
  // For now, calculate basic momentum based on creation date and stars
  // In a real implementation, you'd fetch historical data
  return repos.map(repo => {
    const createdDate = new Date(repo.created_at);
    const updatedDate = new Date(repo.updated_at);
    const daysOld = Math.floor((Date.now() - createdDate.getTime()) / (1000 * 60 * 60 * 24));
    const hoursStale = Math.floor((Date.now() - updatedDate.getTime()) / (1000 * 60 * 60));
    
    // Calculate momentum indicators
    const starsPerDay = daysOld > 0 ? repo.stargazers_count / daysOld : repo.stargazers_count;
    const isActive = hoursStale < 168; // Updated within a week
    
    let momentum: 'rising' | 'stable' | 'declining' = 'stable';
    if (starsPerDay > 10 && isActive) {
      momentum = 'rising';
    } else if (starsPerDay < 1 || hoursStale > 720) { // No activity for a month
      momentum = 'declining';
    }
    
    return {
      ...repo,
      stars_today: Math.round(starsPerDay),
      stars_gained_week: Math.round(starsPerDay * 7),
      forks_gained_week: Math.round((repo.forks_count / daysOld) * 7),
      momentum,
      trend_score: starsPerDay * (isActive ? 2 : 1)
    };
  });
}

/**
 * Get programming languages with their colors
 */
export const LANGUAGE_COLORS: Record<string, string> = {
  TypeScript: '#3178c6',
  JavaScript: '#f1e05a',
  Python: '#3572A5',
  Java: '#b07219',
  'C++': '#f34b7d',
  C: '#555555',
  'C#': '#178600',
  Go: '#00ADD8',
  Rust: '#dea584',
  Swift: '#FA7343',
  Kotlin: '#A97BFF',
  Ruby: '#701516',
  PHP: '#4F5D95',
  HTML: '#e34c26',
  CSS: '#563d7c',
  Vue: '#41b883',
  React: '#61dafb',
  Shell: '#89e051',
  Dart: '#00B4AB',
  Scala: '#c22d40',
  Clojure: '#db5855',
  Haskell: '#5e5086',
  Lua: '#000080',
  R: '#198CE7',
  MATLAB: '#e16737',
  Objective: '#438eff',
  Perl: '#0298c3',
  PowerShell: '#012456',
  Assembly: '#6E4C13',
  Elixir: '#6e4a7e',
  Elm: '#60B5CC',
  Erlang: '#B83998',
  Julia: '#a270ba',
  Nim: '#ffc200',
  Crystal: '#000100',
  Zig: '#ec915c',
};

/**
 * Popular programming languages for filtering
 */
export const POPULAR_LANGUAGES = [
  'All',
  'JavaScript',
  'TypeScript', 
  'Python',
  'Java',
  'C++',
  'C#',
  'Go',
  'Rust',
  'Swift',
  'Kotlin',
  'Ruby',
  'PHP',
  'Dart',
  'Scala',
  'R',
  'Shell'
];

/**
 * Trending time ranges
 */
export const TIME_RANGES = [
  { value: 'daily', label: 'Today' },
  { value: 'weekly', label: 'This Week' },
  { value: 'monthly', label: 'This Month' }
] as const;

/**
 * Mock data for development/fallback
 */
function getMockTrendingDevelopers(): GitHubDeveloper[] {
  return [
    {
      login: 'torvalds',
      id: 1024025,
      avatar_url: 'https://avatars.githubusercontent.com/u/1024025?v=4',
      html_url: 'https://github.com/torvalds',
      name: 'Linus Torvalds',
      company: 'Linux Foundation',
      location: 'Portland, OR',
      bio: 'Creator of Linux and Git',
      public_repos: 6,
      public_gists: 0,
      followers: 178500,
      following: 0,
      created_at: '2011-09-03T15:26:22Z',
      updated_at: '2024-01-20T10:30:00Z',
      total_stars: 50000,
      rank: 1
    },
    {
      login: 'gaearon',
      id: 810438,
      avatar_url: 'https://avatars.githubusercontent.com/u/810438?v=4',
      html_url: 'https://github.com/gaearon',
      name: 'Dan Abramov',
      company: 'Vercel',
      location: 'London, UK',
      bio: 'React team at Meta. Co-created Redux, React Hot Loader, and Create React App.',
      public_repos: 132,
      public_gists: 48,
      followers: 89200,
      following: 171,
      created_at: '2011-05-25T18:18:31Z',
      updated_at: '2024-01-19T14:22:00Z',
      total_stars: 75000,
      rank: 2
    },
    {
      login: 'sindresorhus',
      id: 170270,
      avatar_url: 'https://avatars.githubusercontent.com/u/170270?v=4',
      html_url: 'https://github.com/sindresorhus',
      name: 'Sindre Sorhus',
      company: undefined,
      location: 'Norway',
      bio: 'Full-time open-sourcerer. Focused on Swift and JavaScript.',
      public_repos: 1087,
      public_gists: 137,
      followers: 47800,
      following: 2,
      created_at: '2010-01-20T13:52:41Z',
      updated_at: '2024-01-18T09:15:00Z',
      total_stars: 120000,
      rank: 3
    }
  ];
}

function getMockTrendingTopics(): GitHubTopic[] {
  return [
    {
      name: 'artificial-intelligence',
      display_name: 'Artificial Intelligence',
      repo_count: 15420,
      score: 89500,
      trending_repos: []
    },
    {
      name: 'machine-learning',
      display_name: 'Machine Learning',
      repo_count: 12890,
      score: 78900,
      trending_repos: []
    },
    {
      name: 'react',
      display_name: 'React',
      repo_count: 9650,
      score: 67800,
      trending_repos: []
    },
    {
      name: 'blockchain',
      display_name: 'Blockchain',
      repo_count: 8230,
      score: 54600,
      trending_repos: []
    }
  ];
}

function getMockTrendingRepos(): TrendingRepo[] {
  return [
    {
      id: 1,
      name: 'awesome-react',
      full_name: 'facebook/awesome-react',
      owner: {
        login: 'facebook',
        avatar_url: 'https://avatars.githubusercontent.com/u/69631?v=4'
      },
      description: 'A collection of awesome things regarding React ecosystem',
      html_url: 'https://github.com/facebook/awesome-react',
      stargazers_count: 45230,
      forks_count: 5420,
      language: 'TypeScript',
      topics: ['react', 'javascript', 'frontend'],
      created_at: '2023-01-15T00:00:00Z',
      updated_at: '2024-01-20T00:00:00Z',
      stars_today: 125,
      rank: 1
    },
    {
      id: 2,
      name: 'chatgpt-clone',
      full_name: 'openai/chatgpt-clone',
      owner: {
        login: 'openai',
        avatar_url: 'https://avatars.githubusercontent.com/u/14957082?v=4'
      },
      description: 'Open source ChatGPT clone with enhanced features',
      html_url: 'https://github.com/openai/chatgpt-clone',
      stargazers_count: 38420,
      forks_count: 4230,
      language: 'Python',
      topics: ['ai', 'chatgpt', 'machine-learning'],
      created_at: '2023-02-10T00:00:00Z',
      updated_at: '2024-01-19T00:00:00Z',
      stars_today: 89,
      rank: 2
    },
    {
      id: 3,
      name: 'rust-web-framework',
      full_name: 'rustlang/rust-web-framework',
      owner: {
        login: 'rustlang',
        avatar_url: 'https://avatars.githubusercontent.com/u/5430905?v=4'
      },
      description: 'A blazingly fast web framework written in Rust',
      html_url: 'https://github.com/rustlang/rust-web-framework',
      stargazers_count: 28340,
      forks_count: 3120,
      language: 'Rust',
      topics: ['rust', 'web', 'framework'],
      created_at: '2023-03-05T00:00:00Z',
      updated_at: '2024-01-18T00:00:00Z',
      stars_today: 67,
      rank: 3
    }
  ];
}