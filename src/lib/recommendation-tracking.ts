import { invoke } from "@tauri-apps/api/core";
import { create } from 'zustand';

/**
 * User interaction types for recommendation system
 */
export type InteractionType = 
  | 'agent_execute' 
  | 'marketplace_browse' 
  | 'marketplace_install'
  | 'marketplace_rate'
  | 'feature_use' 
  | 'tab_create' 
  | 'tab_switch'
  | 'tool_use'
  | 'session_start'
  | 'session_end'
  | 'project_open'
  | 'file_read'
  | 'file_write'
  | 'recommendation_shown'
  | 'recommendation_clicked'
  | 'recommendation_dismissed'
  | 'recommendation_feedback';

/**
 * Feature categories for classification
 */
export type FeatureCategory = 
  | 'agent' 
  | 'ui_feature' 
  | 'marketplace' 
  | 'session_management' 
  | 'file_operations'
  | 'tool_integration'
  | 'configuration'
  | 'recommendation';

/**
 * Context data for interactions
 */
export interface InteractionContext {
  projectType?: string;
  fileExtensions?: string[];
  modelUsed?: string;
  taskType?: string;
  sessionDuration?: number;
  tokenUsage?: number;
  errorOccurred?: boolean;
  userIntent?: string;
  workflowStep?: string;
  previousAction?: string;
  [key: string]: any;
}

/**
 * User interaction data
 */
export interface UserInteraction {
  sessionId?: string;
  userSessionHash: string;
  projectId?: string;
  interactionType: InteractionType;
  featureId: string;
  featureCategory: FeatureCategory;
  contextData?: InteractionContext;
  successIndicator?: boolean;
  durationSeconds?: number;
  tokenUsage?: number;
  userRating?: number; // 1-5
  timestamp?: string;
}

/**
 * Recommendation feedback data
 */
export interface RecommendationFeedback {
  recommendationId: number;
  action: 'shown' | 'clicked' | 'adopted' | 'dismissed' | 'rated';
  rating?: number; // 1-5
  feedbackText?: string;
  context?: Record<string, any>;
}

/**
 * User behavior pattern
 */
export interface BehaviorPattern {
  patternType: 'workflow_sequence' | 'feature_combination' | 'time_based' | 'project_based';
  patternData: Record<string, any>;
  frequencyScore: number;
  successScore: number;
  efficiencyScore: number;
  confidenceLevel: number;
}

/**
 * Recommendation tracking service state
 */
interface RecommendationTrackingState {
  // Current session data
  currentSessionHash: string;
  currentProjectId?: string;
  sessionStartTime: Date;
  
  // Interaction queue for batch processing
  pendingInteractions: UserInteraction[];
  isProcessingQueue: boolean;
  
  // User behavior analysis
  userBehaviorPatterns: BehaviorPattern[];
  lastPatternUpdate: Date;
  
  // Settings
  trackingEnabled: boolean;
  batchSize: number;
  flushInterval: number; // milliseconds
  
  // Actions
  trackInteraction: (interaction: Omit<UserInteraction, 'userSessionHash' | 'timestamp'>) => Promise<void>;
  trackRecommendationFeedback: (feedback: RecommendationFeedback) => Promise<void>;
  flushPendingInteractions: () => Promise<void>;
  generateUserProfile: () => Promise<string>;
  updateBehaviorPatterns: () => Promise<void>;
  setTrackingEnabled: (enabled: boolean) => void;
  
  // Internal methods
  _addToQueue: (interaction: UserInteraction) => void;
  _generateSessionHash: () => string;
  _startAutoFlush: () => void;
  _stopAutoFlush: () => void;
}

// Auto-flush timer
let autoFlushTimer: NodeJS.Timeout | null = null;

/**
 * Generate anonymous session hash based on non-PII factors
 */
const generateSessionHash = (): string => {
  const factors = [
    navigator.userAgent,
    screen.width,
    screen.height,
    new Date().toDateString(), // Changes daily
    Math.random().toString(36).substring(7) // Add randomness
  ].join('|');
  
  // Simple hash function
  let hash = 0;
  for (let i = 0; i < factors.length; i++) {
    const char = factors.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32bit integer
  }
  
  return Math.abs(hash).toString(36);
};

/**
 * Detect project type from file extensions and context
 */
const detectProjectType = (fileExtensions: string[] = [], projectPath?: string): string => {
  const extensions = fileExtensions.map(ext => ext.toLowerCase());
  
  // Web development
  if (extensions.some(ext => ['ts', 'tsx', 'js', 'jsx'].includes(ext))) {
    if (extensions.includes('tsx') || extensions.includes('jsx')) return 'react';
    if (projectPath?.includes('next')) return 'nextjs';
    if (projectPath?.includes('vue')) return 'vue';
    return 'javascript';
  }
  
  // Backend development
  if (extensions.includes('py')) return 'python';
  if (extensions.includes('rs')) return 'rust';
  if (extensions.includes('go')) return 'go';
  if (extensions.includes('java')) return 'java';
  if (extensions.includes('rb')) return 'ruby';
  if (extensions.includes('php')) return 'php';
  
  // Mobile development
  if (extensions.includes('swift')) return 'ios';
  if (extensions.includes('kt') || extensions.includes('java')) return 'android';
  
  // Data science
  if (extensions.includes('ipynb') || extensions.includes('py')) return 'data-science';
  
  // DevOps
  if (extensions.some(ext => ['yml', 'yaml', 'dockerfile', 'tf'].includes(ext))) return 'devops';
  
  return 'general';
};

/**
 * Store for recommendation tracking
 */
export const useRecommendationTracking = create<RecommendationTrackingState>((set, get) => ({
  // Initial state
  currentSessionHash: generateSessionHash(),
  sessionStartTime: new Date(),
  pendingInteractions: [],
  isProcessingQueue: false,
  userBehaviorPatterns: [],
  lastPatternUpdate: new Date(),
  trackingEnabled: true,
  batchSize: 10,
  flushInterval: 30000, // 30 seconds
  
  // Track a user interaction
  trackInteraction: async (interaction) => {
    const state = get();
    if (!state.trackingEnabled) return;
    
    const fullInteraction: UserInteraction = {
      ...interaction,
      userSessionHash: state.currentSessionHash,
      timestamp: new Date().toISOString()
    };
    
    // Add context enrichment
    if (fullInteraction.contextData) {
      fullInteraction.contextData.projectType = detectProjectType(
        fullInteraction.contextData.fileExtensions,
        state.currentProjectId
      );
      fullInteraction.contextData.sessionDuration = 
        (Date.now() - state.sessionStartTime.getTime()) / 1000;
    }
    
    state._addToQueue(fullInteraction);
    
    // Auto-flush if queue is full
    if (state.pendingInteractions.length >= state.batchSize) {
      await state.flushPendingInteractions();
    }
  },
  
  // Track recommendation feedback
  trackRecommendationFeedback: async (feedback) => {
    const state = get();
    
    try {
      // Update recommendation record in database
      await invoke('update_recommendation_feedback', {
        recommendationId: feedback.recommendationId,
        action: feedback.action,
        rating: feedback.rating,
        feedbackText: feedback.feedbackText,
        context: feedback.context
      });
      
      // Also track as interaction for analytics
      await state.trackInteraction({
        interactionType: 'recommendation_feedback',
        featureId: `recommendation_${feedback.recommendationId}`,
        featureCategory: 'recommendation',
        contextData: {
          action: feedback.action,
          rating: feedback.rating,
          feedbackText: feedback.feedbackText,
          ...feedback.context
        },
        successIndicator: feedback.action === 'adopted',
        userRating: feedback.rating
      });
      
    } catch (error) {
      console.error('Failed to track recommendation feedback:', error);
    }
  },
  
  // Flush pending interactions to database
  flushPendingInteractions: async () => {
    const state = get();
    if (state.isProcessingQueue || state.pendingInteractions.length === 0) return;
    
    set({ isProcessingQueue: true });
    
    try {
      const interactions = [...state.pendingInteractions];
      set({ pendingInteractions: [] });
      
      // Batch insert interactions
      await invoke('bulk_insert_user_interactions', { interactions });
      
      // Update behavior patterns periodically
      const timeSinceLastUpdate = Date.now() - state.lastPatternUpdate.getTime();
      if (timeSinceLastUpdate > 300000) { // 5 minutes
        await state.updateBehaviorPatterns();
      }
      
    } catch (error) {
      console.error('Failed to flush interactions:', error);
      // Re-add failed interactions to queue
      set(state => ({
        pendingInteractions: [...state.pendingInteractions, ...state.pendingInteractions]
      }));
    } finally {
      set({ isProcessingQueue: false });
    }
  },
  
  // Generate user profile hash for recommendations
  generateUserProfile: async () => {
    const state = get();
    
    try {
      const profileData = await invoke('generate_user_profile', {
        userSessionHash: state.currentSessionHash,
        lookbackDays: 30
      });
      
      return profileData as string;
    } catch (error) {
      console.error('Failed to generate user profile:', error);
      return state.currentSessionHash;
    }
  },
  
  // Update behavior patterns
  updateBehaviorPatterns: async () => {
    const state = get();
    
    try {
      const patterns = await invoke('analyze_user_behavior_patterns', {
        userSessionHash: state.currentSessionHash,
        analysisDepth: 'standard'
      });
      
      set({ 
        userBehaviorPatterns: patterns as BehaviorPattern[],
        lastPatternUpdate: new Date()
      });
      
    } catch (error) {
      console.error('Failed to update behavior patterns:', error);
    }
  },
  
  // Enable/disable tracking
  setTrackingEnabled: (enabled) => {
    const state = get();
    set({ trackingEnabled: enabled });
    
    if (enabled) {
      state._startAutoFlush();
    } else {
      state._stopAutoFlush();
    }
  },
  
  // Internal: Add interaction to queue
  _addToQueue: (interaction) => {
    set(state => ({
      pendingInteractions: [...state.pendingInteractions, interaction]
    }));
  },
  
  // Internal: Generate session hash
  _generateSessionHash: generateSessionHash,
  
  // Internal: Start auto-flush timer
  _startAutoFlush: () => {
    const state = get();
    state._stopAutoFlush(); // Clear existing timer
    
    autoFlushTimer = setInterval(async () => {
      await state.flushPendingInteractions();
    }, state.flushInterval);
  },
  
  // Internal: Stop auto-flush timer
  _stopAutoFlush: () => {
    if (autoFlushTimer) {
      clearInterval(autoFlushTimer);
      autoFlushTimer = null;
    }
  }
}));

// Start auto-flush on initialization
useRecommendationTracking.getState()._startAutoFlush();

/**
 * React hook for easy interaction tracking
 */
export const useInteractionTracker = () => {
  const trackInteraction = useRecommendationTracking(state => state.trackInteraction);
  const trackFeedback = useRecommendationTracking(state => state.trackRecommendationFeedback);
  
  return {
    trackInteraction,
    trackFeedback,
    
    // Convenience methods for common interactions
    trackAgentExecution: (agentId: string, success: boolean, duration?: number, tokens?: number) =>
      trackInteraction({
        interactionType: 'agent_execute',
        featureId: agentId,
        featureCategory: 'agent',
        successIndicator: success,
        durationSeconds: duration,
        tokenUsage: tokens
      }),
    
    trackMarketplaceBrowse: (category?: string, searchQuery?: string) =>
      trackInteraction({
        interactionType: 'marketplace_browse',
        featureId: 'marketplace',
        featureCategory: 'marketplace',
        contextData: { category, searchQuery }
      }),
    
    trackAgentInstall: (agentId: string, source: string) =>
      trackInteraction({
        interactionType: 'marketplace_install',
        featureId: agentId,
        featureCategory: 'marketplace',
        successIndicator: true,
        contextData: { source }
      }),
    
    trackFeatureUse: (featureName: string, category: FeatureCategory, context?: InteractionContext) =>
      trackInteraction({
        interactionType: 'feature_use',
        featureId: featureName,
        featureCategory: category,
        contextData: context
      }),
    
    trackSessionStart: (projectId: string, sessionId: string) =>
      trackInteraction({
        interactionType: 'session_start',
        featureId: sessionId,
        featureCategory: 'session_management',
        projectId,
        sessionId,
        successIndicator: true
      }),
    
    trackRecommendationShown: (recommendationId: number, itemId: string, type: string) =>
      trackInteraction({
        interactionType: 'recommendation_shown',
        featureId: itemId,
        featureCategory: 'recommendation',
        contextData: { recommendationId, recommendationType: type }
      })
  };
};

/**
 * Cleanup function to flush remaining interactions before app closes
 */
export const cleanupRecommendationTracking = async () => {
  const state = useRecommendationTracking.getState();
  state._stopAutoFlush();
  await state.flushPendingInteractions();
};