/**
 * Claude-themed syntax highlighting theme
 * Features orange, purple, and violet colors to match <PERSON>'s aesthetic
 * Now with improved contrast and dynamic theme support
 */

// Function to get theme-aware colors
const getThemeColor = (lightColor: string, darkColor: string) => {
  if (typeof window !== 'undefined') {
    const theme = document.documentElement.getAttribute('data-theme') || 
                  (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
    return theme === 'dark' ? darkColor : lightColor;
  }
  return darkColor; // Default to dark theme
};

export const claudeSyntaxTheme: any = {
  'code[class*="language-"]': {
    color: getThemeColor('#2d3748', '#e3e8f0'),
    background: 'transparent',
    textShadow: 'none',
    fontFamily: 'var(--font-mono)',
    fontSize: '0.875em',
    textAlign: 'left',
    whiteSpace: 'pre',
    wordSpacing: 'normal',
    wordBreak: 'normal',
    wordWrap: 'normal',
    lineHeight: '1.5',
    MozTabSize: '4',
    OTabSize: '4',
    tabSize: '4',
    WebkitHyphens: 'none',
    MozHyphens: 'none',
    msHyphens: 'none',
    hyphens: 'none',
  },
  'pre[class*="language-"]': {
    color: getThemeColor('#2d3748', '#e3e8f0'),
    background: 'transparent',
    textShadow: 'none',
    fontFamily: 'var(--font-mono)',
    fontSize: '0.875em',
    textAlign: 'left',
    whiteSpace: 'pre',
    wordSpacing: 'normal',
    wordBreak: 'normal',
    wordWrap: 'normal',
    lineHeight: '1.5',
    MozTabSize: '4',
    OTabSize: '4',
    tabSize: '4',
    WebkitHyphens: 'none',
    MozHyphens: 'none',
    msHyphens: 'none',
    hyphens: 'none',
    padding: '1em',
    margin: '0',
    overflow: 'auto',
  },
  ':not(pre) > code[class*="language-"]': {
    background: getThemeColor('rgba(139, 92, 246, 0.15)', 'rgba(139, 92, 246, 0.1)'),
    padding: '0.1em 0.3em',
    borderRadius: '0.3em',
    whiteSpace: 'normal',
  },
  'comment': {
    color: getThemeColor('#718096', '#6b7280'),
    fontStyle: 'italic',
  },
  'prolog': {
    color: getThemeColor('#718096', '#6b7280'),
  },
  'doctype': {
    color: getThemeColor('#718096', '#6b7280'),
  },
  'cdata': {
    color: getThemeColor('#718096', '#6b7280'),
  },
  'punctuation': {
    color: getThemeColor('#4a5568', '#9ca3af'),
  },
  'namespace': {
    opacity: '0.7',
  },
  'property': {
    color: getThemeColor('#d97706', '#f59e0b'), // Amber/Orange with better contrast
  },
  'tag': {
    color: getThemeColor('#7c3aed', '#8b5cf6'), // Violet with better contrast
  },
  'boolean': {
    color: getThemeColor('#d97706', '#f59e0b'), // Amber/Orange
  },
  'number': {
    color: getThemeColor('#d97706', '#f59e0b'), // Amber/Orange
  },
  'constant': {
    color: getThemeColor('#d97706', '#f59e0b'), // Amber/Orange
  },
  'symbol': {
    color: getThemeColor('#d97706', '#f59e0b'), // Amber/Orange
  },
  'deleted': {
    color: getThemeColor('#dc2626', '#ef4444'), // Red with better contrast
  },
  'selector': {
    color: getThemeColor('#8b5a9f', '#a78bfa'), // Light Purple with better contrast
  },
  'attr-name': {
    color: getThemeColor('#8b5a9f', '#a78bfa'), // Light Purple
  },
  'string': {
    color: getThemeColor('#059669', '#10b981'), // Emerald Green with better contrast
  },
  'char': {
    color: getThemeColor('#059669', '#10b981'), // Emerald Green
  },
  'builtin': {
    color: getThemeColor('#7c3aed', '#8b5cf6'), // Violet
  },
  'url': {
    color: getThemeColor('#059669', '#10b981'), // Emerald Green
  },
  'inserted': {
    color: getThemeColor('#059669', '#10b981'), // Emerald Green
  },
  'entity': {
    color: getThemeColor('#8b5a9f', '#a78bfa'), // Light Purple
    cursor: 'help',
  },
  'atrule': {
    color: getThemeColor('#a855f7', '#c084fc'), // Light Violet
  },
  'attr-value': {
    color: getThemeColor('#059669', '#10b981'), // Emerald Green
  },
  'keyword': {
    color: getThemeColor('#a855f7', '#c084fc'), // Light Violet
  },
  'function': {
    color: getThemeColor('#4f46e5', '#818cf8'), // Indigo with better contrast
  },
  'class-name': {
    color: getThemeColor('#d97706', '#f59e0b'), // Amber/Orange
  },
  'regex': {
    color: getThemeColor('#0891b2', '#06b6d4'), // Cyan with better contrast
  },
  'important': {
    color: getThemeColor('#d97706', '#f59e0b'), // Amber/Orange
    fontWeight: 'bold',
  },
  'variable': {
    color: getThemeColor('#8b5a9f', '#a78bfa'), // Light Purple
  },
  'bold': {
    fontWeight: 'bold',
  },
  'italic': {
    fontStyle: 'italic',
  },
  'operator': {
    color: getThemeColor('#4a5568', '#9ca3af'),
  },
  'script': {
    color: getThemeColor('#2d3748', '#e3e8f0'),
  },
  'parameter': {
    color: getThemeColor('#d69e2e', '#fbbf24'), // Yellow with better contrast
  },
  'method': {
    color: getThemeColor('#4f46e5', '#818cf8'), // Indigo
  },
  'field': {
    color: getThemeColor('#d97706', '#f59e0b'), // Amber/Orange
  },
  'annotation': {
    color: getThemeColor('#718096', '#6b7280'),
  },
  'type': {
    color: getThemeColor('#8b5a9f', '#a78bfa'), // Light Purple
  },
  'module': {
    color: getThemeColor('#7c3aed', '#8b5cf6'), // Violet
  },
}; 