/**
 * Real-time Preference Learning System
 * Continuously adapts recommendations based on user behavior and feedback
 */

import React from 'react';
import { invoke } from "@tauri-apps/api/core";
import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';

export interface UserPreference {
  category: string;
  itemId: string;
  preferenceScore: number; // -1 to 1, where -1 is strong dislike, 1 is strong like
  confidence: number; // 0 to 1, how confident we are in this preference
  lastUpdated: Date;
  updateCount: number;
  context?: Record<string, any>;
}

export interface PreferenceSignal {
  signalType: 'implicit' | 'explicit';
  action: string;
  itemId: string;
  category: string;
  strength: number; // How strong this signal is (-1 to 1)
  context?: Record<string, any>;
  timestamp: Date;
}

export interface LearningRule {
  id: string;
  name: string;
  description: string;
  signalPattern: string;
  preferenceUpdate: (current: number, signal: PreferenceSignal) => number;
  confidenceUpdate: (current: number, signal: PreferenceSignal) => number;
  decayRate: number; // How fast preferences decay over time
  isActive: boolean;
}

export interface PreferenceModel {
  userHash: string;
  preferences: Map<string, UserPreference>;
  implicitSignals: PreferenceSignal[];
  explicitSignals: PreferenceSignal[];
  modelVersion: string;
  lastUpdated: Date;
  trainingData: {
    totalSignals: number;
    positiveSignals: number;
    negativeSignals: number;
    confidenceThreshold: number;
  };
}

// Default learning rules
const DEFAULT_LEARNING_RULES: LearningRule[] = [
  {
    id: 'explicit_rating',
    name: 'Explicit Rating',
    description: 'Direct user ratings (1-5 stars)',
    signalPattern: 'rating',
    preferenceUpdate: (current, signal) => {
      // Convert 1-5 rating to -1 to 1 scale
      const normalizedRating = (signal.strength - 3) / 2;
      // Give explicit feedback high weight
      return current * 0.3 + normalizedRating * 0.7;
    },
    confidenceUpdate: (current, signal) => Math.min(1.0, current + 0.3),
    decayRate: 0.95, // Slow decay for explicit feedback
    isActive: true
  },
  
  {
    id: 'adoption_behavior',
    name: 'Adoption Behavior',
    description: 'User actually uses recommended items',
    signalPattern: 'adopted|installed|used',
    preferenceUpdate: (current, signal) => {
      const adoptionScore = signal.action === 'adopted' ? 0.8 : 0.6;
      return current * 0.6 + adoptionScore * 0.4;
    },
    confidenceUpdate: (current, signal) => Math.min(1.0, current + 0.2),
    decayRate: 0.98,
    isActive: true
  },
  
  {
    id: 'engagement_time',
    name: 'Engagement Time',
    description: 'Time spent interacting with recommendations',
    signalPattern: 'engagement',
    preferenceUpdate: (current, signal) => {
      // Longer engagement indicates higher preference
      const engagementScore = Math.min(1.0, signal.strength / 300); // 5 minutes = max score
      return current * 0.8 + engagementScore * 0.2;
    },
    confidenceUpdate: (current, signal) => Math.min(1.0, current + 0.1),
    decayRate: 0.99,
    isActive: true
  },
  
  {
    id: 'dismissal_behavior',
    name: 'Dismissal Behavior',
    description: 'User dismisses or shows disinterest',
    signalPattern: 'dismissed|ignored|skipped',
    preferenceUpdate: (current, signal) => {
      const dismissalScore = signal.action === 'dismissed' ? -0.6 : -0.3;
      return current * 0.7 + dismissalScore * 0.3;
    },
    confidenceUpdate: (current, signal) => Math.min(1.0, current + 0.15),
    decayRate: 0.97,
    isActive: true
  },
  
  {
    id: 'contextual_patterns',
    name: 'Contextual Patterns',
    description: 'Preferences based on context (project type, time, etc.)',
    signalPattern: 'contextual',
    preferenceUpdate: (current, signal) => {
      // Weight contextual signals based on how specific the context is
      const contextSpecificity = signal.context ? Object.keys(signal.context).length / 10 : 0.5;
      const weight = 0.1 + (contextSpecificity * 0.2);
      return current * (1 - weight) + signal.strength * weight;
    },
    confidenceUpdate: (current, signal) => Math.min(1.0, current + 0.05),
    decayRate: 0.995,
    isActive: true
  },
  
  {
    id: 'similarity_inference',
    name: 'Similarity Inference',
    description: 'Infer preferences for similar items',
    signalPattern: 'similarity',
    preferenceUpdate: (current, signal) => {
      // Weaker signal for inferred preferences
      const similarityWeight = 0.1;
      return current * (1 - similarityWeight) + signal.strength * similarityWeight;
    },
    confidenceUpdate: (current, signal) => Math.min(1.0, current + 0.02),
    decayRate: 0.99,
    isActive: true
  }
];

interface PreferenceLearningState {
  // Model data
  userModel: PreferenceModel | null;
  learningRules: Map<string, LearningRule>;
  
  // Real-time processing
  pendingSignals: PreferenceSignal[];
  isProcessing: boolean;
  lastUpdate: Date;
  
  // Configuration
  batchSize: number;
  processingInterval: number; // milliseconds
  confidenceThreshold: number;
  
  // Actions
  recordSignal: (signal: Omit<PreferenceSignal, 'timestamp'>) => Promise<void>;
  processSignals: () => Promise<void>;
  updatePreference: (itemId: string, category: string, signal: PreferenceSignal) => Promise<void>;
  getPreference: (itemId: string, category: string) => UserPreference | null;
  getUserPreferences: (category?: string, minConfidence?: number) => UserPreference[];
  adaptRecommendations: (recommendations: any[], context?: Record<string, any>) => Promise<any[]>;
  
  // Model management
  initializeModel: (userHash: string) => Promise<void>;
  saveModel: () => Promise<void>;
  loadModel: (userHash: string) => Promise<void>;
  resetModel: () => Promise<void>;
}

let processingTimer: NodeJS.Timeout | null = null;

export const usePreferenceLearning = create<PreferenceLearningState>()(
  subscribeWithSelector((set, get) => ({
    // Initial state
    userModel: null,
    learningRules: new Map(DEFAULT_LEARNING_RULES.map(rule => [rule.id, rule])),
    pendingSignals: [],
    isProcessing: false,
    lastUpdate: new Date(),
    batchSize: 20,
    processingInterval: 10000, // 10 seconds
    confidenceThreshold: 0.3,
    
    // Record a preference signal
    recordSignal: async (signalInput) => {
      const signal: PreferenceSignal = {
        ...signalInput,
        timestamp: new Date()
      };
      
      set(state => ({
        pendingSignals: [...state.pendingSignals, signal]
      }));
      
      // Process immediately if batch is full
      const state = get();
      if (state.pendingSignals.length >= state.batchSize) {
        await state.processSignals();
      }
    },
    
    // Process pending signals
    processSignals: async () => {
      const state = get();
      if (state.isProcessing || state.pendingSignals.length === 0) {
        return;
      }
      
      set({ isProcessing: true });
      
      try {
        const signalsToProcess = [...state.pendingSignals];
        set({ pendingSignals: [] });
        
        // Group signals by item for batch processing
        const signalGroups = new Map<string, PreferenceSignal[]>();
        
        for (const signal of signalsToProcess) {
          const key = `${signal.category}:${signal.itemId}`;
          if (!signalGroups.has(key)) {
            signalGroups.set(key, []);
          }
          signalGroups.get(key)!.push(signal);
        }
        
        // Process each group
        for (const [key, signals] of signalGroups) {
          const [category, itemId] = key.split(':');
          await state.updatePreferenceFromSignals(itemId, category, signals);
        }
        
        // Apply time-based decay to all preferences
        state.applyTimeDecay();
        
        // Save updated model
        await state.saveModel();
        
        set({ lastUpdate: new Date() });
        
      } catch (error) {
        console.error('Failed to process preference signals:', error);
      } finally {
        set({ isProcessing: false });
      }
    },
    
    // Update preference based on a signal
    updatePreference: async (itemId, category, signal) => {
      const state = get();
      if (!state.userModel) return;
      
      const key = `${category}:${itemId}`;
      const currentPreference = state.userModel.preferences.get(key);
      
      // Apply matching learning rules
      let updatedPreference = currentPreference || {
        category,
        itemId,
        preferenceScore: 0,
        confidence: 0,
        lastUpdated: new Date(),
        updateCount: 0,
        context: signal.context
      };
      
      for (const rule of state.learningRules.values()) {
        if (!rule.isActive) continue;
        
        const regex = new RegExp(rule.signalPattern, 'i');
        if (regex.test(signal.action) || regex.test(signal.signalType)) {
          const newScore = rule.preferenceUpdate(updatedPreference.preferenceScore, signal);
          const newConfidence = rule.confidenceUpdate(updatedPreference.confidence, signal);
          
          updatedPreference = {
            ...updatedPreference,
            preferenceScore: Math.max(-1, Math.min(1, newScore)),
            confidence: Math.max(0, Math.min(1, newConfidence)),
            lastUpdated: new Date(),
            updateCount: updatedPreference.updateCount + 1
          };
        }
      }
      
      // Update context with new information
      if (signal.context) {
        updatedPreference.context = {
          ...updatedPreference.context,
          ...signal.context
        };
      }
      
      // Store updated preference
      state.userModel.preferences.set(key, updatedPreference);
      
      // Update training data
      state.userModel.trainingData.totalSignals++;
      if (signal.strength > 0) {
        state.userModel.trainingData.positiveSignals++;
      } else if (signal.strength < 0) {
        state.userModel.trainingData.negativeSignals++;
      }
      
      // Store explicit and implicit signals separately
      if (signal.signalType === 'explicit') {
        state.userModel.explicitSignals.push(signal);
      } else {
        state.userModel.implicitSignals.push(signal);
      }
      
      // Limit signal history
      if (state.userModel.explicitSignals.length > 1000) {
        state.userModel.explicitSignals = state.userModel.explicitSignals.slice(-500);
      }
      if (state.userModel.implicitSignals.length > 5000) {
        state.userModel.implicitSignals = state.userModel.implicitSignals.slice(-2500);
      }
    },
    
    // Get preference for a specific item
    getPreference: (itemId, category) => {
      const state = get();
      if (!state.userModel) return null;
      
      const key = `${category}:${itemId}`;
      return state.userModel.preferences.get(key) || null;
    },
    
    // Get all user preferences
    getUserPreferences: (category, minConfidence = 0.3) => {
      const state = get();
      if (!state.userModel) return [];
      
      const preferences: UserPreference[] = [];
      
      for (const preference of state.userModel.preferences.values()) {
        if (preference.confidence >= minConfidence) {
          if (!category || preference.category === category) {
            preferences.push(preference);
          }
        }
      }
      
      // Sort by preference score and confidence
      preferences.sort((a, b) => {
        const aWeight = a.preferenceScore * a.confidence;
        const bWeight = b.preferenceScore * b.confidence;
        return bWeight - aWeight;
      });
      
      return preferences;
    },
    
    // Adapt recommendations based on learned preferences
    adaptRecommendations: async (recommendations, context) => {
      const state = get();
      if (!state.userModel || recommendations.length === 0) {
        return recommendations;
      }
      
      const adaptedRecommendations = recommendations.map(rec => {
        const preference = state.getPreference(rec.itemId, rec.recommendationType);
        
        if (preference && preference.confidence >= state.confidenceThreshold) {
          // Adjust relevance score based on learned preference
          const preferenceWeight = preference.confidence * 0.3;
          const originalWeight = 1 - preferenceWeight;
          
          const adaptedScore = (rec.relevanceScore * originalWeight) + 
                              ((preference.preferenceScore + 1) / 2 * preferenceWeight);
          
          return {
            ...rec,
            relevanceScore: Math.max(0, Math.min(1, adaptedScore)),
            priorityScore: Math.max(0, Math.min(1, adaptedScore * 1.1)),
            reasoningData: {
              ...rec.reasoningData,
              preferenceAdjustment: preference.preferenceScore,
              preferenceConfidence: preference.confidence,
              adaptationApplied: true
            }
          };
        }
        
        return rec;
      });
      
      // Re-sort by adapted scores
      adaptedRecommendations.sort((a, b) => b.priorityScore - a.priorityScore);
      
      return adaptedRecommendations;
    },
    
    // Initialize user model
    initializeModel: async (userHash) => {
      try {
        await get().loadModel(userHash);
      } catch (error) {
        // Create new model if loading fails
        const newModel: PreferenceModel = {
          userHash,
          preferences: new Map(),
          implicitSignals: [],
          explicitSignals: [],
          modelVersion: '1.0',
          lastUpdated: new Date(),
          trainingData: {
            totalSignals: 0,
            positiveSignals: 0,
            negativeSignals: 0,
            confidenceThreshold: 0.3
          }
        };
        
        set({ userModel: newModel });
        await get().saveModel();
      }
    },
    
    // Save model to database
    saveModel: async () => {
      const state = get();
      if (!state.userModel) return;
      
      try {
        // Convert Map to serializable format
        const preferencesArray = Array.from(state.userModel.preferences.entries()).map(([key, pref]) => ({
          key,
          ...pref,
          lastUpdated: pref.lastUpdated.toISOString()
        }));
        
        await invoke('save_preference_model', {
          userHash: state.userModel.userHash,
          preferences: preferencesArray,
          implicitSignals: state.userModel.implicitSignals.map(s => ({
            ...s,
            timestamp: s.timestamp.toISOString()
          })),
          explicitSignals: state.userModel.explicitSignals.map(s => ({
            ...s,
            timestamp: s.timestamp.toISOString()
          })),
          trainingData: state.userModel.trainingData,
          modelVersion: state.userModel.modelVersion
        });
      } catch (error) {
        console.error('Failed to save preference model:', error);
      }
    },
    
    // Load model from database
    loadModel: async (userHash) => {
      try {
        const modelData = await invoke('load_preference_model', { userHash });
        
        if (modelData) {
          const preferences = new Map();
          for (const prefData of modelData.preferences || []) {
            preferences.set(prefData.key, {
              ...prefData,
              lastUpdated: new Date(prefData.lastUpdated)
            });
          }
          
          const model: PreferenceModel = {
            userHash,
            preferences,
            implicitSignals: (modelData.implicitSignals || []).map((s: any) => ({
              ...s,
              timestamp: new Date(s.timestamp)
            })),
            explicitSignals: (modelData.explicitSignals || []).map((s: any) => ({
              ...s,
              timestamp: new Date(s.timestamp)
            })),
            modelVersion: modelData.modelVersion || '1.0',
            lastUpdated: new Date(modelData.lastUpdated || Date.now()),
            trainingData: modelData.trainingData || {
              totalSignals: 0,
              positiveSignals: 0,
              negativeSignals: 0,
              confidenceThreshold: 0.3
            }
          };
          
          set({ userModel: model });
        }
      } catch (error) {
        console.error('Failed to load preference model:', error);
        throw error;
      }
    },
    
    // Reset model
    resetModel: async () => {
      const state = get();
      if (state.userModel) {
        await state.initializeModel(state.userModel.userHash);
      }
    },
    
    // Internal helper methods
    updatePreferenceFromSignals: async (itemId: string, category: string, signals: PreferenceSignal[]) => {
      for (const signal of signals) {
        await get().updatePreference(itemId, category, signal);
      }
    },
    
    applyTimeDecay: () => {
      const state = get();
      if (!state.userModel) return;
      
      const now = new Date();
      const daysSinceUpdate = (now.getTime() - state.lastUpdate.getTime()) / (1000 * 60 * 60 * 24);
      
      if (daysSinceUpdate < 1) return; // Only apply decay daily
      
      for (const [key, preference] of state.userModel.preferences) {
        const daysSincePreferenceUpdate = (now.getTime() - preference.lastUpdated.getTime()) / (1000 * 60 * 60 * 24);
        
        if (daysSincePreferenceUpdate > 1) {
          // Find applicable decay rate from learning rules
          let decayRate = 0.99; // Default
          for (const rule of state.learningRules.values()) {
            if (rule.isActive) {
              decayRate = Math.min(decayRate, rule.decayRate);
            }
          }
          
          const decayFactor = Math.pow(decayRate, daysSincePreferenceUpdate);
          
          const updatedPreference = {
            ...preference,
            preferenceScore: preference.preferenceScore * decayFactor,
            confidence: preference.confidence * Math.pow(0.995, daysSincePreferenceUpdate)
          };
          
          state.userModel.preferences.set(key, updatedPreference);
        }
      }
    }
  }))
);

// Start automatic processing
usePreferenceLearning.subscribe(
  (state) => [state.pendingSignals.length, state.processingInterval],
  () => {
    const state = usePreferenceLearning.getState();
    
    if (processingTimer) {
      clearInterval(processingTimer);
    }
    
    processingTimer = setInterval(() => {
      state.processSignals();
    }, state.processingInterval);
  }
);

/**
 * React hook for easy preference learning integration
 */
export const useUserPreferences = (userHash: string) => {
  const store = usePreferenceLearning();
  
  React.useEffect(() => {
    if (userHash && !store.userModel) {
      store.initializeModel(userHash);
    }
  }, [userHash, store]);
  
  return {
    recordLike: (itemId: string, category: string, context?: Record<string, any>) =>
      store.recordSignal({
        signalType: 'explicit',
        action: 'liked',
        itemId,
        category,
        strength: 1,
        context
      }),
    
    recordDislike: (itemId: string, category: string, context?: Record<string, any>) =>
      store.recordSignal({
        signalType: 'explicit',
        action: 'disliked',
        itemId,
        category,
        strength: -1,
        context
      }),
    
    recordAdoption: (itemId: string, category: string, context?: Record<string, any>) =>
      store.recordSignal({
        signalType: 'implicit',
        action: 'adopted',
        itemId,
        category,
        strength: 0.8,
        context
      }),
    
    recordEngagement: (itemId: string, category: string, duration: number, context?: Record<string, any>) =>
      store.recordSignal({
        signalType: 'implicit',
        action: 'engagement',
        itemId,
        category,
        strength: duration,
        context
      }),
    
    recordDismissal: (itemId: string, category: string, context?: Record<string, any>) =>
      store.recordSignal({
        signalType: 'implicit',
        action: 'dismissed',
        itemId,
        category,
        strength: -0.5,
        context
      }),
    
    getPreferences: store.getUserPreferences,
    getPreference: store.getPreference,
    adaptRecommendations: store.adaptRecommendations
  };
};