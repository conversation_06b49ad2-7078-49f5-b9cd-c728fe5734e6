/**
 * Advanced cache management system for Hub Dashboard
 */

export interface CacheItem<T = any> {
  key: string;
  data: T;
  timestamp: number;
  expiry: number;
  ttl: number;
  hitCount: number;
  size: number;
  tags: string[];
  priority: 'low' | 'medium' | 'high';
}

export interface CacheConfig {
  maxSize: number; // in bytes
  defaultTTL: number; // in milliseconds
  maxItems: number;
  cleanupInterval: number;
  compressionThreshold: number; // compress items larger than this
  persistToDisk: boolean;
  enableMetrics: boolean;
}

export interface CacheMetrics {
  hits: number;
  misses: number;
  hitRate: number;
  totalSize: number;
  itemCount: number;
  evictions: number;
  compressionSavings: number;
}

class CacheManager {
  private cache: Map<string, CacheItem> = new Map();
  private config: CacheConfig;
  private metrics: CacheMetrics;
  private cleanupTimer?: NodeJS.Timeout;
  private accessOrder: string[] = []; // LRU tracking

  constructor(config: Partial<CacheConfig> = {}) {
    this.config = {
      maxSize: 100 * 1024 * 1024, // 100MB default
      defaultTTL: 30 * 60 * 1000, // 30 minutes
      maxItems: 1000,
      cleanupInterval: 5 * 60 * 1000, // 5 minutes
      compressionThreshold: 50 * 1024, // 50KB
      persistToDisk: true,
      enableMetrics: true,
      ...config
    };

    this.metrics = {
      hits: 0,
      misses: 0,
      hitRate: 0,
      totalSize: 0,
      itemCount: 0,
      evictions: 0,
      compressionSavings: 0
    };

    this.initializeCache();
  }

  private initializeCache() {
    // Load persisted cache if enabled
    if (this.config.persistToDisk && typeof localStorage !== 'undefined') {
      this.loadFromPersistence();
    }

    // Start cleanup interval
    this.startCleanupTimer();

    // Setup beforeunload handler for persistence
    if (typeof window !== 'undefined') {
      window.addEventListener('beforeunload', () => {
        this.saveToPersistence();
      });
    }
  }

  private startCleanupTimer() {
    this.cleanupTimer = setInterval(() => {
      this.cleanup();
    }, this.config.cleanupInterval);
  }

  private loadFromPersistence() {
    try {
      const cacheData = localStorage.getItem('claudia-cache');
      if (cacheData) {
        const parsed = JSON.parse(cacheData);
        
        // Validate and restore cache items
        for (const [key, item] of Object.entries(parsed.cache || {})) {
          const cacheItem = item as CacheItem;
          
          // Skip expired items
          if (Date.now() > cacheItem.expiry) continue;
          
          // Decompress if needed
          if (cacheItem.data && typeof cacheItem.data === 'string' && cacheItem.data.startsWith('compressed:')) {
            try {
              cacheItem.data = JSON.parse(atob(cacheItem.data.substring(11)));
            } catch (e) {
              console.warn('Failed to decompress cache item:', key);
              continue;
            }
          }
          
          this.cache.set(key, cacheItem);
          this.updateAccessOrder(key);
        }

        // Restore metrics
        if (parsed.metrics) {
          this.metrics = { ...this.metrics, ...parsed.metrics };
        }

        console.debug(`Restored ${this.cache.size} cache items from persistence`);
      }
    } catch (error) {
      console.warn('Failed to load cache from persistence:', error);
    }
  }

  private saveToPersistence() {
    if (!this.config.persistToDisk || typeof localStorage === 'undefined') return;

    try {
      const cacheObject: Record<string, CacheItem> = {};
      
      // Compress large items before saving
      for (const [key, item] of this.cache.entries()) {
        const itemCopy = { ...item };
        
        if (itemCopy.size > this.config.compressionThreshold) {
          try {
            const compressed = btoa(JSON.stringify(itemCopy.data));
            itemCopy.data = `compressed:${compressed}`;
          } catch (e) {
            // Skip items that can't be compressed
            continue;
          }
        }
        
        cacheObject[key] = itemCopy;
      }

      const cacheData = {
        cache: cacheObject,
        metrics: this.metrics,
        version: '1.0.0',
        timestamp: Date.now()
      };

      localStorage.setItem('claudia-cache', JSON.stringify(cacheData));
    } catch (error) {
      console.warn('Failed to save cache to persistence:', error);
      
      // If quota exceeded, clear some old items and try again
      if (error instanceof DOMException && error.name === 'QuotaExceededError') {
        this.evictLRUItems(Math.floor(this.cache.size * 0.2)); // Remove 20% of items
        this.saveToPersistence(); // Retry
      }
    }
  }

  private calculateItemSize(data: any): number {
    try {
      return new Blob([JSON.stringify(data)]).size;
    } catch {
      // Fallback estimation
      return JSON.stringify(data).length * 2; // Rough estimate
    }
  }

  private updateAccessOrder(key: string) {
    // Remove from current position
    const index = this.accessOrder.indexOf(key);
    if (index > -1) {
      this.accessOrder.splice(index, 1);
    }
    
    // Add to end (most recently used)
    this.accessOrder.push(key);
  }

  private cleanup() {
    const now = Date.now();
    let expiredCount = 0;
    let freedSize = 0;

    // Remove expired items
    for (const [key, item] of this.cache.entries()) {
      if (now > item.expiry) {
        freedSize += item.size;
        this.cache.delete(key);
        expiredCount++;
        
        // Remove from access order
        const index = this.accessOrder.indexOf(key);
        if (index > -1) {
          this.accessOrder.splice(index, 1);
        }
      }
    }

    // Check if we need to evict more items due to size/count limits
    if (this.metrics.totalSize > this.config.maxSize || this.cache.size > this.config.maxItems) {
      const itemsToEvict = Math.max(
        this.cache.size - this.config.maxItems,
        Math.floor(this.cache.size * 0.1) // Evict at least 10%
      );
      
      const evictedSize = this.evictLRUItems(itemsToEvict);
      freedSize += evictedSize;
    }

    // Update metrics
    this.updateTotalSize();
    this.metrics.evictions += expiredCount;

    if (expiredCount > 0 || freedSize > 0) {
      console.debug(`Cache cleanup: removed ${expiredCount} expired items, freed ${Math.round(freedSize / 1024)}KB`);
    }
  }

  private evictLRUItems(count: number): number {
    let freedSize = 0;
    let evicted = 0;

    // Evict from least recently used
    while (evicted < count && this.accessOrder.length > 0) {
      const key = this.accessOrder.shift()!;
      const item = this.cache.get(key);
      
      if (item) {
        freedSize += item.size;
        this.cache.delete(key);
        evicted++;
      }
    }

    this.metrics.evictions += evicted;
    return freedSize;
  }

  private updateTotalSize() {
    this.metrics.totalSize = Array.from(this.cache.values())
      .reduce((total, item) => total + item.size, 0);
    this.metrics.itemCount = this.cache.size;
  }

  // Public API
  public set<T>(
    key: string, 
    data: T, 
    options: {
      ttl?: number;
      tags?: string[];
      priority?: 'low' | 'medium' | 'high';
    } = {}
  ): void {
    const {
      ttl = this.config.defaultTTL,
      tags = [],
      priority = 'medium'
    } = options;

    const size = this.calculateItemSize(data);
    const now = Date.now();

    // Check if item is too large
    if (size > this.config.maxSize * 0.1) { // No single item should be more than 10% of cache
      console.warn(`Cache item '${key}' is too large (${Math.round(size / 1024)}KB), skipping`);
      return;
    }

    const item: CacheItem<T> = {
      key,
      data,
      timestamp: now,
      expiry: now + ttl,
      ttl,
      hitCount: 0,
      size,
      tags,
      priority
    };

    // If cache is full, make room
    if (this.cache.size >= this.config.maxItems) {
      this.evictLRUItems(1);
    }

    this.cache.set(key, item);
    this.updateAccessOrder(key);
    this.updateTotalSize();

    // Save to persistence periodically
    if (this.cache.size % 10 === 0) {
      this.saveToPersistence();
    }
  }

  public get<T>(key: string): T | null {
    const item = this.cache.get(key) as CacheItem<T> | undefined;

    if (!item) {
      this.metrics.misses++;
      this.updateHitRate();
      return null;
    }

    // Check if expired
    if (Date.now() > item.expiry) {
      this.cache.delete(key);
      this.metrics.misses++;
      this.updateHitRate();
      return null;
    }

    // Update hit metrics
    item.hitCount++;
    this.metrics.hits++;
    this.updateHitRate();
    this.updateAccessOrder(key);

    return item.data;
  }

  public has(key: string): boolean {
    const item = this.cache.get(key);
    return item !== undefined && Date.now() <= item.expiry;
  }

  public delete(key: string): boolean {
    const item = this.cache.get(key);
    if (item) {
      this.cache.delete(key);
      
      // Remove from access order
      const index = this.accessOrder.indexOf(key);
      if (index > -1) {
        this.accessOrder.splice(index, 1);
      }
      
      this.updateTotalSize();
      return true;
    }
    return false;
  }

  public clear(tag?: string): void {
    if (tag) {
      // Clear items with specific tag
      for (const [key, item] of this.cache.entries()) {
        if (item.tags.includes(tag)) {
          this.delete(key);
        }
      }
    } else {
      // Clear all
      this.cache.clear();
      this.accessOrder = [];
      this.updateTotalSize();
    }
  }

  public getByTag(tag: string): Array<{ key: string; data: any }> {
    const results: Array<{ key: string; data: any }> = [];
    
    for (const [key, item] of this.cache.entries()) {
      if (item.tags.includes(tag) && Date.now() <= item.expiry) {
        results.push({ key, data: item.data });
      }
    }
    
    return results;
  }

  public getMetrics(): CacheMetrics {
    return { ...this.metrics };
  }

  public getConfig(): CacheConfig {
    return { ...this.config };
  }

  public updateConfig(updates: Partial<CacheConfig>): void {
    this.config = { ...this.config, ...updates };
    
    // Apply new limits
    if (this.cache.size > this.config.maxItems) {
      this.evictLRUItems(this.cache.size - this.config.maxItems);
    }
    
    this.updateTotalSize();
  }

  private updateHitRate() {
    const total = this.metrics.hits + this.metrics.misses;
    this.metrics.hitRate = total > 0 ? this.metrics.hits / total : 0;
  }

  public optimizeCache(): void {
    // Remove low-priority, low-hit items
    const itemsToRemove: string[] = [];
    
    for (const [key, item] of this.cache.entries()) {
      const ageInHours = (Date.now() - item.timestamp) / (1000 * 60 * 60);
      const hitRate = item.hitCount / Math.max(ageInHours, 1);
      
      // Remove low-hit, low-priority items
      if (item.priority === 'low' && hitRate < 0.1 && ageInHours > 24) {
        itemsToRemove.push(key);
      }
    }
    
    itemsToRemove.forEach(key => this.delete(key));
    
    if (itemsToRemove.length > 0) {
      console.debug(`Cache optimization: removed ${itemsToRemove.length} low-value items`);
    }
  }

  public destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }
    
    this.saveToPersistence();
    this.cache.clear();
    this.accessOrder = [];
  }
}

// Singleton instance
const cacheManager = new CacheManager();

// React hook for cache management
export const useCacheManager = () => {
  return {
    set: cacheManager.set.bind(cacheManager),
    get: cacheManager.get.bind(cacheManager),
    has: cacheManager.has.bind(cacheManager),
    delete: cacheManager.delete.bind(cacheManager),
    clear: cacheManager.clear.bind(cacheManager),
    getByTag: cacheManager.getByTag.bind(cacheManager),
    getMetrics: cacheManager.getMetrics.bind(cacheManager),
    optimize: cacheManager.optimizeCache.bind(cacheManager)
  };
};

export default cacheManager;