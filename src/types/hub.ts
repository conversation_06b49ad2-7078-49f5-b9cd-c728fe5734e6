/**
 * Type definitions for Hub Dashboard functionality
 */

// ============================================================================
// Core Hub Types
// ============================================================================

export interface HubWidget {
  id: string;
  type: 'reddit' | 'github-trending' | 'coding-progress' | 'ai-news' | 'learning';
  title: string;
  position: WidgetPosition;
  size: WidgetSize;
  isEnabled: boolean;
  refreshInterval?: number; // in minutes
  settings: WidgetSettings;
  lastUpdated?: Date;
}

export interface WidgetPosition {
  x: number;
  y: number;
  w: number;
  h: number;
}

export interface WidgetSize {
  minW: number;
  minH: number;
  maxW?: number;
  maxH?: number;
}

export interface WidgetSettings {
  [key: string]: any;
}

// ============================================================================
// Reddit Widget Types
// ============================================================================

export interface RedditPost {
  id: string;
  title: string;
  author: string;
  score: number;
  url: string;
  permalink: string;
  subreddit: string;
  created_utc: number;
  num_comments: number;
  is_self: boolean;
  selftext?: string;
  preview?: RedditPreview;
}

export interface RedditPreview {
  images: Array<{
    source: {
      url: string;
      width: number;
      height: number;
    };
  }>;
}

export interface RedditWidgetSettings extends WidgetSettings {
  subreddits: string[];
  postLimit: number;
  sortBy: 'hot' | 'new' | 'top' | 'rising';
  timeRange?: 'hour' | 'day' | 'week' | 'month' | 'year' | 'all';
}

// ============================================================================
// GitHub Trending Types
// ============================================================================

export interface GitHubTrendingRepo {
  id: number;
  name: string;
  full_name: string;
  description: string | null;
  html_url: string;
  stargazers_count: number;
  forks_count: number;
  language: string | null;
  topics: string[];
  created_at: string;
  updated_at: string;
  owner: {
    login: string;
    avatar_url: string;
  };
  stars_today?: number;
  rank?: number;
}

export interface GitHubTrendingWidgetSettings extends WidgetSettings {
  timeRange: 'daily' | 'weekly' | 'monthly';
  language?: string;
  repoLimit: number;
  includeStarsToday: boolean;
  showDevelopers?: boolean;
  showTopics?: boolean;
  defaultView?: 'repositories' | 'developers' | 'topics';
  viewMode?: 'compact' | 'detailed';
}

// ============================================================================
// Coding Progress Types
// ============================================================================

export interface CodingProgress {
  totalProjects: number;
  activeSessions: number;
  linesOfCode: number;
  commitsToday: number;
  sessionsThisWeek: number;
  topLanguages: Array<{
    language: string;
    percentage: number;
    color: string;
  }>;
  recentActivity: CodingActivity[];
  productivity: ProductivityMetrics;
}

export interface CodingActivity {
  date: string;
  sessions: number;
  duration: number; // in minutes
  commits: number;
  filesModified: number;
}

export interface ProductivityMetrics {
  averageSessionDuration: number;
  peakProductivityTime: string;
  weeklyGoalProgress: number;
  monthlyGoalProgress: number;
}

export interface CodingProgressWidgetSettings extends WidgetSettings {
  timeRange: 'today' | 'week' | 'month' | 'year';
  showGoals: boolean;
  showLanguageBreakdown: boolean;
  showActivityChart: boolean;
}

// ============================================================================
// AI News Types
// ============================================================================

export interface AINewsItem {
  id: string;
  title: string;
  description: string;
  url: string;
  source: string;
  publishedAt: Date;
  category: 'ai' | 'ml' | 'nlp' | 'cv' | 'robotics' | 'general';
  tags: string[];
  score?: number;
  isBookmarked?: boolean;
}

export interface AINewsWidgetSettings extends WidgetSettings {
  sources: Array<'hackernews' | 'arxiv' | 'aiweekly' | 'mit-news' | 'openai-blog'>;
  categories: Array<'ai' | 'ml' | 'nlp' | 'cv' | 'robotics' | 'general'>;
  itemLimit: number;
  keywords: string[];
  timeRange: 'day' | 'week' | 'month';
}

// ============================================================================
// Learning Widget Types
// ============================================================================

export interface LearningResource {
  id: string;
  title: string;
  description: string;
  type: 'course' | 'tutorial' | 'paper' | 'book' | 'video' | 'documentation';
  url: string;
  provider: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  duration?: string;
  rating?: number;
  price?: 'free' | 'paid';
  tags: string[];
  progress?: number; // 0-100
  isBookmarked?: boolean;
}

export interface LearningWidgetSettings extends WidgetSettings {
  resourceTypes: Array<'course' | 'tutorial' | 'paper' | 'book' | 'video' | 'documentation'>;
  difficulty: Array<'beginner' | 'intermediate' | 'advanced'>;
  providers: string[];
  topics: string[];
  showProgress: boolean;
  sortBy: 'relevance' | 'rating' | 'recent' | 'popular';
}

// ============================================================================
// Hub Dashboard State Types
// ============================================================================

export interface HubState {
  widgets: HubWidget[];
  layout: WidgetPosition[];
  selectedWidgetId: string | null;
  isEditing: boolean;
  isLoading: boolean;
  error: string | null;
  lastRefresh: Date | null;
  autoRefresh: boolean;
  refreshInterval: number; // global refresh interval in minutes
}

export interface HubSettings {
  // Global refresh settings
  autoRefresh: boolean;
  refreshInterval: number;
  enabledWidgets: string[];

  // Layout settings
  layout: {
    enableDragDrop: boolean;
    snapToGrid: boolean;
    gridSize: number;
    layoutPresets: LayoutPreset[];
    currentPreset?: string;
    showGridLines: boolean;
    responsiveBreakpoints: ResponsiveBreakpoint[];
  };

  // Theme and appearance
  theme: {
    mode: 'light' | 'dark' | 'auto';
    customColors?: CustomColorScheme;
    compactMode: boolean;
    showWidgetTitles: boolean;
    animationsEnabled: boolean;
    borderRadius: 'none' | 'small' | 'medium' | 'large';
    widgetSpacing: 'tight' | 'normal' | 'relaxed';
    fontScale: 'small' | 'normal' | 'large';
  };

  // Widget-specific settings
  widgetSettings: {
    [widgetId: string]: WidgetSpecificSettings;
  };

  // User profile and preferences
  userProfile: UserProfile;

  // Notification settings
  notifications: NotificationSettings;

  // Privacy settings
  privacy: PrivacySettings;

  // Performance settings
  performance: PerformanceSettings;

  // Accessibility settings
  accessibility: AccessibilitySettings;

  // Keyboard shortcuts
  keyboardShortcuts: KeyboardShortcuts;

  // Advanced features
  advanced: AdvancedSettings;
}

export interface LayoutPreset {
  id: string;
  name: string;
  description: string;
  layout: WidgetPosition[];
  widgetTypes: HubWidget['type'][];
  isDefault?: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface ResponsiveBreakpoint {
  name: string;
  minWidth: number;
  cols: number;
  rowHeight: number;
  margin: [number, number];
  containerPadding: [number, number];
}

export interface CustomColorScheme {
  primary: string;
  secondary: string;
  accent: string;
  background: string;
  surface: string;
  text: string;
  textSecondary: string;
  border: string;
  success: string;
  warning: string;
  error: string;
}

export interface WidgetSpecificSettings {
  [key: string]: any;
}

export interface UserProfile {
  name?: string;
  avatar?: string;
  skills: string[];
  interests: string[];
  experienceLevel: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  preferredLanguages: string[];
  timezone: string;
  workingHours?: {
    start: string;
    end: string;
    timezone: string;
  };
  github?: {
    username: string;
    connected: boolean;
  };
  preferences: {
    contentTypes: string[];
    difficultyLevels: string[];
    topics: string[];
  };
}

export interface NotificationSettings {
  enabled: boolean;
  updateNotifications: boolean;
  errorNotifications: boolean;
  achievementNotifications: boolean;
  weeklyDigest: boolean;
  emailNotifications: boolean;
  pushNotifications: boolean;
  notificationSound: boolean;
  quietHours?: {
    enabled: boolean;
    start: string;
    end: string;
  };
}

export interface PrivacySettings {
  dataSharing: boolean;
  analytics: boolean;
  crashReporting: boolean;
  usageStatistics: boolean;
  personalizedContent: boolean;
  thirdPartyIntegrations: boolean;
  dataRetention: '30days' | '6months' | '1year' | 'forever';
  exportData: boolean;
  deleteData: boolean;
}

export interface PerformanceSettings {
  cacheSize: number; // MB
  maxCacheAge: number; // minutes
  preloadWidgets: boolean;
  lazyLoadImages: boolean;
  backgroundRefresh: boolean;
  maxConcurrentRequests: number;
  requestTimeout: number; // seconds
  enableVirtualization: boolean;
  reduceAnimations: boolean;
  compressedMode: boolean;
}

export interface AccessibilitySettings {
  highContrast: boolean;
  reducedMotion: boolean;
  largeText: boolean;
  screenReaderSupport: boolean;
  keyboardNavigation: boolean;
  focusIndicators: boolean;
  altTextImages: boolean;
  colorBlindSupport: 'none' | 'protanopia' | 'deuteranopia' | 'tritanopia';
  voiceAnnouncements: boolean;
}

export interface KeyboardShortcuts {
  enabled: boolean;
  shortcuts: {
    [action: string]: string;
  };
  customShortcuts: CustomShortcut[];
}

export interface CustomShortcut {
  id: string;
  name: string;
  key: string;
  action: string;
  scope: 'global' | 'widget';
  enabled: boolean;
}

export interface AdvancedSettings {
  enableBetaFeatures: boolean;
  developerMode: boolean;
  debugMode: boolean;
  experimentalFeatures: string[];
  apiEndpoints: {
    [service: string]: string;
  };
  customCSS?: string;
  backupSettings: {
    autoBackup: boolean;
    backupInterval: 'daily' | 'weekly' | 'monthly';
    maxBackups: number;
  };
}

// ============================================================================
// API Response Types
// ============================================================================

export interface HubDataResponse<T> {
  success: boolean;
  data: T;
  error?: string;
  timestamp: Date;
  cached: boolean;
  cacheExpiry?: Date;
}

// ============================================================================
// Widget Configuration
// ============================================================================

export interface WidgetConfig {
  type: HubWidget['type'];
  title: string;
  description: string;
  icon: string;
  defaultPosition: WidgetPosition;
  defaultSize: WidgetSize;
  defaultSettings: WidgetSettings;
  minRefreshInterval: number;
  configurable: boolean;
  component: React.ComponentType<WidgetProps>;
}

export interface WidgetProps {
  widget: HubWidget;
  isEditing: boolean;
  onUpdate: (updates: Partial<HubWidget>) => void;
  onRemove: () => void;
  onRefresh: () => void;
}

// ============================================================================
// Cache Types
// ============================================================================

export interface HubCache {
  key: string;
  data: any;
  timestamp: Date;
  expiry: Date;
  widgetId: string;
  widgetType: string;
}

// ============================================================================
// Error Types
// ============================================================================

export interface HubError {
  code: string;
  message: string;
  details?: any;
  widgetId?: string;
  timestamp: Date;
}

// ============================================================================
// Default Values
// ============================================================================

export const DEFAULT_WIDGET_SIZES = {
  reddit: { minW: 4, minH: 6, maxW: 8, maxH: 12 },
  'github-trending': { minW: 4, minH: 6, maxW: 8, maxH: 10 },
  'coding-progress': { minW: 6, minH: 4, maxW: 12, maxH: 8 },
  'ai-news': { minW: 4, minH: 6, maxW: 8, maxH: 12 },
  'learning': { minW: 4, minH: 6, maxW: 8, maxH: 10 },
};

export const DEFAULT_RESPONSIVE_BREAKPOINTS: ResponsiveBreakpoint[] = [
  {
    name: 'lg',
    minWidth: 1200,
    cols: 12,
    rowHeight: 60,
    margin: [10, 10],
    containerPadding: [20, 20],
  },
  {
    name: 'md',
    minWidth: 996,
    cols: 10,
    rowHeight: 60,
    margin: [10, 10],
    containerPadding: [20, 20],
  },
  {
    name: 'sm',
    minWidth: 768,
    cols: 6,
    rowHeight: 60,
    margin: [10, 10],
    containerPadding: [20, 20],
  },
  {
    name: 'xs',
    minWidth: 480,
    cols: 4,
    rowHeight: 60,
    margin: [10, 10],
    containerPadding: [20, 20],
  },
  {
    name: 'xxs',
    minWidth: 0,
    cols: 2,
    rowHeight: 60,
    margin: [10, 10],
    containerPadding: [20, 20],
  },
];

export const DEFAULT_KEYBOARD_SHORTCUTS = {
  enabled: true,
  shortcuts: {
    'hub.refresh': 'r',
    'hub.settings': 's',
    'hub.toggleEdit': 'e',
    'hub.addWidget': 'a',
    'hub.search': '/',
    'hub.help': '?',
    'widget.refresh': 'shift+r',
    'widget.settings': 'shift+s',
    'widget.remove': 'shift+x',
    'widget.duplicate': 'shift+d',
  },
  customShortcuts: [],
};

export const DEFAULT_HUB_SETTINGS: HubSettings = {
  // Global refresh settings
  autoRefresh: true,
  refreshInterval: 30,
  enabledWidgets: ['reddit', 'github-trending', 'coding-progress'],

  // Layout settings
  layout: {
    enableDragDrop: true,
    snapToGrid: true,
    gridSize: 10,
    layoutPresets: [],
    showGridLines: false,
    responsiveBreakpoints: DEFAULT_RESPONSIVE_BREAKPOINTS,
  },

  // Theme and appearance
  theme: {
    mode: 'auto',
    compactMode: false,
    showWidgetTitles: true,
    animationsEnabled: true,
    borderRadius: 'medium',
    widgetSpacing: 'normal',
    fontScale: 'normal',
  },

  // Widget-specific settings
  widgetSettings: {},

  // User profile and preferences
  userProfile: {
    skills: [],
    interests: [],
    experienceLevel: 'intermediate',
    preferredLanguages: ['JavaScript', 'TypeScript', 'Python'],
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    preferences: {
      contentTypes: ['article', 'tutorial', 'documentation'],
      difficultyLevels: ['intermediate', 'advanced'],
      topics: ['programming', 'ai', 'web-development'],
    },
  },

  // Notification settings
  notifications: {
    enabled: true,
    updateNotifications: true,
    errorNotifications: true,
    achievementNotifications: false,
    weeklyDigest: true,
    emailNotifications: false,
    pushNotifications: false,
    notificationSound: false,
  },

  // Privacy settings
  privacy: {
    dataSharing: false,
    analytics: true,
    crashReporting: true,
    usageStatistics: true,
    personalizedContent: true,
    thirdPartyIntegrations: true,
    dataRetention: '6months',
    exportData: true,
    deleteData: true,
  },

  // Performance settings
  performance: {
    cacheSize: 50, // MB
    maxCacheAge: 30, // minutes
    preloadWidgets: true,
    lazyLoadImages: true,
    backgroundRefresh: true,
    maxConcurrentRequests: 5,
    requestTimeout: 10, // seconds
    enableVirtualization: false,
    reduceAnimations: false,
    compressedMode: false,
  },

  // Accessibility settings
  accessibility: {
    highContrast: false,
    reducedMotion: false,
    largeText: false,
    screenReaderSupport: false,
    keyboardNavigation: true,
    focusIndicators: true,
    altTextImages: true,
    colorBlindSupport: 'none',
    voiceAnnouncements: false,
  },

  // Keyboard shortcuts
  keyboardShortcuts: DEFAULT_KEYBOARD_SHORTCUTS,

  // Advanced features
  advanced: {
    enableBetaFeatures: false,
    developerMode: false,
    debugMode: false,
    experimentalFeatures: [],
    apiEndpoints: {},
    backupSettings: {
      autoBackup: true,
      backupInterval: 'weekly',
      maxBackups: 5,
    },
  },
};

export const WIDGET_REFRESH_INTERVALS = {
  reddit: 30,
  'github-trending': 60,
  'coding-progress': 15,
  'ai-news': 45,
  'learning': 120,
};