/**
 * TypeScript interfaces for Marketplace functionality
 * Extends existing Agent interface and follows established patterns
 */

import type { Agent } from '@/lib/api';

// ============================================================================
// Core Marketplace Types
// ============================================================================

/**
 * Marketplace Agent - extends the base Agent with marketplace-specific fields
 */
export interface MarketplaceAgent extends Agent {
  // Required marketplace fields
  remote_id: string; // Unique identifier from marketplace/GitHub
  author: string;
  description: string;
  version: string;
  download_url: string;
  
  // Optional marketplace fields
  author_url?: string;
  long_description?: string;
  license?: string;
  tags?: string[]; // Parsed from JSON
  category_id?: number;
  category?: AgentCategory; // Populated via JOIN
  
  // Statistics
  download_count: number;
  rating_average: number;
  rating_count: number;
  
  // URLs and metadata
  source_url?: string;
  documentation_url?: string;
  homepage_url?: string;
  sha?: string;
  file_size: number;
  
  // Installation tracking
  is_installed: boolean;
  installed_at?: string;
  installation_source?: 'github' | 'marketplace' | 'manual';
  
  // Timestamps
  published_at?: string;
  last_synced_at?: string;
  
  // Relationships (populated when needed)
  ratings?: AgentRating[];
  dependencies?: AgentDependency[];
  collections?: AgentCollection[];
}

/**
 * Agent Category for classification
 */
export interface AgentCategory {
  id: number;
  name: string;
  slug: string;
  description?: string;
  icon?: string;
  parent_id?: number;
  parent?: AgentCategory;
  children?: AgentCategory[];
  sort_order: number;
  is_active: boolean;
  created_at: string;
  
  // Computed fields
  agent_count?: number;
}

/**
 * Agent Rating and Review
 */
export interface AgentRating {
  id: number;
  marketplace_agent_id: number;
  user_identifier?: string; // For current user's ratings
  rating: 1 | 2 | 3 | 4 | 5;
  review_title?: string;
  review_content?: string;
  is_verified: boolean;
  helpful_votes: number;
  total_votes: number;
  created_at: string;
  updated_at: string;
  
  // Relationships
  agent?: MarketplaceAgent;
}

/**
 * Agent Dependency
 */
export interface AgentDependency {
  id: number;
  marketplace_agent_id: number;
  dependency_type: 'agent' | 'tool' | 'binary' | 'npm_package';
  dependency_name: string;
  dependency_version?: string; // Semver constraint
  is_required: boolean;
  created_at: string;
  
  // Status (computed at runtime)
  is_satisfied?: boolean;
  installed_version?: string;
}

/**
 * Agent Collection (curated lists)
 */
export interface AgentCollection {
  id: number;
  name: string;
  description?: string;
  curator?: string;
  is_featured: boolean;
  is_public: boolean;
  sort_order: number;
  created_at: string;
  updated_at: string;
  
  // Relationships
  agents?: MarketplaceAgent[];
  agent_count?: number;
}

/**
 * Agent Collection Item (junction table)
 */
export interface AgentCollectionItem {
  id: number;
  collection_id: number;
  marketplace_agent_id: number;
  sort_order: number;
  added_at: string;
  
  // Relationships
  collection?: AgentCollection;
  agent?: MarketplaceAgent;
}

/**
 * Agent Download tracking
 */
export interface AgentDownload {
  id: number;
  marketplace_agent_id: number;
  user_identifier?: string;
  download_source: 'marketplace' | 'github';
  download_url?: string;
  success: boolean;
  error_message?: string;
  downloaded_at: string;
}

/**
 * User Preferences for marketplace
 */
export interface UserPreference {
  key: string;
  value: string;
  value_type: 'string' | 'number' | 'boolean' | 'json';
  created_at: string;
  updated_at: string;
}

/**
 * Marketplace Cache entry
 */
export interface MarketplaceCache {
  id: number;
  cache_key: string;
  cache_type: 'agent_list' | 'agent_detail' | 'category_list' | 'featured_collections';
  data: string; // JSON data
  expires_at: string;
  created_at: string;
}

// ============================================================================
// API Request/Response Types
// ============================================================================

/**
 * Marketplace search/filter parameters
 */
export interface MarketplaceSearchParams {
  query?: string;
  category_id?: number;
  tags?: string[];
  author?: string;
  sort_by?: 'downloads' | 'rating' | 'updated' | 'alphabetical' | 'newest';
  sort_order?: 'asc' | 'desc';
  include_installed?: boolean;
  include_experimental?: boolean;
  min_rating?: number;
  page?: number;
  limit?: number;
}

/**
 * Marketplace search results
 */
export interface MarketplaceSearchResult {
  agents: MarketplaceAgent[];
  total_count: number;
  page: number;
  limit: number;
  total_pages: number;
  filters_applied: MarketplaceSearchParams;
}

/**
 * Agent installation request
 */
export interface AgentInstallRequest {
  remote_id: string;
  source: 'github' | 'marketplace';
  force_reinstall?: boolean;
  install_dependencies?: boolean;
}

/**
 * Agent installation result
 */
export interface AgentInstallResult {
  success: boolean;
  agent?: Agent; // Converted to local Agent after installation
  message: string;
  errors?: string[];
  warnings?: string[];
  installed_dependencies?: string[];
  skipped_dependencies?: string[];
}

/**
 * Agent rating submission
 */
export interface AgentRatingSubmission {
  marketplace_agent_id: number;
  rating: 1 | 2 | 3 | 4 | 5;
  review_title?: string;
  review_content?: string;
}

/**
 * Bulk agent sync request
 */
export interface AgentSyncRequest {
  source: 'github' | 'marketplace';
  force_refresh?: boolean;
  categories?: string[]; // Only sync specific categories
}

/**
 * Bulk agent sync result
 */
export interface AgentSyncResult {
  success: boolean;
  agents_updated: number;
  agents_added: number;
  agents_removed: number;
  errors: string[];
  warnings: string[];
  sync_duration_ms: number;
}

// ============================================================================
// Validation and Error Types
// ============================================================================

/**
 * Marketplace agent validation result
 */
export interface MarketplaceAgentValidation {
  valid: boolean;
  errors: MarketplaceValidationError[];
  warnings: MarketplaceValidationWarning[];
}

export interface MarketplaceValidationError {
  field: string;
  message: string;
  code: string;
}

export interface MarketplaceValidationWarning {
  field: string;
  message: string;
  code: string;
}

// ============================================================================
// Utility Types
// ============================================================================

/**
 * Marketplace configuration
 */
export interface MarketplaceConfig {
  enabled: boolean;
  auto_update_agents: boolean;
  download_confirmations: boolean;
  preferred_categories: string[];
  cache_expiry_hours: number;
  show_experimental: boolean;
  default_sort_order: 'downloads' | 'rating' | 'updated' | 'alphabetical';
  anonymous_analytics: boolean;
  
  // GitHub specific
  github_token?: string;
  github_repo: string;
  github_branch: string;
  
  // Marketplace API
  marketplace_api_url?: string;
  marketplace_api_key?: string;
}

/**
 * Marketplace statistics
 */
export interface MarketplaceStats {
  total_agents: number;
  total_downloads: number;
  total_ratings: number;
  average_rating: number;
  categories_count: number;
  active_users_count: number;
  popular_tags: Array<{ tag: string; count: number }>;
  top_authors: Array<{ author: string; agent_count: number }>;
  recent_activity: {
    new_agents_this_week: number;
    downloads_this_week: number;
    ratings_this_week: number;
  };
}

/**
 * Agent comparison data
 */
export interface AgentComparison {
  agents: MarketplaceAgent[];
  comparison_fields: Array<{
    field: keyof MarketplaceAgent;
    label: string;
    type: 'string' | 'number' | 'boolean' | 'array' | 'rating';
  }>;
  similarities: string[];
  differences: string[];
}

// ============================================================================
// Event Types for Real-time Updates
// ============================================================================

export interface MarketplaceEvent {
  type: 'agent_installed' | 'agent_updated' | 'agent_rated' | 'sync_completed';
  timestamp: string;
  data: unknown;
}

export interface AgentInstalledEvent extends MarketplaceEvent {
  type: 'agent_installed';
  data: {
    agent: Agent;
    source: 'github' | 'marketplace';
    install_duration_ms: number;
  };
}

export interface AgentUpdatedEvent extends MarketplaceEvent {
  type: 'agent_updated';
  data: {
    agent: Agent;
    old_version: string;
    new_version: string;
  };
}

export interface AgentRatedEvent extends MarketplaceEvent {
  type: 'agent_rated';
  data: {
    rating: AgentRating;
    agent_name: string;
  };
}

export interface SyncCompletedEvent extends MarketplaceEvent {
  type: 'sync_completed';
  data: AgentSyncResult;
}

// ============================================================================
// Extended API Types
// ============================================================================

/**
 * Extended GitHubAgentFile with marketplace metadata
 */
export interface EnhancedGitHubAgentFile {
  name: string;
  path: string;
  download_url: string;
  size: number;
  sha: string;
  
  // Enhanced metadata
  parsed_metadata?: {
    name: string;
    author: string;
    version: string;
    description: string;
    tags: string[];
    category: string;
    license: string;
  };
  
  // Installation status
  is_installed?: boolean;
  local_agent_id?: number;
  needs_update?: boolean;
  
  // Validation
  is_valid?: boolean;
  validation_errors?: string[];
}

/**
 * Marketplace agent export (extends existing AgentExport)
 */
export interface MarketplaceAgentExport {
  version: number;
  exported_at: string;
  agent: {
    name: string;
    icon: string;
    system_prompt: string;
    default_task?: string;
    model: string;
    hooks?: string;
    
    // Marketplace metadata
    author: string;
    author_url?: string;
    description: string;
    long_description?: string;
    version: string;
    license: string;
    tags: string[];
    category: string;
    dependencies: Array<{
      type: 'agent' | 'tool' | 'binary' | 'npm_package';
      name: string;
      version?: string;
      required: boolean;
    }>;
    
    // URLs
    source_url?: string;
    documentation_url?: string;
    homepage_url?: string;
  };
}

/**
 * Type guards for runtime type checking
 */
export const isMarketplaceAgent = (agent: Agent | MarketplaceAgent): agent is MarketplaceAgent => {
  return 'remote_id' in agent && 'author' in agent && 'description' in agent;
};

export const isAgentRating = (obj: unknown): obj is AgentRating => {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    'rating' in obj &&
    'marketplace_agent_id' in obj
  );
};

// ============================================================================
// Default Values and Constants
// ============================================================================

export const DEFAULT_MARKETPLACE_CONFIG: MarketplaceConfig = {
  enabled: true,
  auto_update_agents: false,
  download_confirmations: true,
  preferred_categories: [],
  cache_expiry_hours: 24,
  show_experimental: false,
  default_sort_order: 'downloads',
  anonymous_analytics: true,
  github_repo: 'getAsterisk/claudia',
  github_branch: 'main'
};

export const MARKETPLACE_SORT_OPTIONS = [
  { value: 'downloads', label: 'Most Downloaded' },
  { value: 'rating', label: 'Highest Rated' },
  { value: 'updated', label: 'Recently Updated' },
  { value: 'newest', label: 'Newest' },
  { value: 'alphabetical', label: 'Name (A-Z)' }
] as const;

export const RATING_LABELS = {
  1: 'Poor',
  2: 'Fair',
  3: 'Good',
  4: 'Very Good',
  5: 'Excellent'
} as const;

export const DEPENDENCY_TYPES = [
  'agent',
  'tool',
  'binary',
  'npm_package'
] as const;

export const INSTALLATION_SOURCES = [
  'github',
  'marketplace',
  'manual'
] as const;