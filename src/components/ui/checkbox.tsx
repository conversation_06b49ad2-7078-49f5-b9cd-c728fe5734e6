import * as React from "react";
import { Check } from "lucide-react";
import { cn } from "@/lib/utils";

export interface CheckboxProps {
  checked?: boolean;
  defaultChecked?: boolean;
  onCheckedChange?: (checked: boolean) => void;
  disabled?: boolean;
  className?: string;
  id?: string;
}

/**
 * A checkbox component for boolean selection
 * 
 * @example
 * <Checkbox 
 *   checked={isChecked}
 *   onCheckedChange={setIsChecked}
 * />
 */
export const Checkbox = React.forwardRef<HTMLInputElement, CheckboxProps>(
  ({ className, checked, defaultChecked, onCheckedChange, disabled, id, ...props }, ref) => {
    const [internalChecked, setInternalChecked] = React.useState(checked ?? defaultChecked ?? false);
    
    React.useEffect(() => {
      if (checked !== undefined) {
        setInternalChecked(checked);
      }
    }, [checked]);

    const isChecked = checked !== undefined ? checked : internalChecked;

    const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
      const newChecked = event.target.checked;
      if (checked === undefined) {
        setInternalChecked(newChecked);
      }
      onCheckedChange?.(newChecked);
    };

    return (
      <div className="relative flex items-center">
        <input
          ref={ref}
          type="checkbox"
          id={id}
          checked={isChecked}
          disabled={disabled}
          onChange={handleChange}
          className="sr-only"
          {...props}
        />
        <div
          className={cn(
            "flex h-4 w-4 items-center justify-center rounded-sm border border-primary ring-offset-background transition-colors",
            "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
            "disabled:cursor-not-allowed disabled:opacity-50",
            isChecked && "bg-primary text-primary-foreground",
            !isChecked && "bg-background",
            disabled && "opacity-50 cursor-not-allowed",
            className
          )}
          onClick={() => !disabled && handleChange({ target: { checked: !isChecked } } as any)}
        >
          {isChecked && (
            <Check className="h-3 w-3" />
          )}
        </div>
      </div>
    );
  }
);

Checkbox.displayName = "Checkbox";