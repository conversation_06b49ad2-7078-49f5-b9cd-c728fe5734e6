"use client";

import { cn } from "@/lib/utils";
import React, {
  ReactNode,
  useCallback,
  useState,
} from "react";



interface MagicContainerProps {
  children?: ReactNode;
  className?: string;
}

const MagicContainer = ({ children, className }: MagicContainerProps) => {
  const containerRef = React.useRef<HTMLDivElement>(null);
  const [mouseX, setMouseX] = useState(0);
  const [mouseY, setMouseY] = useState(0);

  const handleMouseMove = useCallback(
    (e: React.MouseEvent<HTMLDivElement>) => {
      if (containerRef.current) {
        const rect = containerRef.current.getBoundingClientRect();
        setMouseX(e.clientX - rect.left);
        setMouseY(e.clientY - rect.top);
      }
    },
    [],
  );



  return (
    <div
      ref={containerRef}
      onMouseMove={handleMouseMove}

      className={cn("group relative overflow-hidden", className)}
    >
      <div
        className="pointer-events-none absolute -inset-px opacity-0 transition duration-300 group-hover:opacity-100"
        style={{
          background: `radial-gradient(600px circle at ${mouseX}px ${mouseY}px, rgba(255,182,255,.1), transparent 40%)`,
        }}
      />
      {children}
    </div>
  );
};

interface MagicCardProps {
  children: ReactNode;
  className?: string;
  gradientSize?: number;
  gradientColor?: string;
  gradientOpacity?: number;
}

const MagicCard = ({
  children,
  className,
  gradientSize = 200,
  gradientColor = "#262626",
  gradientOpacity = 0.8,
}: MagicCardProps) => {
  const [mousePosition, setMousePosition] = useState({ x: -gradientSize, y: -gradientSize });
  const [isHovered, setIsHovered] = useState(false);

  const handleMouseMove = useCallback(
    (e: React.MouseEvent<HTMLDivElement>) => {
      const rect = e.currentTarget.getBoundingClientRect();
      setMousePosition({
        x: e.clientX - rect.left,
        y: e.clientY - rect.top,
      });
    },
    []
  );

  const handleMouseEnter = useCallback(() => {
    setIsHovered(true);
  }, []);

  const handleMouseLeave = useCallback(() => {
    setIsHovered(false);
    setMousePosition({ x: -gradientSize, y: -gradientSize });
  }, [gradientSize]);

  return (
    <div
      onMouseMove={handleMouseMove}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      className={cn(
        "group relative overflow-hidden rounded-xl bg-neutral-100 p-4 dark:bg-neutral-900",
        className,
      )}
    >
      <div
        className="pointer-events-none absolute transition-opacity duration-300 ease-in-out"
        style={{
          background: `radial-gradient(${gradientSize}px circle at ${mousePosition.x}px ${mousePosition.y}px, ${gradientColor}, transparent 100%)`,
          opacity: isHovered ? gradientOpacity : 0,
          left: 0,
          top: 0,
          right: 0,
          bottom: 0,
        }}
      />
      <div className="relative z-10">{children}</div>
    </div>
  );
};

export { MagicCard, MagicContainer };