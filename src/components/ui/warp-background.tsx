"use client";

import { cn } from "@/lib/utils";
import React from "react";

interface WarpBackgroundProps {
  children?: React.ReactNode;
  className?: string;
  containerClassName?: string;
  colors?: string[];
  waveWidth?: number;
  backgroundFill?: string;
  blur?: number;
  speed?: "slow" | "fast";
  waveOpacity?: number;
  [key: string]: any;
}

export const WarpBackground = ({
  children,
  className,
  containerClassName,
  colors = ["#38bdf8", "#818cf8", "#c084fc", "#e879f9", "#22d3ee"],
  waveWidth = 50,
  backgroundFill = "black",
  blur = 10,
  speed = "fast",
  waveOpacity = 0.5,
  ...props
}: WarpBackgroundProps) => {
  return (
    <div
      className={cn(
        "relative flex h-screen w-full items-center justify-center overflow-hidden rounded-lg border bg-background md:shadow-xl",
        containerClassName,
      )}
      {...props}
    >
      <svg
        className="absolute inset-0 h-full w-full"
        width="100%"
        height="100%"
        viewBox="0 0 400 400"
        xmlns="http://www.w3.org/2000/svg"
      >
        <defs>
          <filter id="blurMe">
            <feGaussianBlur in="SourceGraphic" stdDeviation={blur} />
          </filter>
        </defs>
        <rect width="100%" height="100%" fill={backgroundFill} />
        <g mask="url(#mask)" fill="none">
          {colors.map((color, index) => (
            <rect
              key={index}
              fill={color}
              opacity={waveOpacity}
              width="100%"
              height="100%"
            >
              <animateTransform
                attributeName="transform"
                type="translate"
                values={`-${waveWidth} 0; ${waveWidth} 0; -${waveWidth} 0`}
                dur={speed === "fast" ? "3s" : "8s"}
                begin={`${index * 0.5}s`}
                repeatCount="indefinite"
              />
            </rect>
          ))}
        </g>
        <rect width="100%" height="100%" fill={backgroundFill} mask="url(#mask)" />
        <defs>
          <mask id="mask">
            <rect width="100%" height="100%" fill="black" />
            <path
              d="m0,200 h400"
              stroke="white"
              strokeWidth={waveWidth}
              filter="url(#blurMe)"
            >
              <animateTransform
                attributeName="transform"
                type="translate"
                values="0 -200; 0 200; 0 -200"
                dur={speed === "fast" ? "4s" : "10s"}
                repeatCount="indefinite"
              />
            </path>
          </mask>
        </defs>
      </svg>
      <div className={cn("relative z-10", className)}>{children}</div>
    </div>
  );
};