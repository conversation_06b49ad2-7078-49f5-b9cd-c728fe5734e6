import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, CheckCircle, AlertCircle, Info, AlertTriangle } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from './button';
import type { Toast } from '@/hooks/useToast';

interface ToastItemProps extends Toast {
  onDismiss: (id: string) => void;
}

const ToastItem: React.FC<ToastItemProps> = ({
  id,
  message,
  type,
  action,
  onDismiss,
}) => {
  const icons = {
    success: <CheckCircle className="h-4 w-4" />,
    error: <AlertCircle className="h-4 w-4" />,
    info: <Info className="h-4 w-4" />,
    warning: <AlertTriangle className="h-4 w-4" />,
  };

  const colors = {
    success: {
      icon: 'text-green-500',
      bg: 'bg-green-50 border-green-200 dark:bg-green-950/20 dark:border-green-800',
    },
    error: {
      icon: 'text-red-500',
      bg: 'bg-red-50 border-red-200 dark:bg-red-950/20 dark:border-red-800',
    },
    info: {
      icon: 'text-blue-500',
      bg: 'bg-blue-50 border-blue-200 dark:bg-blue-950/20 dark:border-blue-800',
    },
    warning: {
      icon: 'text-yellow-500',
      bg: 'bg-yellow-50 border-yellow-200 dark:bg-yellow-950/20 dark:border-yellow-800',
    },
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 50, scale: 0.95 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      exit={{ opacity: 0, x: 300, scale: 0.95 }}
      transition={{ duration: 0.2 }}
      className={cn(
        'relative flex items-center gap-3 rounded-lg border p-4 shadow-lg backdrop-blur-sm',
        'min-w-[320px] max-w-[400px]',
        colors[type].bg
      )}
    >
      <span className={colors[type].icon}>
        {icons[type]}
      </span>
      
      <div className="flex-1 space-y-1">
        <p className="text-sm font-medium text-foreground">{message}</p>
        {action && (
          <Button
            size="sm"
            variant="outline"
            onClick={action.onClick}
            className="h-7 px-2 text-xs"
          >
            {action.label}
          </Button>
        )}
      </div>

      <button
        onClick={() => onDismiss(id)}
        className="text-muted-foreground hover:text-foreground transition-colors rounded-sm opacity-70 hover:opacity-100"
      >
        <X className="h-4 w-4" />
        <span className="sr-only">Dismiss</span>
      </button>
    </motion.div>
  );
};

interface ToastStackProps {
  toasts: Toast[];
  onDismiss: (id: string) => void;
}

export const ToastStack: React.FC<ToastStackProps> = ({ toasts, onDismiss }) => {
  return (
    <div className="fixed bottom-0 right-0 z-50 flex flex-col-reverse gap-2 p-4 pointer-events-none">
      <AnimatePresence mode="popLayout">
        {toasts.map((toast) => (
          <div key={toast.id} className="pointer-events-auto">
            <ToastItem {...toast} onDismiss={onDismiss} />
          </div>
        ))}
      </AnimatePresence>
    </div>
  );
};