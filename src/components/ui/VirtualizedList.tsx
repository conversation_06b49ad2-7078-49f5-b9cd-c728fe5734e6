/**
 * High-performance virtualized list component using @tanstack/react-virtual
 */

import React, { useMemo, useRef, CSSProperties } from 'react';
import { useVirtualizer, VirtualItem } from '@tanstack/react-virtual';
import { cn } from '@/lib/utils';
import { usePerformanceMonitor } from '@/hooks/usePerformanceMonitor';

export interface VirtualizedListProps<T = any> {
  items: T[];
  itemHeight: number | ((index: number) => number);
  renderItem: (item: T, index: number, virtualItem: VirtualItem) => React.ReactNode;
  className?: string;
  style?: CSSProperties;
  overscan?: number;
  estimateSize?: (index: number) => number;
  getItemKey?: (item: T, index: number) => string | number;
  onScroll?: (scrollTop: number) => void;
  header?: React.ReactNode;
  footer?: React.ReactNode;
  emptyState?: React.ReactNode;
  loadingState?: React.ReactNode;
  isLoading?: boolean;
  enableSmoothScrolling?: boolean;
  maintainScrollPosition?: boolean;
}

export const VirtualizedList = <T,>({
  items,
  itemHeight,
  renderItem,
  className,
  style,
  overscan = 5,
  estimateSize,
  getItemKey,
  onScroll,
  header,
  footer,
  emptyState,
  loadingState,
  isLoading = false,
  enableSmoothScrolling = true,
  maintainScrollPosition = false
}: VirtualizedListProps<T>) => {
  const parentRef = useRef<HTMLDivElement>(null);
  const { startWidgetMeasure, endWidgetMeasure } = usePerformanceMonitor();
  const listId = useMemo(() => `virtual-list-${Date.now()}`, []);

  // Get the size function
  const sizeFunction = useMemo(() => {
    if (typeof itemHeight === 'function') {
      return itemHeight;
    }
    if (estimateSize) {
      return estimateSize;
    }
    return () => itemHeight as number;
  }, [itemHeight, estimateSize]);

  const virtualizer = useVirtualizer({
    count: items.length,
    getScrollElement: () => parentRef.current,
    estimateSize: sizeFunction,
    overscan,
    observeElementRect: maintainScrollPosition ? undefined : () => ({}), // Disable rect observation if maintaining scroll
    observeElementOffset: maintainScrollPosition ? undefined : () => ({}), // Disable offset observation if maintaining scroll
  });

  // Performance monitoring
  React.useEffect(() => {
    startWidgetMeasure(listId, 'virtual-list');
    return () => endWidgetMeasure(listId);
  }, [listId, startWidgetMeasure, endWidgetMeasure]);

  // Handle scroll events
  const handleScroll = React.useCallback((e: React.UIEvent<HTMLDivElement>) => {
    const scrollTop = e.currentTarget.scrollTop;
    onScroll?.(scrollTop);
  }, [onScroll]);

  // Memoized virtual items
  const virtualItems = virtualizer.getVirtualItems();

  // Loading state
  if (isLoading && loadingState) {
    return (
      <div className={cn('flex items-center justify-center h-full', className)} style={style}>
        {loadingState}
      </div>
    );
  }

  // Empty state
  if (!isLoading && items.length === 0 && emptyState) {
    return (
      <div className={cn('flex items-center justify-center h-full', className)} style={style}>
        {emptyState}
      </div>
    );
  }

  return (
    <div
      ref={parentRef}
      className={cn(
        'h-full overflow-auto',
        enableSmoothScrolling && 'scroll-smooth',
        className
      )}
      style={style}
      onScroll={handleScroll}
      data-testid="virtualized-list"
    >
      {/* Header */}
      {header && (
        <div className="sticky top-0 z-10 bg-background">
          {header}
        </div>
      )}

      {/* Virtual list container */}
      <div
        style={{
          height: virtualizer.getTotalSize(),
          width: '100%',
          position: 'relative',
        }}
      >
        {virtualItems.map((virtualItem) => {
          const item = items[virtualItem.index];
          const key = getItemKey ? getItemKey(item, virtualItem.index) : virtualItem.index;

          return (
            <div
              key={key}
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: virtualItem.size,
                transform: `translateY(${virtualItem.start}px)`,
              }}
              data-index={virtualItem.index}
            >
              {renderItem(item, virtualItem.index, virtualItem)}
            </div>
          );
        })}
      </div>

      {/* Footer */}
      {footer && (
        <div className="sticky bottom-0 z-10 bg-background">
          {footer}
        </div>
      )}
    </div>
  );
};

// Specialized components for common use cases

interface VirtualizedGridProps<T = any> extends Omit<VirtualizedListProps<T>, 'renderItem'> {
  renderItem: (item: T, index: number) => React.ReactNode;
  columns: number;
  gap?: number;
}

export const VirtualizedGrid = <T,>({
  items,
  columns,
  gap = 16,
  renderItem,
  ...props
}: VirtualizedGridProps<T>) => {
  // Group items into rows
  const rows = useMemo(() => {
    const result: T[][] = [];
    for (let i = 0; i < items.length; i += columns) {
      result.push(items.slice(i, i + columns));
    }
    return result;
  }, [items, columns]);

  const renderRow = (row: T[], rowIndex: number) => (
    <div
      className="flex"
      style={{ gap }}
      key={`row-${rowIndex}`}
    >
      {row.map((item, colIndex) => {
        const itemIndex = rowIndex * columns + colIndex;
        return (
          <div key={itemIndex} className="flex-1">
            {renderItem(item, itemIndex)}
          </div>
        );
      })}
      {/* Fill empty columns */}
      {row.length < columns && (
        <>
          {Array.from({ length: columns - row.length }).map((_, i) => (
            <div key={`empty-${i}`} className="flex-1" />
          ))}
        </>
      )}
    </div>
  );

  return (
    <VirtualizedList
      {...props}
      items={rows}
      renderItem={renderRow}
    />
  );
};

// Infinite scroll wrapper
interface InfiniteVirtualizedListProps<T = any> extends VirtualizedListProps<T> {
  hasNextPage: boolean;
  isLoadingNext: boolean;
  onLoadNext: () => void;
  loadThreshold?: number;
}

export const InfiniteVirtualizedList = <T,>({
  hasNextPage,
  isLoadingNext,
  onLoadNext,
  loadThreshold = 5,
  ...props
}: InfiniteVirtualizedListProps<T>) => {
  const [lastScrollTop, setLastScrollTop] = React.useState(0);

  const handleScroll = React.useCallback((scrollTop: number) => {
    setLastScrollTop(scrollTop);
    props.onScroll?.(scrollTop);

    // Check if we need to load more
    if (hasNextPage && !isLoadingNext) {
      const parentElement = document.querySelector('[data-testid="virtualized-list"]');
      if (parentElement) {
        const { scrollHeight, clientHeight } = parentElement;
        const scrollThreshold = scrollHeight - clientHeight - (loadThreshold * (props.itemHeight as number || 50));
        
        if (scrollTop >= scrollThreshold) {
          onLoadNext();
        }
      }
    }
  }, [hasNextPage, isLoadingNext, onLoadNext, loadThreshold, props.itemHeight, props.onScroll]);

  const footer = useMemo(() => {
    if (!hasNextPage) return props.footer;
    
    return (
      <>
        {props.footer}
        {isLoadingNext && (
          <div className="flex items-center justify-center p-4">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary" />
            <span className="ml-2 text-sm text-muted-foreground">Loading more...</span>
          </div>
        )}
      </>
    );
  }, [hasNextPage, isLoadingNext, props.footer]);

  return (
    <VirtualizedList
      {...props}
      onScroll={handleScroll}
      footer={footer}
    />
  );
};

// Memory optimization wrapper
export const MemoryOptimizedVirtualizedList = <T,>(props: VirtualizedListProps<T>) => {
  // Use React.memo to prevent unnecessary re-renders
  const MemoizedList = React.memo(VirtualizedList) as typeof VirtualizedList;
  
  return <MemoizedList {...props} />;
};

export default VirtualizedList;