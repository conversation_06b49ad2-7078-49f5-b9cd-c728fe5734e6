import * as React from "react";
import { cn } from "@/lib/utils";

/**
 * Label component for form fields
 * 
 * @example
 * <Label htmlFor="input-id">Field Label</Label>
 */
function Label({
  className,
  ...props
}: React.ComponentProps<"label">) {
  return (
    <label
      data-slot="label"
      className={cn(
        "text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",
        className
      )}
      {...props}
    />
  );
}

export { Label };