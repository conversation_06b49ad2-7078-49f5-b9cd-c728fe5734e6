"use client";

import { cn } from "@/lib/utils";
import React, { useRef } from "react";

export interface AnimatedBeamProps {
  className?: string;
  containerRef: React.RefObject<HTMLElement>; // Container ref
  fromRef: React.RefObject<HTMLElement>;
  toRef: React.RefObject<HTMLElement>;
  curvature?: number;
  reverse?: boolean;
  pathColor?: string;
  pathWidth?: number;
  pathOpacity?: number;
  gradientStartColor?: string;
  gradientStopColor?: string;
  delay?: number;
  duration?: number;
  startXOffset?: number;
  startYOffset?: number;
  endXOffset?: number;
  endYOffset?: number;
}

export const AnimatedBeam: React.FC<AnimatedBeamProps> = ({
  className,
  containerRef,
  fromRef,
  toRef,
  curvature = 0,
  reverse = false, // Include the reverse prop
  duration = Math.random() * 3 + 4,
  delay = 0,
  pathColor = "gray",
  pathWidth = 2,
  pathOpacity = 0.2,
  gradientStartColor = "#ffaa40",
  gradientStopColor = "#9c40ff",
  startXOffset = 0,
  startYOffset = 0,
  endXOffset = 0,
  endYOffset = 0,
}) => {
  const id = React.useId();
  const svgRef = useRef<SVGSVGElement>(null);
  const [pathD, setPathD] = React.useState("");
  const [svgDimensions, setSvgDimensions] = React.useState({ width: 0, height: 0 });

  const gradientId = `gradient-${id}`;
  const maskId = `mask-${id}`;

  const updatePath = React.useCallback(() => {
    if (containerRef.current && fromRef.current && toRef.current) {
      const containerRect = containerRef.current.getBoundingClientRect();
      const rectA = fromRef.current.getBoundingClientRect();
      const rectB = toRef.current.getBoundingClientRect();

      const svgWidth = containerRect.width;
      const svgHeight = containerRect.height;
      setSvgDimensions({
        width: svgWidth,
        height: svgHeight,
      });

      const startX =
        rectA.left - containerRect.left + rectA.width / 2 + startXOffset;
      const startY =
        rectA.top - containerRect.top + rectA.height / 2 + startYOffset;
      const endX =
        rectB.left - containerRect.left + rectB.width / 2 + endXOffset;
      const endY = rectB.top - containerRect.top + rectB.height / 2 + endYOffset;

      const controlPointX = startX + (endX - startX) / 2;
      const controlPointY = startY - curvature;

      const d = `M ${startX},${startY} Q ${controlPointX},${controlPointY} ${endX},${endY}`;
      setPathD(d);
    }
  }, [containerRef, fromRef, toRef, curvature, startXOffset, startYOffset, endXOffset, endYOffset]);

  React.useEffect(() => {
    // Initialize the path
    updatePath();

    // Set up a ResizeObserver to update the path when the container resizes
    const resizeObserver = new ResizeObserver(() => {
      // For all entries, recalculate the path
      updatePath();
    });

    // Observe the container for resize changes
    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
    }

    // Clean up the observer on component unmount
    return () => {
      resizeObserver.disconnect();
    };
  }, [updatePath, containerRef]);

  return (
    <svg
      ref={svgRef}
      fill="none"
      width={svgDimensions.width}
      height={svgDimensions.height}
      xmlns="http://www.w3.org/2000/svg"
      className={cn(
        "pointer-events-none absolute left-0 top-0 transform-gpu stroke-2",
        className,
      )}
      viewBox={`0 0 ${svgDimensions.width} ${svgDimensions.height}`}
    >
      <defs>
        <linearGradient
          className={cn("transform-gpu")}
          id={gradientId}
          gradientUnits="userSpaceOnUse"
          x1="0%"
          x2="100%"
          y1="0%"
          y2="0%"
        >
          {reverse ? (
            <>
              <stop offset="0%" stopColor={gradientStopColor} stopOpacity="0" />
              <stop offset="100%" stopColor={gradientStartColor} stopOpacity="1" />
            </>
          ) : (
            <>
              <stop offset="0%" stopColor={gradientStartColor} stopOpacity="1" />
              <stop offset="100%" stopColor={gradientStopColor} stopOpacity="0" />
            </>
          )}
          <animateTransform
            attributeName="gradientTransform"
            type="translate"
            values={reverse ? "0 0; -200 0; 0 0" : "0 0; 200 0; 0 0"}
            dur={`${duration}s`}
            begin={`${delay}s`}
            repeatCount="indefinite"
          />
        </linearGradient>

        <mask id={maskId}>
          <path
            d={pathD}
            stroke="white"
            strokeWidth={pathWidth}
            strokeOpacity={pathOpacity}
            fill="none"
          />
        </mask>
      </defs>
      <path
        d={pathD}
        stroke={pathColor}
        strokeWidth={pathWidth}
        strokeOpacity={pathOpacity}
        fill="none"
      />
      <path
        d={pathD}
        stroke={`url(#${gradientId})`}
        strokeWidth={pathWidth}
        fill="none"
        mask={`url(#${maskId})`}
      />
    </svg>
  );
};

export default AnimatedBeam;