import React, { useState, useEffect, useRef } from "react";
import { motion } from "framer-motion";
import {
  <PERSON>,
  <PERSON><PERSON>,
  Plus,
  Settings,
  Brain,
  Target,
  GitBranch,
  Sparkles,
  Activity,
  MessageSquare,
  BarChart3,
  Wand2,
  Save,
  FolderOpen,
  Users2,
  Workflow,
  X
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Progress } from "@/components/ui/progress";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { cn } from "@/lib/utils";
import { api, type Agent } from "@/lib/api";
import { AgentSelector } from "./orchestra/AgentSelector";
import { TaskDistributor } from "./orchestra/TaskDistributor";
import { MCPAutoInitializer } from "./orchestra/MCPAutoInitializer";
import WorkflowDesigner from "./orchestra/WorkflowDesigner";
import { AgentMemoryViewer } from "./orchestra/AgentMemoryViewer";
import { CollaborationIndicator } from "./orchestra/CollaborationIndicator";
import { IntelligentTaskRouter } from "./orchestra/IntelligentTaskRouter";
import { OrchestratorChat } from "./orchestra/OrchestratorChat";

// Types for orchestration
export interface OrchestratedAgent {
  agent: Agent;
  status: 'idle' | 'initializing' | 'working' | 'paused' | 'completed' | 'error';
  task?: string;
  progress: number;
  requiredMCPServers: string[];
  mcpStatus: Record<string, 'pending' | 'running' | 'error'>;
  memory: AgentMemory;
  performance: AgentPerformance;
  sessionId?: string;
  lastActivity?: string;
  output?: string[];
  collaboratingWith?: string[];
}

export interface AgentMemory {
  totalTasks: number;
  successfulTasks: number;
  failedTasks: number;
  learnings: string[];
  preferences: Record<string, any>;
  knownPatterns: string[];
  lastUpdated: string;
}

export interface AgentPerformance {
  averageTaskTime: number;
  successRate: number;
  tokenEfficiency: number;
  collaborationScore: number;
  specialties: string[];
}

export interface AgentTask {
  id: string;
  description: string;
  assignedTo: string[];
  dependencies: string[];
  priority: 'low' | 'medium' | 'high' | 'critical';
  status: 'pending' | 'in-progress' | 'completed' | 'failed';
  progress: number;
  startedAt?: string;
  completedAt?: string;
  output?: any;
  requiredCapabilities: string[];
}

export interface AgentWorkflow {
  id: string;
  name: string;
  description: string;
  nodes: WorkflowNode[];
  edges: WorkflowEdge[];
  variables: Record<string, any>;
  triggers: WorkflowTrigger[];
  status: 'draft' | 'active' | 'paused' | 'completed';
  createdAt: Date;
  updatedAt: Date;
}

export interface WorkflowNode {
  id: string;
  type: 'agent' | 'condition' | 'parallel' | 'sequential' | 'loop' | 'delay' | 'webhook';
  name: string;
  agentId?: string;
  config: Record<string, any>;
  position: { x: number; y: number };
}

export interface WorkflowEdge {
  id: string;
  source: string;
  target: string;
  condition?: string;
}

export interface WorkflowTrigger {
  id: string;
  type: 'manual' | 'schedule' | 'event' | 'condition';
  config: Record<string, any>;
}

interface AgentOrchestraPanelProps {
  projectPath: string;
  onClose: () => void;
  className?: string;
  sessionId?: string;
}

export const AgentOrchestraPanel: React.FC<AgentOrchestraPanelProps> = ({
  projectPath,
  onClose,
  className
}) => {
  // Core state
  const [orchestratedAgents, setOrchestratedAgents] = useState<OrchestratedAgent[]>([]);
  const [tasks, setTasks] = useState<AgentTask[]>([]);
  const [, setWorkflows] = useState<AgentWorkflow[]>([]);
  const [activeTab, setActiveTab] = useState<'agents' | 'tasks' | 'workflow' | 'memory' | 'analytics' | 'chat'>('agents');
  const [isRunning] = useState(false);
  
  // UI state
  const [showWorkflowDesigner, setShowWorkflowDesigner] = useState(false);
  const [showIntelligentRouter, setShowIntelligentRouter] = useState(false);
  const [collaborationMode] = useState<'isolated' | 'paired' | 'ensemble'>('paired');
  const [autoInitMCP, setAutoInitMCP] = useState(true);
  const [showAgentDetails, setShowAgentDetails] = useState(true);
  
  // Performance tracking
  const [orchestrationMetrics] = useState({
    totalTasks: 0,
    completedTasks: 0,
    averageCompletionTime: 0,
    tokenUsage: 0,
    mcpServerUtilization: {} as Record<string, number>
  });

  // Natural language orchestration
  const [orchestratorCommand, setOrchestratorCommand] = useState("");
  const [commandHistory, setCommandHistory] = useState<string[]>([]);
  
  // Agent communication channel
  const agentCommChannel = useRef<BroadcastChannel>(new BroadcastChannel('agent-orchestra'));
  
  useEffect(() => {
    // Set up agent communication listener
    agentCommChannel.current.onmessage = (event) => {
      handleAgentMessage(event.data);
    };
    
    return () => {
      agentCommChannel.current.close();
    };
  }, []);

  const handleAgentMessage = (message: any) => {
    const { agentId, type, data } = message;
    
    switch (type) {
      case 'progress':
        updateAgentProgress(agentId, data.progress);
        break;
      case 'collaboration-request':
        handleCollaborationRequest(agentId, data.targetAgentId);
        break;
      case 'discovery':
        shareDiscovery(agentId, data.discovery);
        break;
      case 'task-complete':
        handleTaskCompletion(agentId, data.taskId, data.output);
        break;
    }
  };

  const updateAgentProgress = (agentId: string, progress: number) => {
    setOrchestratedAgents(prev => prev.map(oa => 
      oa.agent.id !== undefined && String(oa.agent.id) === agentId ? { ...oa, progress } : oa
    ));
  };

  const handleCollaborationRequest = (requesterId: string, targetId: string) => {
    // Intelligent collaboration matching
    const requester = orchestratedAgents.find(oa => oa.agent.id !== undefined && String(oa.agent.id) === requesterId);
    const target = orchestratedAgents.find(oa => oa.agent.id !== undefined && String(oa.agent.id) === targetId);
    
    if (requester && target && target.status === 'working') {
      // Check if collaboration makes sense based on current tasks
      const shouldCollaborate = analyzeCollaborationSynergy(requester, target);
      
      if (shouldCollaborate) {
        setOrchestratedAgents(prev => prev.map(oa => {
          if (oa.agent.id !== undefined && String(oa.agent.id) === requesterId) {
            return { ...oa, collaboratingWith: [...(oa.collaboratingWith || []), targetId] };
          }
          if (oa.agent.id !== undefined && String(oa.agent.id) === targetId) {
            return { ...oa, collaboratingWith: [...(oa.collaboratingWith || []), requesterId] };
          }
          return oa;
        }));
      }
    }
  };

  const analyzeCollaborationSynergy = (agent1: OrchestratedAgent, agent2: OrchestratedAgent): boolean => {
    // Check if agents have complementary skills
    const agent1Skills = agent1.performance.specialties;
    const agent2Skills = agent2.performance.specialties;
    
    // Check for skill overlap and complementarity
    const overlap = agent1Skills.filter(s => agent2Skills.includes(s)).length;
    const complementary = agent1Skills.length + agent2Skills.length - overlap;
    
    return complementary > overlap * 2; // Favor complementary skills
  };

  const shareDiscovery = (agentId: string, discovery: any) => {
    // Broadcast discovery to relevant agents
    const relevantAgents = orchestratedAgents.filter(oa =>
      oa.status === 'working' &&
      oa.agent.id !== undefined && String(oa.agent.id) !== agentId &&
      isDiscoveryRelevant(discovery, oa.task)
    );
    
    relevantAgents.forEach(oa => {
      agentCommChannel.current.postMessage({
        type: 'shared-discovery',
        agentId: oa.agent.id,
        discovery,
        sharedBy: agentId
      });
    });
  };

  const isDiscoveryRelevant = (discovery: any, task?: string): boolean => {
    if (!task) return false;
    // Simple relevance check - in production, use NLP
    return discovery.keywords?.some((kw: string) => task.toLowerCase().includes(kw.toLowerCase()));
  };

  const handleTaskCompletion = (agentId: string, taskId: string, output: any) => {
    setTasks(prev => prev.map(task => 
      task.id === taskId ? { ...task, status: 'completed', output, completedAt: new Date().toISOString() } : task
    ));
    
    // Update agent memory with success
    setOrchestratedAgents(prev => prev.map(oa => {
      if (oa.agent.id !== undefined && String(oa.agent.id) === agentId) {
        const memory = {
          ...oa.memory,
          totalTasks: oa.memory.totalTasks + 1,
          successfulTasks: oa.memory.successfulTasks + 1,
          lastUpdated: new Date().toISOString()
        };
        
        const performance = {
          ...oa.performance,
          successRate: memory.successfulTasks / memory.totalTasks
        };
        
        return { ...oa, memory, performance, status: 'idle' };
      }
      return oa;
    }));
    
    // Check for dependent tasks
    checkAndStartDependentTasks(taskId);
  };

  const checkAndStartDependentTasks = (completedTaskId: string) => {
    const dependentTasks = tasks.filter(task => 
      task.dependencies.includes(completedTaskId) &&
      task.status === 'pending'
    );
    
    dependentTasks.forEach(task => {
      const allDependenciesComplete = task.dependencies.every(depId => 
        tasks.find(t => t.id === depId)?.status === 'completed'
      );
      
      if (allDependenciesComplete) {
        assignTaskToOptimalAgent(task);
      }
    });
  };

  const assignTaskToOptimalAgent = (task: AgentTask) => {
    // Find the best agent for the task using AI-powered matching
    const availableAgents = orchestratedAgents.filter(oa => oa.status === 'idle');
    
    if (availableAgents.length === 0) {
      // Queue the task
      return;
    }
    
    // Score each agent for the task
    const agentScores = availableAgents.map(oa => ({
      agent: oa,
      score: calculateAgentTaskScore(oa, task)
    }));
    
    // Select the best agent
    const bestMatch = agentScores.reduce((best, current) => 
      current.score > best.score ? current : best
    );
    
    if (bestMatch.score > 0.5) {
      if (bestMatch.agent.agent.id !== undefined) {
        startAgentTask(String(bestMatch.agent.agent.id), task);
      }
    }
  };

  const calculateAgentTaskScore = (agent: OrchestratedAgent, task: AgentTask): number => {
    let score = 0;
    
    // Check capability match
    const capabilityMatch = task.requiredCapabilities.filter(cap => 
      agent.performance.specialties.includes(cap)
    ).length / task.requiredCapabilities.length;
    score += capabilityMatch * 0.4;
    
    // Consider past performance
    score += agent.performance.successRate * 0.3;
    
    // Consider current workload (favor less loaded agents)
    score += (1 - agent.progress / 100) * 0.2;
    
    // Consider collaboration potential
    if (collaborationMode !== 'isolated') {
      const collaborationPotential = orchestratedAgents
        .filter(oa => oa.status === 'working' && agent.agent.id !== undefined && oa.collaboratingWith?.includes(String(agent.agent.id)))
        .length / orchestratedAgents.length;
      score += collaborationPotential * 0.1;
    }
    
    return score;
  };

  const startAgentTask = async (agentId: string, task: AgentTask) => {
    // Update task and agent status
    setTasks(prev => prev.map(t => 
      t.id === task.id ? { ...t, status: 'in-progress', startedAt: new Date().toISOString() } : t
    ));
    
    setOrchestratedAgents(prev => prev.map(oa => 
      oa.agent.id !== undefined && String(oa.agent.id) === agentId ? { ...oa, status: 'working', task: task.description, progress: 0 } : oa
    ));
    
    // Start the agent with the task
    try {
      const agentIdNum = parseInt(agentId);
      if (isNaN(agentIdNum)) {
        throw new Error('Invalid agent ID');
      }
      const runId = await api.executeAgent(agentIdNum, projectPath, task.description, 'sonnet');
      
      setOrchestratedAgents(prev => prev.map(oa => 
        oa.agent.id !== undefined && String(oa.agent.id) === agentId ? { ...oa, sessionId: String(runId) } : oa
      ));
    } catch (error) {
      console.error('Failed to start agent task:', error);
      handleTaskFailure(agentId, task.id);
    }
  };

  const handleTaskFailure = (agentId: string, taskId: string) => {
    setTasks(prev => prev.map(task => 
      task.id === taskId ? { ...task, status: 'failed' } : task
    ));
    
    setOrchestratedAgents(prev => prev.map(oa => {
      if (oa.agent.id !== undefined && String(oa.agent.id) === agentId) {
        const memory = {
          ...oa.memory,
          totalTasks: oa.memory.totalTasks + 1,
          failedTasks: oa.memory.failedTasks + 1,
          lastUpdated: new Date().toISOString()
        };
        
        const performance = {
          ...oa.performance,
          successRate: memory.successfulTasks / memory.totalTasks
        };
        
        return { ...oa, memory, performance, status: 'error' };
      }
      return oa;
    }));
  };

  const processOrchestratorCommand = async (command: string) => {
    setCommandHistory(prev => [...prev, command]);
    
    // Parse natural language commands
    if (command.startsWith('/')) {
      // Handle slash commands
      const [cmd, ...args] = command.slice(1).split(' ');
      
      switch (cmd) {
        case 'add':
          // /add agent1 agent2 agent3
          // Add agents logic
          break;
          
        case 'task':
          // /task agent1 "description"
          // Create task logic
          break;
          
        case 'collaborate':
          // /collaborate agent1 agent2
          // Set up collaboration
          break;
          
        case 'workflow':
          // /workflow save "name"
          if (args[0] === 'save') {
            const workflowName = args.slice(1).join(' ').replace(/"/g, '');
            saveCurrentWorkflow(workflowName);
          }
          break;
      }
    } else {
      // Natural language processing
      interpretNaturalCommand(command);
    }
  };

  const interpretNaturalCommand = async (command: string) => {
    // This would use NLP in production
    const lowerCommand = command.toLowerCase();
    
    if (lowerCommand.includes('add') && lowerCommand.includes('agent')) {
      // "Add the code reviewer and test writer agents"
      setShowIntelligentRouter(true);
    } else if (lowerCommand.includes('status')) {
      // "Show me the status of all agents"
      // Show status overview
    } else if (lowerCommand.includes('help')) {
      // "Help agent1 with agent2"
      // Set up collaboration
    }
  };

  const saveCurrentWorkflow = (name: string) => {
    const workflow: AgentWorkflow = {
      id: `workflow_${Date.now()}`,
      name,
      description: `Saved orchestration workflow with ${orchestratedAgents.length} agents`,
      nodes: orchestratedAgents.map((oa, index) => ({
        id: oa.agent.id !== undefined ? String(oa.agent.id) : `agent_${index}`,
        type: 'agent' as const,
        name: oa.agent.name || `Agent ${index + 1}`,
        agentId: oa.agent.id !== undefined ? String(oa.agent.id) : undefined,
        config: { task: oa.task },
        position: { x: index * 200, y: 100 }
      })),
      edges: [], // Would be populated based on dependencies
      variables: {},
      triggers: [{ id: `trigger_${Date.now()}`, type: 'manual', config: {} }],
      status: 'draft',
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    setWorkflows((prev: AgentWorkflow[]) => [...prev, workflow]);
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.95 }}
      className={cn(
        "fixed inset-x-4 top-16 bottom-4 bg-background rounded-lg shadow-2xl border border-border overflow-hidden z-40",
        "flex flex-col",
        className
      )}
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-border bg-card">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-primary/10 rounded-lg">
            <Users className="h-5 w-5 text-primary" />
          </div>
          <div>
            <h2 className="text-lg font-semibold flex items-center gap-2">
              Agent Orchestra
              {isRunning && (
                <Badge variant="secondary" className="animate-pulse">
                  <Activity className="h-3 w-3 mr-1" />
                  Active
                </Badge>
              )}
            </h2>
            <p className="text-sm text-muted-foreground">
              {orchestratedAgents.length} agents • {tasks.filter(t => t.status === 'in-progress').length} active tasks
            </p>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          {/* Quick Actions */}
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => setShowWorkflowDesigner(true)}
                >
                  <Workflow className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Design Workflow</TooltipContent>
            </Tooltip>
          </TooltipProvider>
          
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => setShowIntelligentRouter(true)}
                >
                  <Wand2 className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>AI Task Router</TooltipContent>
            </Tooltip>
          </TooltipProvider>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="icon">
                <Settings className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <div className="p-2 space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="auto-mcp">Auto-init MCP</Label>
                  <Switch
                    id="auto-mcp"
                    checked={autoInitMCP}
                    onCheckedChange={setAutoInitMCP}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="show-details">Show Details</Label>
                  <Switch
                    id="show-details"
                    checked={showAgentDetails}
                    onCheckedChange={setShowAgentDetails}
                  />
                </div>
              </div>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => saveCurrentWorkflow('Current Setup')}>
                <Save className="h-4 w-4 mr-2" />
                Save Workflow
              </DropdownMenuItem>
              <DropdownMenuItem>
                <FolderOpen className="h-4 w-4 mr-2" />
                Load Workflow
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          
          <Button variant="ghost" size="icon" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Orchestrator Command Bar */}
      <div className="p-4 border-b border-border bg-muted/20">
        <div className="flex gap-2">
          <div className="flex-1 relative">
            <Sparkles className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              value={orchestratorCommand}
              onChange={(e) => setOrchestratorCommand(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter' && orchestratorCommand.trim()) {
                  processOrchestratorCommand(orchestratorCommand);
                  setOrchestratorCommand('');
                }
              }}
              placeholder="Natural language orchestration... (e.g., 'Add code reviewer and test writer to review the API changes')"
              className="pl-10"
            />
          </div>
          <Button
            onClick={() => {
              if (orchestratorCommand.trim()) {
                processOrchestratorCommand(orchestratorCommand);
                setOrchestratorCommand('');
              }
            }}
          >
            Execute
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex overflow-hidden">
        {/* Left Panel - Agent Orchestra */}
        <div className="flex-1 flex flex-col">
          <Tabs value={activeTab} onValueChange={(v) => setActiveTab(v as any)} className="flex-1 flex flex-col">
            <TabsList className="mx-4 mt-4">
              <TabsTrigger value="agents" className="flex items-center gap-2">
                <Bot className="h-4 w-4" />
                Agents
              </TabsTrigger>
              <TabsTrigger value="tasks" className="flex items-center gap-2">
                <Target className="h-4 w-4" />
                Tasks
              </TabsTrigger>
              <TabsTrigger value="workflow" className="flex items-center gap-2">
                <GitBranch className="h-4 w-4" />
                Workflow
              </TabsTrigger>
              <TabsTrigger value="memory" className="flex items-center gap-2">
                <Brain className="h-4 w-4" />
                Memory
              </TabsTrigger>
              <TabsTrigger value="analytics" className="flex items-center gap-2">
                <BarChart3 className="h-4 w-4" />
                Analytics
              </TabsTrigger>
              <TabsTrigger value="chat" className="flex items-center gap-2">
                <MessageSquare className="h-4 w-4" />
                Chat
              </TabsTrigger>
            </TabsList>

            <TabsContent value="agents" className="flex-1 p-4 overflow-hidden">
              <AgentSelector
                orchestratedAgents={orchestratedAgents}
                onAgentSelect={(agent) => {
                  // Add agent to orchestra
                  const newOrchestratedAgent: OrchestratedAgent = {
                    agent,
                    status: 'idle',
                    progress: 0,
                    requiredMCPServers: [], // Would be determined by agent config
                    mcpStatus: {},
                    memory: {
                      totalTasks: 0,
                      successfulTasks: 0,
                      failedTasks: 0,
                      learnings: [],
                      preferences: {},
                      knownPatterns: [],
                      lastUpdated: new Date().toISOString()
                    },
                    performance: {
                      averageTaskTime: 0,
                      successRate: 0,
                      tokenEfficiency: 0,
                      collaborationScore: 0,
                      specialties: []
                    }
                  };
                  setOrchestratedAgents(prev => [...prev, newOrchestratedAgent]);
                }}
                onAgentRemove={(agentId) => {
                  setOrchestratedAgents(prev => prev.filter(oa => oa.agent.id === undefined || String(oa.agent.id) !== agentId));
                }}
                showDetails={showAgentDetails}
                collaborationMode={collaborationMode}
              />
            </TabsContent>

            <TabsContent value="tasks" className="flex-1 p-4 overflow-hidden">
              <TaskDistributor
                tasks={tasks}
                orchestratedAgents={orchestratedAgents}
                onTaskCreate={(task) => setTasks(prev => [...prev, task])}
                onTaskAssign={assignTaskToOptimalAgent}
                onTaskUpdate={(taskId, updates) => {
                  setTasks(prev => prev.map(t => 
                    t.id === taskId ? { ...t, ...updates } : t
                  ));
                }}
              />
            </TabsContent>

            <TabsContent value="workflow" className="flex-1 p-4 overflow-hidden">
              <div className="h-full flex flex-col gap-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold">Workflow View</h3>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowWorkflowDesigner(true)}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Design Workflow
                  </Button>
                </div>
                
                {/* Visual workflow representation */}
                <div className="flex-1 border border-border rounded-lg bg-muted/10 p-4">
                  <WorkflowVisualizer
                    agents={orchestratedAgents}
                    tasks={tasks}
                    collaborationMode={collaborationMode}
                  />
                </div>
              </div>
            </TabsContent>

            <TabsContent value="memory" className="flex-1 p-4 overflow-hidden">
              <AgentMemoryViewer
                orchestratedAgents={orchestratedAgents}
                onMemoryUpdate={(agentId, memory) => {
                  setOrchestratedAgents(prev => prev.map(oa => 
                    oa.agent.id !== undefined && String(oa.agent.id) === agentId ? { ...oa, memory } : oa
                  ));
                }}
              />
            </TabsContent>

            <TabsContent value="analytics" className="flex-1 p-4 overflow-hidden">
              <OrchestraAnalytics
                orchestratedAgents={orchestratedAgents}
                tasks={tasks}
                metrics={orchestrationMetrics}
              />
            </TabsContent>
            <TabsContent value="chat" className="flex-1 overflow-hidden">
              <OrchestratorChat
                onCommand={(command) => {
                  setCommandHistory(prev => [...prev, command]);
                  // Process the command here - would implement command parsing
                  console.log('Orchestra command:', command);
                }}
                commandHistory={commandHistory}
                className="h-full"
              />
            </TabsContent>
          </Tabs>
        </div>

        {/* Right Panel - Live Status & Collaboration */}
        <div className="w-96 border-l border-border flex flex-col bg-muted/5">
          <div className="p-4 border-b border-border">
            <h3 className="font-semibold flex items-center gap-2">
              <Activity className="h-4 w-4" />
              Live Activity
            </h3>
          </div>
          
          <ScrollArea className="flex-1">
            <div className="p-4 space-y-4">
              {/* Collaboration Indicators */}
              <CollaborationIndicator
                orchestratedAgents={orchestratedAgents}
                onCollaborationClick={() => {
                  // Show collaboration details
                }}
              />
              
              {/* Real-time Agent Status */}
              {orchestratedAgents.map(oa => (
                <Card key={oa.agent.id} className={cn(
                  "transition-all",
                  oa.status === 'working' && "border-primary/50 shadow-sm"
                )}>
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Bot className="h-4 w-4" />
                        <span className="font-medium">{oa.agent.name}</span>
                      </div>
                      <Badge variant={
                        oa.status === 'working' ? 'default' :
                        oa.status === 'completed' ? 'secondary' :
                        oa.status === 'error' ? 'destructive' :
                        'outline'
                      }>
                        {oa.status}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    {oa.task && (
                      <p className="text-sm text-muted-foreground mb-2">{oa.task}</p>
                    )}
                    {oa.status === 'working' && (
                      <Progress value={oa.progress} className="h-2" />
                    )}
                    {oa.collaboratingWith && oa.collaboratingWith.length > 0 && (
                      <div className="mt-2 flex items-center gap-1 text-xs text-muted-foreground">
                        <Users2 className="h-3 w-3" />
                        Collaborating with {oa.collaboratingWith.length} agent(s)
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          </ScrollArea>
          
          {/* MCP Status */}
          <div className="p-4 border-t border-border">
            <MCPAutoInitializer
              orchestratedAgents={orchestratedAgents}
              autoInit={autoInitMCP}
              onMCPStatusUpdate={(agentId, mcpStatus) => {
                setOrchestratedAgents(prev => prev.map(oa => 
                  oa.agent.id !== undefined && String(oa.agent.id) === agentId ? { ...oa, mcpStatus } : oa
                ));
              }}
            />
          </div>
        </div>
      </div>

      {/* Modals */}
      {showWorkflowDesigner && (
        <WorkflowDesigner
          onClose={() => setShowWorkflowDesigner(false)}
          onSave={() => {
            setShowWorkflowDesigner(false);
          }}
          existingAgents={orchestratedAgents.map(oa => oa.agent)}
        />
      )}
      
      {showIntelligentRouter && (
        <IntelligentTaskRouter
          projectPath={projectPath}
          onClose={() => setShowIntelligentRouter(false)}
          onRouteTask={(task, agentIds) => {
            // Create task and assign to agents
            const newTask: AgentTask = {
              ...task,
              id: `task_${Date.now()}`,
              assignedTo: agentIds,
              status: 'pending',
              progress: 0
            };
            setTasks(prev => [...prev, newTask]);
            assignTaskToOptimalAgent(newTask);
            setShowIntelligentRouter(false);
          }}
          availableAgents={orchestratedAgents.filter(oa => oa.status === 'idle')}
        />
      )}
    </motion.div>
  );
};

// Helper Components
const WorkflowVisualizer: React.FC<{
  agents: OrchestratedAgent[];
  tasks: AgentTask[];
  collaborationMode: string;
}> = ({ agents, tasks }) => {
  return (
    <div className="relative h-full">
      {/* Simple visualization - in production, use a proper graph library */}
      <div className="flex items-center justify-center h-full">
        <div className="text-center text-muted-foreground">
          <Workflow className="h-12 w-12 mx-auto mb-4 opacity-50" />
          <p>Workflow visualization</p>
          <p className="text-sm mt-2">{agents.length} agents • {tasks.length} tasks</p>
        </div>
      </div>
    </div>
  );
};

const OrchestraAnalytics: React.FC<{
  orchestratedAgents: OrchestratedAgent[];
  tasks: AgentTask[];
  metrics: any;
}> = ({ orchestratedAgents, tasks }) => {
  const completionRate = tasks.length > 0 
    ? (tasks.filter(t => t.status === 'completed').length / tasks.length) * 100 
    : 0;
    
  const activeAgents = orchestratedAgents.filter(oa => oa.status === 'working').length;
  
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-2 gap-4">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Completion Rate</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{completionRate.toFixed(1)}%</div>
            <Progress value={completionRate} className="mt-2" />
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium">Active Agents</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{activeAgents}</div>
            <p className="text-sm text-muted-foreground mt-1">
              of {orchestratedAgents.length} total
            </p>
          </CardContent>
        </Card>
      </div>
      
      {/* Agent Performance Chart */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm font-medium">Agent Performance</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {orchestratedAgents.map(oa => (
              <div key={oa.agent.id} className="flex items-center gap-2">
                <span className="text-sm w-32 truncate">{oa.agent.name}</span>
                <Progress 
                  value={oa.performance.successRate * 100} 
                  className="flex-1 h-2"
                />
                <span className="text-sm text-muted-foreground w-12 text-right">
                  {(oa.performance.successRate * 100).toFixed(0)}%
                </span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AgentOrchestraPanel;