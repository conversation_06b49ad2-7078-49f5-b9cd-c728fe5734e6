import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Bug,
  PlusCircle,
  Eye,
  Zap,
  BookOpen,
  GitBranch,
  Search,
  Sparkles,
  Clock,
  Star,
  Tag,
  CheckCircle
} from 'lucide-react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
// import { Textarea } from '@/components/ui/textarea'; // Not used in this component
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';
import { useTabState } from '@/hooks/useTabState';

interface SessionTemplate {
  id: string;
  name: string;
  description: string;
  icon: React.ElementType;
  category: 'development' | 'debugging' | 'review' | 'documentation' | 'maintenance';
  tags: string[];
  systemPrompt: string;
  initialPrompt?: string;
  contextSetup: string[];
  estimatedTime: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  popular?: boolean;
  trending?: boolean;
}

interface SessionTemplatesProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onTemplateSelect?: (template: SessionTemplate) => void;
}

export function SessionTemplates({ open, onOpenChange, onTemplateSelect }: SessionTemplatesProps) {
  const [search, setSearch] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedTemplate, setSelectedTemplate] = useState<SessionTemplate | null>(null);
  const { createChatTab } = useTabState();

  // Built-in templates
  const templates: SessionTemplate[] = [
    {
      id: 'bug-fix',
      name: 'Bug Fixing',
      description: 'Systematic approach to identifying and fixing bugs',
      icon: Bug,
      category: 'debugging',
      tags: ['debugging', 'troubleshooting', 'problem-solving'],
      systemPrompt: `You are a debugging expert. Help the user systematically identify, analyze, and fix bugs in their code. Follow these steps:

1. **Understand the Problem**: Ask clarifying questions about the bug
2. **Reproduce the Issue**: Help set up conditions to reproduce the bug
3. **Analyze the Code**: Examine the relevant code sections
4. **Identify Root Cause**: Use debugging techniques to find the actual issue
5. **Implement Fix**: Provide a clean, tested solution
6. **Verify**: Ensure the fix works and doesn't introduce new issues

Always prioritize code quality, testing, and documentation of the fix.`,
      initialPrompt: 'Describe the bug you\'re experiencing. Include error messages, expected behavior, and steps to reproduce.',
      contextSetup: [
        'Error logs and stack traces',
        'Relevant code files',
        'Test cases (if available)',
        'Environment details'
      ],
      estimatedTime: '30-60 minutes',
      difficulty: 'intermediate',
      popular: true
    },
    {
      id: 'feature-dev',
      name: 'Feature Development',
      description: 'End-to-end feature implementation with best practices',
      icon: PlusCircle,
      category: 'development',
      tags: ['feature', 'development', 'implementation', 'planning'],
      systemPrompt: `You are a feature development expert. Guide the user through implementing new features with engineering best practices:

1. **Requirements Analysis**: Clarify feature requirements and acceptance criteria
2. **Design Planning**: Architecture and design decisions
3. **Implementation Strategy**: Break down into manageable tasks
4. **Code Implementation**: Write clean, maintainable code
5. **Testing**: Unit tests, integration tests, edge cases
6. **Documentation**: Code comments and user documentation
7. **Review & Refinement**: Code review suggestions and improvements

Focus on scalability, maintainability, and user experience.`,
      initialPrompt: 'What feature would you like to implement? Describe the requirements and expected behavior.',
      contextSetup: [
        'Existing codebase context',
        'API documentation',
        'Design specifications',
        'Testing framework setup'
      ],
      estimatedTime: '1-3 hours',
      difficulty: 'intermediate',
      trending: true
    },
    {
      id: 'code-review',
      name: 'Code Review',
      description: 'Comprehensive code analysis and improvement suggestions',
      icon: Eye,
      category: 'review',
      tags: ['review', 'quality', 'best-practices', 'optimization'],
      systemPrompt: `You are a senior code reviewer. Provide thorough, constructive code reviews focusing on:

1. **Code Quality**: Readability, maintainability, and organization
2. **Performance**: Efficiency and optimization opportunities
3. **Security**: Potential vulnerabilities and security best practices
4. **Best Practices**: Language-specific conventions and patterns
5. **Testing**: Test coverage and quality
6. **Documentation**: Code comments and documentation quality
7. **Architecture**: Design patterns and architectural decisions

Provide specific suggestions with examples and rationale.`,
      initialPrompt: 'Please share the code you\'d like me to review. I\'ll provide detailed feedback and suggestions.',
      contextSetup: [
        'Code files for review',
        'Project context and requirements',
        'Existing tests',
        'Style guide (if any)'
      ],
      estimatedTime: '20-45 minutes',
      difficulty: 'beginner',
      popular: true
    },
    {
      id: 'refactoring',
      name: 'Code Refactoring',
      description: 'Improve code structure and maintainability',
      icon: Zap,
      category: 'maintenance',
      tags: ['refactoring', 'cleanup', 'optimization', 'maintenance'],
      systemPrompt: `You are a refactoring specialist. Help improve code quality through systematic refactoring:

1. **Code Analysis**: Identify code smells and improvement opportunities
2. **Refactoring Strategy**: Plan safe, incremental changes
3. **Design Patterns**: Apply appropriate patterns to improve structure
4. **Performance**: Optimize algorithms and data structures
5. **Readability**: Improve naming, structure, and documentation
6. **Testing**: Ensure refactoring doesn't break functionality
7. **Migration Path**: Provide step-by-step refactoring plan

Always prioritize safety and maintainability over clever solutions.`,
      initialPrompt: 'Share the code you want to refactor. What specific issues or improvements are you targeting?',
      contextSetup: [
        'Current codebase',
        'Existing tests',
        'Performance requirements',
        'Team coding standards'
      ],
      estimatedTime: '45-90 minutes',
      difficulty: 'advanced'
    },
    {
      id: 'documentation',
      name: 'Documentation Writing',
      description: 'Create comprehensive documentation for your project',
      icon: BookOpen,
      category: 'documentation',
      tags: ['docs', 'readme', 'api', 'guides'],
      systemPrompt: `You are a technical writing expert. Help create clear, comprehensive documentation:

1. **Audience Analysis**: Understand the target audience and their needs
2. **Content Structure**: Organize information logically and hierarchically
3. **Clear Writing**: Use simple, precise language with examples
4. **Code Examples**: Provide practical, working code samples
5. **Visual Aids**: Suggest diagrams, screenshots, or flowcharts
6. **Accessibility**: Ensure documentation is accessible to all users
7. **Maintenance**: Create documentation that's easy to keep updated

Focus on user experience and practical value.`,
      initialPrompt: 'What type of documentation do you need? (API docs, README, user guide, etc.) Describe your project.',
      contextSetup: [
        'Project overview',
        'Code structure',
        'API specifications',
        'User workflows'
      ],
      estimatedTime: '30-60 minutes',
      difficulty: 'beginner'
    },
    {
      id: 'performance-optimization',
      name: 'Performance Optimization',
      description: 'Analyze and improve application performance',
      icon: Zap,
      category: 'maintenance',
      tags: ['performance', 'optimization', 'profiling', 'speed'],
      systemPrompt: `You are a performance optimization specialist. Help improve application performance:

1. **Performance Profiling**: Identify bottlenecks and performance issues
2. **Metrics Analysis**: Understand key performance indicators
3. **Optimization Strategy**: Prioritize improvements by impact
4. **Code Optimization**: Improve algorithms and data structures
5. **Resource Management**: Optimize memory and CPU usage
6. **Caching**: Implement effective caching strategies
7. **Monitoring**: Set up performance monitoring and alerts

Always measure before and after optimization to validate improvements.`,
      initialPrompt: 'Describe the performance issues you\'re experiencing. Include any metrics or benchmarks you have.',
      contextSetup: [
        'Performance metrics',
        'Profiling data',
        'Application code',
        'Infrastructure details'
      ],
      estimatedTime: '60-120 minutes',
      difficulty: 'advanced',
      trending: true
    },
    {
      id: 'api-design',
      name: 'API Design',
      description: 'Design clean, scalable APIs',
      icon: GitBranch,
      category: 'development',
      tags: ['api', 'design', 'architecture', 'rest', 'graphql'],
      systemPrompt: `You are an API design expert. Help create well-designed, scalable APIs:

1. **Requirements Analysis**: Understand API requirements and use cases
2. **Design Principles**: Apply REST, GraphQL, or other API patterns
3. **Resource Modeling**: Design clear resource hierarchies and relationships
4. **Error Handling**: Implement consistent error responses
5. **Security**: Authentication, authorization, and data protection
6. **Documentation**: Create comprehensive API documentation
7. **Versioning**: Plan for API evolution and backwards compatibility

Focus on developer experience and long-term maintainability.`,
      initialPrompt: 'What kind of API are you building? Describe the main resources and operations needed.',
      contextSetup: [
        'Business requirements',
        'Data models',
        'Client applications',
        'Security requirements'
      ],
      estimatedTime: '60-90 minutes',
      difficulty: 'advanced'
    }
  ];

  // Filter templates based on search and category
  const filteredTemplates = templates.filter(template => {
    const matchesSearch = template.name.toLowerCase().includes(search.toLowerCase()) ||
                         template.description.toLowerCase().includes(search.toLowerCase()) ||
                         template.tags.some(tag => tag.toLowerCase().includes(search.toLowerCase()));
    
    const matchesCategory = selectedCategory === 'all' || template.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  const categories = [
    { id: 'all', name: 'All Templates', count: templates.length },
    { id: 'development', name: 'Development', count: templates.filter(t => t.category === 'development').length },
    { id: 'debugging', name: 'Debugging', count: templates.filter(t => t.category === 'debugging').length },
    { id: 'review', name: 'Review', count: templates.filter(t => t.category === 'review').length },
    { id: 'documentation', name: 'Documentation', count: templates.filter(t => t.category === 'documentation').length },
    { id: 'maintenance', name: 'Maintenance', count: templates.filter(t => t.category === 'maintenance').length }
  ];

  const handleTemplateSelect = (template: SessionTemplate) => {
    // Create a new tab with the template
    createChatTab({
      title: `${template.name} Session`,
      systemPrompt: template.systemPrompt,
      initialPrompt: template.initialPrompt
    });
    
    if (onTemplateSelect) {
      onTemplateSelect(template);
    }
    
    onOpenChange(false);
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400';
      case 'intermediate': return 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'advanced': return 'bg-red-100 text-red-700 dark:bg-red-900/20 dark:text-red-400';
      default: return 'bg-gray-100 text-gray-700 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-5xl h-[80vh] p-0">
        <DialogHeader className="px-6 py-4 border-b">
          <DialogTitle className="flex items-center gap-2">
            <Sparkles className="h-5 w-5" />
            Session Templates
          </DialogTitle>
        </DialogHeader>

        <div className="flex h-full">
          {/* Sidebar */}
          <div className="w-64 border-r bg-muted/30 p-4">
            <div className="space-y-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                  placeholder="Search templates..."
                  className="pl-9"
                />
              </div>

              <div className="space-y-1">
                <Label className="text-xs font-semibold text-muted-foreground uppercase tracking-wide">
                  Categories
                </Label>
                {categories.map((category) => (
                  <Button
                    key={category.id}
                    variant={selectedCategory === category.id ? "secondary" : "ghost"}
                    size="sm"
                    className="w-full justify-between"
                    onClick={() => setSelectedCategory(category.id)}
                  >
                    <span>{category.name}</span>
                    <Badge variant="outline" className="ml-2">
                      {category.count}
                    </Badge>
                  </Button>
                ))}
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1 flex">
            {/* Templates Grid */}
            <div className="flex-1">
              <ScrollArea className="h-full">
                <div className="p-6">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                    {filteredTemplates.map((template) => {
                      const Icon = template.icon;
                      return (
                        <motion.div
                          key={template.id}
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          whileHover={{ scale: 1.02 }}
                          className="border rounded-lg p-4 hover:shadow-md transition-all cursor-pointer"
                          onClick={() => setSelectedTemplate(template)}
                        >
                          <div className="flex items-start gap-3">
                            <div className="p-2 rounded-lg bg-primary/10">
                              <Icon className="h-5 w-5 text-primary" />
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center gap-2 mb-1">
                                <h3 className="font-semibold truncate">{template.name}</h3>
                                {template.popular && (
                                  <Badge variant="secondary" className="text-xs">
                                    <Star className="h-3 w-3 mr-1" />
                                    Popular
                                  </Badge>
                                )}
                                {template.trending && (
                                  <Badge variant="outline" className="text-xs">
                                    <Zap className="h-3 w-3 mr-1" />
                                    Trending
                                  </Badge>
                                )}
                              </div>
                              <p className="text-sm text-muted-foreground line-clamp-2 mb-3">
                                {template.description}
                              </p>
                              <div className="flex items-center gap-2 mb-2">
                                <Badge className={cn("text-xs", getDifficultyColor(template.difficulty))}>
                                  {template.difficulty}
                                </Badge>
                                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                                  <Clock className="h-3 w-3" />
                                  {template.estimatedTime}
                                </div>
                              </div>
                              <div className="flex flex-wrap gap-1">
                                {template.tags.slice(0, 3).map((tag) => (
                                  <Badge key={tag} variant="outline" className="text-xs">
                                    {tag}
                                  </Badge>
                                ))}
                                {template.tags.length > 3 && (
                                  <Badge variant="outline" className="text-xs">
                                    +{template.tags.length - 3}
                                  </Badge>
                                )}
                              </div>
                            </div>
                          </div>
                        </motion.div>
                      );
                    })}
                  </div>

                  {filteredTemplates.length === 0 && (
                    <div className="text-center py-12">
                      <Search className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                      <h3 className="text-lg font-semibold mb-2">No templates found</h3>
                      <p className="text-muted-foreground">
                        Try adjusting your search or category filters
                      </p>
                    </div>
                  )}
                </div>
              </ScrollArea>
            </div>

            {/* Template Detail Panel */}
            <AnimatePresence>
              {selectedTemplate && (
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  className="w-96 border-l bg-muted/30 p-6"
                >
                  <div className="space-y-4">
                    <div className="flex items-start gap-3">
                      <div className="p-3 rounded-lg bg-primary/10">
                        <selectedTemplate.icon className="h-6 w-6 text-primary" />
                      </div>
                      <div className="flex-1">
                        <h3 className="font-semibold text-lg">{selectedTemplate.name}</h3>
                        <p className="text-sm text-muted-foreground mt-1">
                          {selectedTemplate.description}
                        </p>
                      </div>
                    </div>

                    <div className="space-y-3">
                      <div>
                        <Label className="text-xs font-semibold text-muted-foreground uppercase tracking-wide">
                          Details
                        </Label>
                        <div className="mt-2 space-y-2">
                          <div className="flex justify-between text-sm">
                            <span>Difficulty:</span>
                            <Badge className={getDifficultyColor(selectedTemplate.difficulty)}>
                              {selectedTemplate.difficulty}
                            </Badge>
                          </div>
                          <div className="flex justify-between text-sm">
                            <span>Estimated Time:</span>
                            <span className="text-muted-foreground">{selectedTemplate.estimatedTime}</span>
                          </div>
                          <div className="flex justify-between text-sm">
                            <span>Category:</span>
                            <span className="text-muted-foreground capitalize">{selectedTemplate.category}</span>
                          </div>
                        </div>
                      </div>

                      <div>
                        <Label className="text-xs font-semibold text-muted-foreground uppercase tracking-wide">
                          Context Setup
                        </Label>
                        <ul className="mt-2 space-y-1 text-sm">
                          {selectedTemplate.contextSetup.map((item, index) => (
                            <li key={index} className="flex items-center gap-2">
                              <CheckCircle className="h-3 w-3 text-green-500" />
                              <span className="text-muted-foreground">{item}</span>
                            </li>
                          ))}
                        </ul>
                      </div>

                      <div>
                        <Label className="text-xs font-semibold text-muted-foreground uppercase tracking-wide">
                          Tags
                        </Label>
                        <div className="mt-2 flex flex-wrap gap-1">
                          {selectedTemplate.tags.map((tag) => (
                            <Badge key={tag} variant="outline" className="text-xs">
                              <Tag className="h-3 w-3 mr-1" />
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      </div>

                      {selectedTemplate.initialPrompt && (
                        <div>
                          <Label className="text-xs font-semibold text-muted-foreground uppercase tracking-wide">
                            Initial Prompt
                          </Label>
                          <div className="mt-2 p-3 bg-background rounded-md border text-sm">
                            {selectedTemplate.initialPrompt}
                          </div>
                        </div>
                      )}
                    </div>

                    <Button 
                      onClick={() => handleTemplateSelect(selectedTemplate)}
                      className="w-full"
                      size="lg"
                    >
                      Start Session
                    </Button>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}