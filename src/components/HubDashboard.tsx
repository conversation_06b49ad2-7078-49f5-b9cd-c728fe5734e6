import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  RefreshCw, 
  Settings, 
  Plus, 
  Grid, 
  Eye,
  EyeOff,
  Activity,
  Zap
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { WidgetGrid } from './hub/WidgetGrid';
import { useHubStore } from '@/stores/hubStore';
import { usePerformanceMonitor } from '@/hooks/usePerformanceMonitor';
import { useCacheManager } from '@/lib/cache-manager';
import { ErrorBoundary } from '@/components/ErrorBoundary';
import { HubSettingsModal } from './hub/settings/HubSettingsModal';
import type { HubWidget } from '@/types/hub';

interface HubDashboardProps {
  className?: string;
}

export const HubDashboard: React.FC<HubDashboardProps> = ({ 
  className 
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [showPerformanceMetrics, setShowPerformanceMetrics] = useState(false);

  const {
    widgets,
    isLoading,
    error,
    lastRefresh,
    autoRefresh,
    refreshInterval,
    settings,
    
    // Actions
    initializeWidgets,
    refreshAllWidgets,
    refreshWidget,
    toggleAutoRefresh,
    setRefreshInterval,
    addWidget,
    removeWidget,
    updateWidget,
    clearError
  } = useHubStore();

  const { getMetrics } = usePerformanceMonitor();
  const { getMetrics: getCacheMetrics, optimize: optimizeCache } = useCacheManager();

  // Initialize widgets on mount
  useEffect(() => {
    initializeWidgets();
  }, [initializeWidgets]);

  // Auto-refresh logic
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      refreshAllWidgets();
    }, refreshInterval * 60 * 1000); // Convert minutes to milliseconds

    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, refreshAllWidgets]);

  // Optimize cache periodically
  useEffect(() => {
    const interval = setInterval(() => {
      optimizeCache();
    }, 10 * 60 * 1000); // Every 10 minutes

    return () => clearInterval(interval);
  }, [optimizeCache]);

  const handleRefreshAll = useCallback(async () => {
    await refreshAllWidgets();
  }, [refreshAllWidgets]);

  const handleToggleEditing = useCallback(() => {
    setIsEditing(!isEditing);
  }, [isEditing]);

  const handleAddWidget = useCallback((type: HubWidget['type']) => {
    addWidget(type);
  }, [addWidget]);

  const handleWidgetUpdate = useCallback((widgetId: string, updates: Partial<HubWidget>) => {
    updateWidget(widgetId, updates);
  }, [updateWidget]);

  const handleWidgetRemove = useCallback((widgetId: string) => {
    removeWidget(widgetId);
  }, [removeWidget]);

  const handleWidgetRefresh = useCallback((widgetId: string) => {
    refreshWidget(widgetId);
  }, [refreshWidget]);

  const formatLastRefresh = (date: Date | null) => {
    if (!date) return 'Never';
    
    // Ensure we have a valid Date object
    const dateObj = date instanceof Date ? date : new Date(date);
    if (isNaN(dateObj.getTime())) return 'Never';
    
    const now = new Date();
    const diffMs = now.getTime() - dateObj.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `${diffHours}h ago`;
    const diffDays = Math.floor(diffHours / 24);
    return `${diffDays}d ago`;
  };

  return (
    <div className={cn("flex flex-col h-full bg-background", className)}>
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="flex-shrink-0 border-b bg-card/80 backdrop-blur-sm p-4"
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-gradient-to-br from-blue-500/10 to-purple-500/10">
              <Grid className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <h1 className="text-xl font-semibold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Dashboard Hub
              </h1>
              <p className="text-sm text-muted-foreground">
                Last updated: {formatLastRefresh(lastRefresh)}
              </p>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            {/* Auto-refresh toggle */}
            <div className="flex items-center gap-2">
              <Label htmlFor="auto-refresh" className="text-sm">
                Auto-refresh
              </Label>
              <Switch
                id="auto-refresh"
                checked={autoRefresh}
                onCheckedChange={toggleAutoRefresh}
              />
            </div>

            {/* Refresh interval selector */}
            {autoRefresh && (
              <Select 
                value={refreshInterval.toString()} 
                onValueChange={(value) => setRefreshInterval(parseInt(value))}
              >
                <SelectTrigger className="w-20">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="15">15m</SelectItem>
                  <SelectItem value="30">30m</SelectItem>
                  <SelectItem value="60">1h</SelectItem>
                  <SelectItem value="120">2h</SelectItem>
                </SelectContent>
              </Select>
            )}

            {/* Manual refresh */}
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefreshAll}
              disabled={isLoading}
              className="gap-2"
            >
              <RefreshCw className={cn("h-4 w-4", isLoading && "animate-spin")} />
              Refresh
            </Button>

            {/* Edit mode toggle */}
            <Button
              variant={isEditing ? "default" : "outline"}
              size="sm"
              onClick={handleToggleEditing}
              className="gap-2"
            >
              {isEditing ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              {isEditing ? "Exit Edit" : "Edit"}
            </Button>

            {/* Settings */}
            {/* Performance metrics toggle */}
            {settings.advanced.debugMode && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowPerformanceMetrics(!showPerformanceMetrics)}
                title="Performance Metrics"
              >
                <Activity className="h-4 w-4" />
              </Button>
            )}

            {/* Settings */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowSettings(!showSettings)}
            >
              <Settings className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Add widget bar - shown in edit mode */}
        <AnimatePresence>
          {isEditing && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
              className="mt-4 pt-4 border-t"
            >
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium">Add Widget:</span>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleAddWidget('reddit')}
                    className="gap-2"
                  >
                    <Plus className="h-3 w-3" />
                    Reddit
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleAddWidget('github-trending')}
                    className="gap-2"
                  >
                    <Plus className="h-3 w-3" />
                    GitHub Trending
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleAddWidget('coding-progress')}
                    className="gap-2"
                  >
                    <Plus className="h-3 w-3" />
                    Coding Progress
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleAddWidget('ai-news')}
                    className="gap-2"
                  >
                    <Plus className="h-3 w-3" />
                    AI News
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleAddWidget('learning')}
                    className="gap-2"
                  >
                    <Plus className="h-3 w-3" />
                    Learning
                  </Button>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Performance Metrics Panel */}
        <AnimatePresence>
          {showPerformanceMetrics && settings.advanced.debugMode && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
              className="mt-4 pt-4 border-t"
            >
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {(() => {
                  const perfMetrics = getMetrics();
                  const cacheMetrics = getCacheMetrics();
                  
                  return (
                    <>
                      <div className="bg-muted/50 rounded-lg p-3">
                        <div className="flex items-center gap-2 mb-1">
                          <Zap className="h-4 w-4 text-blue-500" />
                          <span className="text-sm font-medium">Performance</span>
                        </div>
                        <div className="text-xs text-muted-foreground space-y-1">
                          <div>Avg Render: {perfMetrics.average.renderTime?.toFixed(1) || 0}ms</div>
                          <div>Memory: {((perfMetrics.average.memoryUsage || 0) / 1024 / 1024).toFixed(1)}MB</div>
                        </div>
                      </div>

                      <div className="bg-muted/50 rounded-lg p-3">
                        <div className="flex items-center gap-2 mb-1">
                          <Activity className="h-4 w-4 text-green-500" />
                          <span className="text-sm font-medium">Cache</span>
                        </div>
                        <div className="text-xs text-muted-foreground space-y-1">
                          <div>Hit Rate: {(cacheMetrics.hitRate * 100).toFixed(1)}%</div>
                          <div>Size: {(cacheMetrics.totalSize / 1024 / 1024).toFixed(1)}MB</div>
                        </div>
                      </div>

                      <div className="bg-muted/50 rounded-lg p-3">
                        <div className="flex items-center gap-2 mb-1">
                          <Grid className="h-4 w-4 text-orange-500" />
                          <span className="text-sm font-medium">Widgets</span>
                        </div>
                        <div className="text-xs text-muted-foreground space-y-1">
                          <div>Total: {widgets.length}</div>
                          <div>Enabled: {widgets.filter(w => w.isEnabled).length}</div>
                        </div>
                      </div>

                      <div className="bg-muted/50 rounded-lg p-3">
                        <div className="flex items-center gap-2 mb-1">
                          <RefreshCw className="h-4 w-4 text-purple-500" />
                          <span className="text-sm font-medium">Refresh</span>
                        </div>
                        <div className="text-xs text-muted-foreground space-y-1">
                          <div>Auto: {autoRefresh ? 'On' : 'Off'}</div>
                          <div>Interval: {refreshInterval}min</div>
                        </div>
                      </div>
                    </>
                  );
                })()}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>

      {/* Error Display */}
      {error && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="mx-4 mt-4 rounded-lg border border-destructive/50 bg-destructive/10 p-3 text-sm text-destructive"
        >
          <div className="flex items-center justify-between">
            <span>{error}</span>
            <Button
              variant="ghost"
              size="sm"
              onClick={clearError}
              className="h-auto p-1 text-destructive hover:text-destructive"
            >
              ×
            </Button>
          </div>
        </motion.div>
      )}

      {/* Widget Grid */}
      <div className="flex-1 overflow-auto p-4">
        <ErrorBoundary level="page" context="Dashboard">
          {widgets.length === 0 ? (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="flex flex-col items-center justify-center h-64 text-center"
            >
              <div className="p-4 rounded-full bg-muted mb-4">
                <Grid className="h-8 w-8 text-muted-foreground" />
              </div>
              <h3 className="text-lg font-medium mb-2">No widgets added yet</h3>
              <p className="text-sm text-muted-foreground mb-4 max-w-md">
                Click "Edit" and then add widgets to customize your dashboard with Reddit feeds, 
                GitHub trending, coding progress, and more.
              </p>
              <Button onClick={handleToggleEditing} className="gap-2">
                <Plus className="h-4 w-4" />
                Add Your First Widget
              </Button>
            </motion.div>
          ) : (
            <WidgetGrid
              widgets={widgets}
              isEditing={isEditing}
              onWidgetUpdate={handleWidgetUpdate}
              onWidgetRemove={handleWidgetRemove}
              onWidgetRefresh={handleWidgetRefresh}
            />
          )}
        </ErrorBoundary>
      </div>

      {/* Settings Modal */}
      <HubSettingsModal
        isOpen={showSettings}
        onClose={() => setShowSettings(false)}
      />
    </div>
  );
};