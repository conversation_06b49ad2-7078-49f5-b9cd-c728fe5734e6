import React, { useState, useEffect, useCallback } from 'react';
import {
  Lightbulb,
  ThumbsUp,
  ThumbsDown,
  X,
  ExternalLink,
  Zap,
  TrendingUp,
  Download,
  RefreshCw,
  ChevronRight,
  Info
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { 
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import { cn } from '@/lib/utils';
import { recommendationAPI, type Recommendation, type RecommendationOptions } from '@/lib/recommendation-api';
import { useInteractionTracker } from '@/lib/recommendation-tracking';

interface RecommendationPanelProps {
  className?: string;
  projectContext?: Record<string, any>;
  onRecommendationAction?: (recommendation: Recommendation, action: string) => void;
  maxRecommendations?: number;
  showExplanations?: boolean;
}

interface RecommendationCardProps {
  recommendation: Recommendation;
  onAction: (action: string) => void;
  showExplanation: boolean;
}

const RecommendationCard: React.FC<RecommendationCardProps> = ({
  recommendation,
  onAction,
  showExplanation
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  
  const getRecommendationIcon = (type: string) => {
    switch (type) {
      case 'agent': return '🤖';
      case 'feature': return '✨';
      case 'workflow': return '🔄';
      case 'configuration': return '⚙️';
      default: return '💡';
    }
  };

  const getConfidenceColor = (score: number) => {
    if (score >= 0.8) return 'text-green-600';
    if (score >= 0.6) return 'text-yellow-600';
    return 'text-red-600';
  };

  const formatScore = (score: number) => Math.round(score * 100);

  return (
    <Card className="group hover:shadow-md transition-all duration-200 border-l-4 border-l-blue-500">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-3">
            <div className="text-2xl">
              {recommendation.icon || getRecommendationIcon(recommendation.recommendationType)}
            </div>
            <div className="flex-1 min-w-0">
              <CardTitle className="text-sm font-semibold text-foreground line-clamp-2">
                {recommendation.title || recommendation.itemId}
              </CardTitle>
              <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                {recommendation.description || 'No description available'}
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger>
                  <Badge 
                    variant="secondary" 
                    className={cn("text-xs", getConfidenceColor(recommendation.confidenceScore))}
                  >
                    {formatScore(recommendation.confidenceScore)}%
                  </Badge>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Confidence: {formatScore(recommendation.confidenceScore)}%</p>
                  <p>Relevance: {formatScore(recommendation.relevanceScore)}%</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        {/* Progress bars for scores */}
        <div className="space-y-2 mb-4">
          <div className="flex items-center justify-between text-xs">
            <span className="text-muted-foreground">Relevance</span>
            <span className="font-medium">{formatScore(recommendation.relevanceScore)}%</span>
          </div>
          <Progress value={recommendation.relevanceScore * 100} className="h-1" />
        </div>

        {/* Expected benefits */}
        {recommendation.expectedBenefit && (
          <div className="mb-4">
            <div className="flex items-center space-x-2 mb-2">
              <TrendingUp className="w-3 h-3 text-green-600" />
              <span className="text-xs font-medium text-muted-foreground">Expected Benefits</span>
            </div>
            <div className="flex flex-wrap gap-1">
              {Object.entries(recommendation.expectedBenefit).map(([key, value]) => (
                <Badge key={key} variant="outline" className="text-xs">
                  {String(value)}
                </Badge>
              ))}
            </div>
          </div>
        )}

        {/* Action buttons */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Button
              size="sm"
              onClick={() => onAction('adopted')}
              className="text-xs"
            >
              <Download className="w-3 h-3 mr-1" />
              {recommendation.actionText || 'Try This'}
            </Button>
            
            {recommendation.learnMoreUrl && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onAction('learn_more')}
                className="text-xs"
              >
                <ExternalLink className="w-3 h-3 mr-1" />
                Learn More
              </Button>
            )}
          </div>

          <div className="flex items-center space-x-1">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onAction('liked')}
                    className="p-1 h-auto text-muted-foreground hover:text-green-600"
                  >
                    <ThumbsUp className="w-3 h-3" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Like this recommendation</TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onAction('disliked')}
                    className="p-1 h-auto text-muted-foreground hover:text-red-600"
                  >
                    <ThumbsDown className="w-3 h-3" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Not interested</TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onAction('dismissed')}
                    className="p-1 h-auto text-muted-foreground hover:text-red-600"
                  >
                    <X className="w-3 h-3" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Dismiss</TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>

        {/* Expandable explanation */}
        {showExplanation && recommendation.reasoningData && (
          <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
            <Separator className="my-3" />
            <CollapsibleTrigger asChild>
              <Button variant="ghost" size="sm" className="text-xs text-muted-foreground p-0">
                <Info className="w-3 h-3 mr-1" />
                Why this recommendation?
                <ChevronRight className={cn("w-3 h-3 ml-1 transition-transform", isExpanded && "rotate-90")} />
              </Button>
            </CollapsibleTrigger>
            <CollapsibleContent className="mt-2">
              <div className="text-xs text-muted-foreground space-y-1">
                {Object.entries(recommendation.reasoningData).map(([key, value]) => (
                  <div key={key} className="flex justify-between">
                    <span className="capitalize">{key.replace(/_/g, ' ')}:</span>
                    <span>{String(value)}</span>
                  </div>
                ))}
              </div>
            </CollapsibleContent>
          </Collapsible>
        )}
      </CardContent>
    </Card>
  );
};

export const RecommendationPanel: React.FC<RecommendationPanelProps> = ({
  className,
  projectContext,
  onRecommendationAction,
  maxRecommendations = 5,
  showExplanations = true
}) => {
  const [recommendations, setRecommendations] = useState<Recommendation[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());
  const [isCollapsed, setIsCollapsed] = useState(false);

  const { trackInteraction, trackRecommendationShown } = useInteractionTracker();

  const fetchRecommendations = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const options: RecommendationOptions = {
        algorithm: 'hybrid',
        limit: maxRecommendations,
        minConfidence: 0.3,
        includeExplanations: showExplanations,
        projectContext
      };

      const newRecommendations = await recommendationAPI.getRecommendations('agent', options);
      setRecommendations(newRecommendations);
      setLastRefresh(new Date());

      // Track that recommendations were shown
      for (const rec of newRecommendations) {
        if (rec.id) {
          await trackRecommendationShown(rec.id, rec.itemId, rec.recommendationType);
        }
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch recommendations');
      console.error('Failed to fetch recommendations:', err);
    } finally {
      setIsLoading(false);
    }
  }, [maxRecommendations, showExplanations, projectContext, trackRecommendationShown]);

  const handleRecommendationAction = useCallback(async (
    recommendation: Recommendation,
    action: string
  ) => {
    try {
      // Track the interaction
      await trackInteraction({
        interactionType: 'recommendation_clicked',
        featureId: recommendation.itemId,
        featureCategory: 'recommendation',
        contextData: {
          recommendationType: recommendation.recommendationType,
          action,
          confidenceScore: recommendation.confidenceScore,
          relevanceScore: recommendation.relevanceScore
        },
        successIndicator: action === 'adopted'
      });

      // Submit feedback if recommendation has an ID
      if (recommendation.id) {
        await recommendationAPI.submitFeedback({
          recommendationId: recommendation.id,
          action: action as any,
          context: { action }
        });
      }

      // Handle specific actions
      switch (action) {
        case 'adopted':
        case 'learn_more':
          onRecommendationAction?.(recommendation, action);
          break;
        case 'dismissed':
        case 'disliked':
          // Remove from current list
          setRecommendations(prev => prev.filter(r => r.itemId !== recommendation.itemId));
          break;
        case 'liked':
          // Could add visual feedback here
          break;
      }

    } catch (error) {
      console.error('Failed to handle recommendation action:', error);
    }
  }, [trackInteraction, onRecommendationAction]);

  // Initial load
  useEffect(() => {
    fetchRecommendations();
  }, [fetchRecommendations]);

  // Auto-refresh every 10 minutes
  useEffect(() => {
    const interval = setInterval(fetchRecommendations, 10 * 60 * 1000);
    return () => clearInterval(interval);
  }, [fetchRecommendations]);

  const formatLastRefresh = (date: Date) => {
    const now = new Date();
    const diffMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffMinutes < 1) return 'Just now';
    if (diffMinutes < 60) return `${diffMinutes}m ago`;
    const diffHours = Math.floor(diffMinutes / 60);
    if (diffHours < 24) return `${diffHours}h ago`;
    return date.toLocaleDateString();
  };

  if (error) {
    return (
      <Card className={cn("border-red-200", className)}>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm text-red-600 flex items-center">
            <X className="w-4 h-4 mr-2" />
            Recommendation Error
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-xs text-muted-foreground mb-3">{error}</p>
          <Button size="sm" variant="outline" onClick={fetchRecommendations}>
            <RefreshCw className="w-3 h-3 mr-1" />
            Retry
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn("overflow-hidden", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm font-semibold flex items-center">
            <Lightbulb className="w-4 h-4 mr-2 text-yellow-500" />
            Smart Recommendations
            {recommendations.length > 0 && (
              <Badge variant="secondary" className="ml-2 text-xs">
                {recommendations.length}
              </Badge>
            )}
          </CardTitle>
          
          <div className="flex items-center space-x-1">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={fetchRecommendations}
                    disabled={isLoading}
                    className="p-1 h-auto"
                  >
                    <RefreshCw className={cn("w-3 h-3", isLoading && "animate-spin")} />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Refresh recommendations</TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsCollapsed(!isCollapsed)}
                    className="p-1 h-auto"
                  >
                    <ChevronRight className={cn("w-3 h-3 transition-transform", !isCollapsed && "rotate-90")} />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Toggle recommendations</TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>

        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <span>Last updated: {formatLastRefresh(lastRefresh)}</span>
          {recommendations.length > 0 && (
            <span className="flex items-center">
              <Zap className="w-3 h-3 mr-1" />
              Personalized for you
            </span>
          )}
        </div>
      </CardHeader>

      <Collapsible open={!isCollapsed} onOpenChange={(open) => setIsCollapsed(!open)}>
        <CollapsibleContent>
          <CardContent className="pt-0">
            {isLoading ? (
              <div className="space-y-3">
                {[...Array(3)].map((_, i) => (
                  <div key={i} className="animate-pulse">
                    <div className="h-4 bg-muted rounded mb-2" />
                    <div className="h-3 bg-muted rounded w-2/3" />
                  </div>
                ))}
              </div>
            ) : recommendations.length > 0 ? (
              <div className="space-y-4">
                {recommendations.map((recommendation, index) => (
                  <RecommendationCard
                    key={`${recommendation.itemId}-${index}`}
                    recommendation={recommendation}
                    onAction={(action) => handleRecommendationAction(recommendation, action)}
                    showExplanation={showExplanations}
                  />
                ))}
              </div>
            ) : (
              <div className="text-center py-6">
                <Lightbulb className="w-8 h-8 text-muted-foreground mx-auto mb-2" />
                <p className="text-sm text-muted-foreground">
                  No recommendations available right now.
                </p>
                <p className="text-xs text-muted-foreground mt-1">
                  Keep using the app to get personalized suggestions!
                </p>
              </div>
            )}
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  );
};
