import React, { useState, useEffect, useRef, useCallback } from "react";
import {
  <PERSON>,
  Brain,
  Shield,
  Zap,
  GraduationCap,
  Play,
  Settings,
  MessageSquare,
  ArrowLeft,
  Target,
  AlertCircle,
  Loader2
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Progress } from "@/components/ui/progress";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { cn } from "@/lib/utils";

// Claude Personality Types
export type ClaudePersonality = 'architect' | 'reviewer' | 'optimizer' | 'teacher';

// Ensemble Session State
interface EnsembleSessionState {
  id: string;
  projectPath: string;
  problem: string;
  status: 'idle' | 'planning' | 'executing' | 'reviewing' | 'completed' | 'error';
  currentPhase: string;
  progress: number;
  participants: ClaudeParticipant[];
  conversations: EnsembleConversation[];
  decisions: EnsembleDecision[];
  artifacts: EnsembleArtifact[];
  createdAt: string;
  completedAt?: string;
}

// Individual Claude Participant
interface ClaudeParticipant {
  id: string;
  personality: ClaudePersonality;
  name: string;
  role: string;
  status: 'idle' | 'thinking' | 'responding' | 'waiting' | 'done';
  sessionId?: string;
  contributions: number;
  lastActivity?: string;
  expertise: string[];
  currentTask?: string;
}

// Ensemble Conversation
interface EnsembleConversation {
  id: string;
  participantId: string;
  message: string;
  timestamp: string;
  type: 'analysis' | 'proposal' | 'critique' | 'question' | 'decision' | 'implementation' | 'discussion';
  referencedArtifacts?: string[];
  votes?: { participantId: string; vote: 'agree' | 'disagree' | 'neutral' }[];
}

// Ensemble Decision
interface EnsembleDecision {
  id: string;
  title: string;
  description: string;
  proposedBy: string;
  status: 'proposed' | 'discussing' | 'approved' | 'rejected' | 'implemented';
  votes: { participantId: string; vote: 'approve' | 'reject' | 'abstain'; reason?: string }[];
  timestamp: string;
  implementation?: string;
}

// Ensemble Artifact
interface EnsembleArtifact {
  id: string;
  type: 'code' | 'design' | 'documentation' | 'test' | 'analysis';
  title: string;
  content: string;
  createdBy: string;
  timestamp: string;
  version: number;
  status: 'draft' | 'review' | 'approved' | 'implemented';
  reviews?: { participantId: string; feedback: string; approved: boolean }[];
}

// Claude Personality Configurations
const CLAUDE_PERSONALITIES: Record<ClaudePersonality, {
  name: string;
  role: string;
  icon: React.ComponentType<any>;
  color: string;
  expertise: string[];
  systemPrompt: string;
  focusAreas: string[];
}> = {
  architect: {
    name: "Architect Claude",
    role: "System Design & Architecture",
    icon: Brain,
    color: "bg-blue-500",
    expertise: ["System Design", "Architecture Patterns", "Scalability", "Integration"],
    systemPrompt: "You are an expert software architect focused on system design, architecture patterns, scalability, and technical decision-making. Analyze problems from a high-level architectural perspective and propose robust, scalable solutions.",
    focusAreas: ["Architecture Decisions", "System Design", "Technical Strategy", "Integration Patterns"]
  },
  reviewer: {
    name: "Reviewer Claude",
    role: "Code Quality & Security",
    icon: Shield,
    color: "bg-red-500",
    expertise: ["Code Review", "Security Analysis", "Best Practices", "Quality Assurance"],
    systemPrompt: "You are a meticulous code reviewer and security analyst. Focus on code quality, security vulnerabilities, best practices, and potential issues. Provide constructive feedback and ensure high standards.",
    focusAreas: ["Code Quality", "Security Analysis", "Best Practices", "Risk Assessment"]
  },
  optimizer: {
    name: "Optimizer Claude",
    role: "Performance & Efficiency",
    icon: Zap,
    color: "bg-yellow-500",
    expertise: ["Performance Optimization", "Efficiency", "Resource Management", "Benchmarking"],
    systemPrompt: "You are a performance optimization specialist. Focus on improving efficiency, reducing resource usage, optimizing algorithms, and enhancing overall system performance.",
    focusAreas: ["Performance Tuning", "Resource Optimization", "Algorithm Efficiency", "Benchmarking"]
  },
  teacher: {
    name: "Teacher Claude",
    role: "Education & Mentorship",
    icon: GraduationCap,
    color: "bg-green-500",
    expertise: ["Education", "Mentorship", "Documentation", "Knowledge Transfer"],
    systemPrompt: "You are an educational mentor focused on explaining concepts, providing learning guidance, creating documentation, and ensuring knowledge transfer. Make complex topics accessible and educational.",
    focusAreas: ["Educational Explanations", "Mentorship", "Documentation", "Knowledge Sharing"]
  }
};

interface EnsembleSessionProps {
  projectPath: string;
  initialProblem?: string;
  onBack: () => void;
  className?: string;
}

export const EnsembleSession: React.FC<EnsembleSessionProps> = ({
  projectPath,
  initialProblem = "",
  onBack,
  className
}) => {
  const [session, setSession] = useState<EnsembleSessionState | null>(null);
  const [problem, setProblem] = useState(initialProblem);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedParticipant, setSelectedParticipant] = useState<string | null>(null);
   
  const [activeTab, setActiveTab] = useState("overview");
  const conversationEndRef = useRef<HTMLDivElement>(null);

  // Initialize ensemble session
  const initializeSession = useCallback(async () => {
    if (!problem.trim()) return;

    setIsLoading(true);
    setError(null);

    try {
      const sessionId = `ensemble_${Date.now()}`;
      
      // Create participants
      const participants: ClaudeParticipant[] = Object.entries(CLAUDE_PERSONALITIES).map(([personality, config]) => ({
        id: `${personality}_${sessionId}`,
        personality: personality as ClaudePersonality,
        name: config.name,
        role: config.role,
        status: 'idle',
        contributions: 0,
        expertise: config.expertise,
        currentTask: undefined
      }));

      const newSession: EnsembleSessionState = {
        id: sessionId,
        projectPath,
        problem,
        status: 'planning',
        currentPhase: 'Problem Analysis',
        progress: 0,
        participants,
        conversations: [],
        decisions: [],
        artifacts: [],
        createdAt: new Date().toISOString()
      };

      setSession(newSession);
      
      // Start the ensemble process
      await startEnsembleProcess(newSession);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to initialize ensemble session');
    } finally {
      setIsLoading(false);
    }
  }, [problem, projectPath]);

  // Start the ensemble collaboration process
  const startEnsembleProcess = async (sessionState: EnsembleSessionState) => {
    try {
      // Phase 1: Problem Analysis
      await runPhase(sessionState, 'Problem Analysis', async () => {
        // Each participant analyzes the problem from their perspective
        for (const participant of sessionState.participants) {
          await participantAnalyze(sessionState, participant);
        }
      });

      // Phase 2: Solution Design
      await runPhase(sessionState, 'Solution Design', async () => {
        // Architect leads design, others contribute
        const architect = sessionState.participants.find((p: ClaudeParticipant) => p.personality === 'architect')!;
        await participantPropose(sessionState, architect, 'architecture');
        
        // Others review and suggest improvements
        for (const participant of sessionState.participants.filter((p: ClaudeParticipant) => p.id !== architect.id)) {
          await participantReview(sessionState, participant, 'architecture');
        }
      });

      // Phase 3: Implementation Planning
      await runPhase(sessionState, 'Implementation Planning', async () => {
        // Collaborative implementation planning
        await collaborativeDiscussion(sessionState, 'implementation');
      });

      // Phase 4: Quality Assurance
      await runPhase(sessionState, 'Quality Assurance', async () => {
        // Reviewer leads QA, others contribute
        const reviewer = sessionState.participants.find((p: ClaudeParticipant) => p.personality === 'reviewer')!;
        await participantAnalyze(sessionState, reviewer, 'quality_assurance');
      });

      // Complete session
      setSession(prev => prev ? {
        ...prev,
        status: 'completed',
        progress: 100,
        completedAt: new Date().toISOString()
      } : null);

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Ensemble process failed');
      setSession(prev => prev ? { ...prev, status: 'error' } : null);
    }
  };

  // Run a specific phase
  const runPhase = async (_sessionState: EnsembleSessionState, phaseName: string, phaseLogic: () => Promise<void>) => {
    setSession(prev => prev ? {
      ...prev,
      currentPhase: phaseName,
      status: 'executing'
    } : null);

    await phaseLogic();
    
    // Update progress
    const phases = ['Problem Analysis', 'Solution Design', 'Implementation Planning', 'Quality Assurance'];
    const currentPhaseIndex = phases.indexOf(phaseName);
    const progress = ((currentPhaseIndex + 1) / phases.length) * 100;
    
    setSession(prev => prev ? { ...prev, progress } : null);
  };

  // Participant analysis
  const participantAnalyze = async (sessionState: EnsembleSessionState, participant: ClaudeParticipant, context?: string) => {
    // Update participant status
    setSession(prev => prev ? {
      ...prev,
      participants: prev.participants.map(p => 
        p.id === participant.id ? { ...p, status: 'thinking' } : p
      )
    } : null);

    // Simulate AI analysis (in real implementation, this would call Claude API)
    await new Promise(resolve => setTimeout(resolve, 2000));

    // const config = CLAUDE_PERSONALITIES[participant.personality];
    const analysisMessage = generateAnalysisMessage(participant, sessionState.problem, context);

    // Add conversation
    const conversation: EnsembleConversation = {
      id: `conv_${Date.now()}_${participant.id}`,
      participantId: participant.id,
      message: analysisMessage,
      timestamp: new Date().toISOString(),
      type: 'analysis'
    };

    setSession(prev => prev ? {
      ...prev,
      conversations: [...prev.conversations, conversation],
      participants: prev.participants.map(p => 
        p.id === participant.id ? { 
          ...p, 
          status: 'done', 
          contributions: p.contributions + 1,
          lastActivity: new Date().toISOString()
        } : p
      )
    } : null);
  };

  // Participant proposal
  const participantPropose = async (sessionState: EnsembleSessionState, participant: ClaudeParticipant, type: string) => {
    setSession(prev => prev ? {
      ...prev,
      participants: prev.participants.map(p => 
        p.id === participant.id ? { ...p, status: 'thinking' } : p
      )
    } : null);

    await new Promise(resolve => setTimeout(resolve, 3000));

    const proposal = generateProposal(participant, sessionState.problem, type);
    
    // Create artifact
    const artifact: EnsembleArtifact = {
      id: `artifact_${Date.now()}`,
      type: type === 'architecture' ? 'design' : 'code',
      title: `${participant.name} ${type} Proposal`,
      content: proposal,
      createdBy: participant.id,
      timestamp: new Date().toISOString(),
      version: 1,
      status: 'review'
    };

    setSession(prev => prev ? {
      ...prev,
      artifacts: [...prev.artifacts, artifact],
      participants: prev.participants.map(p => 
        p.id === participant.id ? { 
          ...p, 
          status: 'done', 
          contributions: p.contributions + 1
        } : p
      )
    } : null);
  };

  // Participant review
  const participantReview = async (_sessionState: EnsembleSessionState, participant: ClaudeParticipant, artifactType: string) => {
    setSession(prev => prev ? {
      ...prev,
      participants: prev.participants.map(p => 
        p.id === participant.id ? { ...p, status: 'thinking' } : p
      )
    } : null);

    await new Promise(resolve => setTimeout(resolve, 2000));

    const reviewMessage = generateReviewMessage(participant, artifactType);
    
    const conversation: EnsembleConversation = {
      id: `conv_${Date.now()}_${participant.id}`,
      participantId: participant.id,
      message: reviewMessage,
      timestamp: new Date().toISOString(),
      type: 'critique'
    };

    setSession(prev => prev ? {
      ...prev,
      conversations: [...prev.conversations, conversation],
      participants: prev.participants.map(p => 
        p.id === participant.id ? { 
          ...p, 
          status: 'done', 
          contributions: p.contributions + 1
        } : p
      )
    } : null);
  };

  // Collaborative discussion
  const collaborativeDiscussion = async (sessionState: EnsembleSessionState, topic: string) => {
    // Simulate back-and-forth discussion
    for (let i = 0; i < 3; i++) {
      for (const participant of sessionState.participants) {
        await participantContribute(sessionState, participant, topic, i);
      }
    }
  };

  // Participant contribution to discussion
  const participantContribute = async (_sessionState: EnsembleSessionState, participant: ClaudeParticipant, topic: string, round: number) => {
    setSession(prev => prev ? {
      ...prev,
      participants: prev.participants.map(p => 
        p.id === participant.id ? { ...p, status: 'thinking' } : p
      )
    } : null);

    await new Promise(resolve => setTimeout(resolve, 1500));

    const contribution = generateContribution(participant, topic, round);
    
    const conversation: EnsembleConversation = {
      id: `conv_${Date.now()}_${participant.id}`,
      participantId: participant.id,
      message: contribution,
      timestamp: new Date().toISOString(),
      type: round === 0 ? 'proposal' : 'discussion'
    };

    setSession(prev => prev ? {
      ...prev,
      conversations: [...prev.conversations, conversation],
      participants: prev.participants.map(p => 
        p.id === participant.id ? { 
          ...p, 
          status: 'done', 
          contributions: p.contributions + 1
        } : p
      )
    } : null);
  };

  // Generate analysis message (placeholder - would use real Claude API)
  const generateAnalysisMessage = (participant: ClaudeParticipant, _problem: string, _context?: string): string => {
    // const config = CLAUDE_PERSONALITIES[participant.personality];
    const templates = {
      architect: `From an architectural perspective, this problem requires careful consideration of system design patterns, scalability requirements, and integration points. I recommend analyzing the core components and their relationships...`,
      reviewer: `Looking at this from a code quality and security standpoint, I need to identify potential vulnerabilities, code smells, and areas where best practices should be enforced...`,
      optimizer: `Performance-wise, I see several optimization opportunities. We should focus on bottlenecks, resource utilization, and algorithmic efficiency...`,
      teacher: `Let me break this down into educational components to ensure everyone understands the problem domain and the learning opportunities it presents...`
    };
    return templates[participant.personality] || "Analyzing the problem...";
  };

  // Generate proposal (placeholder)
  const generateProposal = (participant: ClaudeParticipant, problem: string, type: string): string => {
    return `## ${participant.name} ${type} Proposal\n\nBased on my analysis of "${problem}", I propose the following approach:\n\n### Key Components\n- Component A: Core functionality\n- Component B: Integration layer\n- Component C: User interface\n\n### Implementation Strategy\n1. Start with core architecture\n2. Implement key features\n3. Add optimization layers\n4. Comprehensive testing\n\nThis approach aligns with ${participant.expertise.join(', ')} best practices.`;
  };

  // Generate review message (placeholder)
  const generateReviewMessage = (participant: ClaudeParticipant, artifactType: string): string => {
    const config = CLAUDE_PERSONALITIES[participant.personality];
    return `I've reviewed the ${artifactType} proposal. From my ${participant.role.toLowerCase()} perspective, I have the following feedback: The approach is solid but could benefit from ${config.focusAreas[0].toLowerCase()} considerations...`;
  };

  // Generate contribution (placeholder)
  const generateContribution = (participant: ClaudeParticipant, topic: string, round: number): string => {
    const contributions = {
      0: `For ${topic}, I suggest we focus on ${participant.expertise[0].toLowerCase()}...`,
      1: `Building on the previous discussion, I think we should also consider ${participant.expertise[1]?.toLowerCase() || 'additional factors'}...`,
      2: `To finalize our ${topic} approach, let's ensure we address ${participant.role.toLowerCase()} requirements...`
    };
    return contributions[round as keyof typeof contributions] || `Continuing the ${topic} discussion...`;
  };

  // Scroll to bottom of conversations
  useEffect(() => {
    conversationEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [session?.conversations]);

  if (!session) {
    return (
      <div className={cn("flex flex-col h-full", className)}>
        <div className="flex items-center justify-between p-4 border-b">
          <div className="flex items-center gap-3">
            <Button variant="ghost" size="sm" onClick={onBack}>
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <div className="flex items-center gap-2">
              <Users className="h-5 w-5 text-blue-500" />
              <h1 className="text-lg font-semibold">Multi-Claude Ensemble Session</h1>
            </div>
          </div>
        </div>

        <div className="flex-1 flex items-center justify-center p-8">
          <Card className="w-full max-w-2xl">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Start Ensemble Collaboration
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-2 gap-4">
                {Object.entries(CLAUDE_PERSONALITIES).map(([personality, config]) => {
                  const Icon = config.icon;
                  return (
                    <div key={personality} className="flex items-center gap-3 p-3 border rounded-lg">
                      <div className={cn("p-2 rounded-full text-white", config.color)}>
                        <Icon className="h-4 w-4" />
                      </div>
                      <div>
                        <div className="font-medium text-sm">{config.name}</div>
                        <div className="text-xs text-muted-foreground">{config.role}</div>
                      </div>
                    </div>
                  );
                })}
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Problem Description</label>
                <Textarea
                  value={problem}
                  onChange={(e) => setProblem(e.target.value)}
                  placeholder="Describe the complex problem you want the ensemble to collaborate on..."
                  rows={4}
                />
              </div>

              {error && (
                <div className="flex items-center gap-2 text-red-600 text-sm">
                  <AlertCircle className="h-4 w-4" />
                  {error}
                </div>
              )}

              <Button 
                onClick={initializeSession} 
                disabled={!problem.trim() || isLoading}
                className="w-full"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Initializing Ensemble...
                  </>
                ) : (
                  <>
                    <Play className="h-4 w-4 mr-2" />
                    Start Ensemble Collaboration
                  </>
                )}
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("flex flex-col h-full", className)}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center gap-3">
          <Button variant="ghost" size="sm" onClick={onBack}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div className="flex items-center gap-2">
            <Users className="h-5 w-5 text-blue-500" />
            <div>
              <h1 className="text-lg font-semibold">Ensemble Session</h1>
              <p className="text-sm text-muted-foreground">{session.currentPhase}</p>
            </div>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <Badge variant={session.status === 'completed' ? 'default' : 'secondary'}>
            {session.status}
          </Badge>
          <Button variant="ghost" size="sm"  >
            <Settings className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Progress */}
      <div className="p-4 border-b">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium">Progress</span>
          <span className="text-sm text-muted-foreground">{Math.round(session.progress)}%</span>
        </div>
        <Progress value={session.progress} className="h-2" />
      </div>

      {/* Main Content */}
      <div className="flex-1 flex">
        {/* Participants Sidebar */}
        <div className="w-80 border-r flex flex-col">
          <div className="p-4 border-b">
            <h3 className="font-medium mb-3">Participants</h3>
            <div className="space-y-2">
              {session.participants.map((participant) => {
                const config = CLAUDE_PERSONALITIES[participant.personality];
                const Icon = config.icon;
                return (
                  <div
                    key={participant.id}
                    className={cn(
                      "flex items-center gap-3 p-3 rounded-lg border cursor-pointer transition-colors",
                      selectedParticipant === participant.id ? "bg-accent" : "hover:bg-accent/50"
                    )}
                    onClick={() => setSelectedParticipant(participant.id)}
                  >
                    <div className={cn("p-2 rounded-full text-white", config.color)}>
                      <Icon className="h-4 w-4" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="font-medium text-sm">{participant.name}</div>
                      <div className="text-xs text-muted-foreground truncate">{participant.role}</div>
                      <div className="flex items-center gap-2 mt-1">
                        <Badge variant="outline" className="text-xs">
                          {participant.status}
                        </Badge>
                        <span className="text-xs text-muted-foreground">
                          {participant.contributions} contributions
                        </span>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>

        {/* Main Content Area */}
        <div className="flex-1 flex flex-col">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="conversations">Conversations</TabsTrigger>
              <TabsTrigger value="artifacts">Artifacts</TabsTrigger>
              <TabsTrigger value="decisions">Decisions</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="flex-1 p-4">
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Problem Statement</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground">{session.problem}</p>
                  </CardContent>
                </Card>

                <div className="grid grid-cols-2 gap-4">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-base flex items-center gap-2">
                        <MessageSquare className="h-4 w-4" />
                        Conversations
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{session.conversations.length}</div>
                      <p className="text-sm text-muted-foreground">Total messages</p>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="text-base flex items-center gap-2">
                        <Target className="h-4 w-4" />
                        Artifacts
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{session.artifacts.length}</div>
                      <p className="text-sm text-muted-foreground">Created artifacts</p>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="conversations" className="flex-1 flex flex-col">
              <ScrollArea className="flex-1 p-4">
                <div className="space-y-4">
                  {session.conversations.map((conversation) => {
                    const participant = session.participants.find(p => p.id === conversation.participantId);
                    const config = participant ? CLAUDE_PERSONALITIES[participant.personality] : null;
                    const Icon = config?.icon || MessageSquare;
                    
                    return (
                      <div key={conversation.id} className="flex gap-3">
                        <div className={cn("p-2 rounded-full text-white flex-shrink-0", config?.color || "bg-gray-500")}>
                          <Icon className="h-4 w-4" />
                        </div>
                        <div className="flex-1 space-y-1">
                          <div className="flex items-center gap-2">
                            <span className="font-medium text-sm">{participant?.name || 'Unknown'}</span>
                            <Badge variant="outline" className="text-xs">{conversation.type}</Badge>
                            <span className="text-xs text-muted-foreground">
                              {new Date(conversation.timestamp).toLocaleTimeString()}
                            </span>
                          </div>
                          <div className="text-sm text-muted-foreground whitespace-pre-wrap">
                            {conversation.message}
                          </div>
                        </div>
                      </div>
                    );
                  })}
                  <div ref={conversationEndRef} />
                </div>
              </ScrollArea>
            </TabsContent>

            <TabsContent value="artifacts" className="flex-1 p-4">
              <div className="space-y-4">
                {session.artifacts.map((artifact) => {
                  const participant = session.participants.find(p => p.id === artifact.createdBy);
                  return (
                    <Card key={artifact.id}>
                      <CardHeader>
                        <div className="flex items-center justify-between">
                          <CardTitle className="text-base">{artifact.title}</CardTitle>
                          <div className="flex items-center gap-2">
                            <Badge variant="outline">{artifact.type}</Badge>
                            <Badge variant={artifact.status === 'approved' ? 'default' : 'secondary'}>
                              {artifact.status}
                            </Badge>
                          </div>
                        </div>
                        <p className="text-sm text-muted-foreground">
                          Created by {participant?.name} • {new Date(artifact.timestamp).toLocaleString()}
                        </p>
                      </CardHeader>
                      <CardContent>
                        <pre className="text-sm bg-muted p-3 rounded-md overflow-auto max-h-40">
                          {artifact.content}
                        </pre>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            </TabsContent>

            <TabsContent value="decisions" className="flex-1 p-4">
              <div className="space-y-4">
                {session.decisions.length === 0 ? (
                  <div className="text-center text-muted-foreground py-8">
                    No decisions made yet
                  </div>
                ) : (
                  session.decisions.map((decision) => (
                    <Card key={decision.id}>
                      <CardHeader>
                        <div className="flex items-center justify-between">
                          <CardTitle className="text-base">{decision.title}</CardTitle>
                          <Badge variant={decision.status === 'approved' ? 'default' : 'secondary'}>
                            {decision.status}
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground">{decision.description}</p>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-2">
                          {decision.votes.map((vote, index) => {
                            const participant = session.participants.find(p => p.id === vote.participantId);
                            return (
                              <div key={index} className="flex items-center justify-between text-sm">
                                <span>{participant?.name}</span>
                                <Badge variant={vote.vote === 'approve' ? 'default' : 'destructive'}>
                                  {vote.vote}
                                </Badge>
                              </div>
                            );
                          })}
                        </div>
                      </CardContent>
                    </Card>
                  ))
                )}
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
};