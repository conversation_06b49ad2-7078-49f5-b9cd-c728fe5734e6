import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import {
  Settings,
  Palette,
  Download,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  Moon,
  Sun,
  Save,
  Keyboard
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Slider } from "@/components/ui/slider";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { cn } from "@/lib/utils";

interface AdvancedSettingsProps {
  onClose: () => void;
  settings: SessionSettings;
  onSettingsChange: (settings: SessionSettings) => void;
}

export interface SessionSettings {
  // General Settings
  defaultModel: 'sonnet' | 'opus';
  autoSave: boolean;
  autoSaveInterval: number; // seconds
  maxTokensPerRequest: number;
  
  // Appearance Settings
  theme: 'light' | 'dark' | 'system';
  fontSize: number;
  fontFamily: string;
  compactMode: boolean;
  showLineNumbers: boolean;
  
  // Behavior Settings
  enableNotifications: boolean;
  soundEnabled: boolean;
  autoScroll: boolean;
  confirmBeforeExit: boolean;
  saveSessionHistory: boolean;
  
  // Export Settings
  defaultExportFormat: 'markdown' | 'html' | 'pdf' | 'json';
  includeTimestamps: boolean;
  includeTokenCounts: boolean;
  
  // Advanced Settings
  enableExperimentalFeatures: boolean;
  debugMode: boolean;
  performanceMode: boolean;
  maxConcurrentRequests: number;
  
  // Keyboard Shortcuts
  shortcuts: {
    sendMessage: string;
    newSession: string;
    saveSession: string;
    toggleSidebar: string;
    focusInput: string;
    clearChat: string;
  };
}



export const AdvancedSettings: React.FC<AdvancedSettingsProps> = ({
  onClose,
  settings,
  onSettingsChange
}) => {
  const [localSettings, setLocalSettings] = useState<SessionSettings>(settings);
  const [hasChanges, setHasChanges] = useState(false);

  useEffect(() => {
    const hasChanged = JSON.stringify(localSettings) !== JSON.stringify(settings);
    setHasChanges(hasChanged);
  }, [localSettings, settings]);

  const handleSave = () => {
    onSettingsChange(localSettings);
    setHasChanges(false);
  };


  const updateSetting = <K extends keyof SessionSettings>(
    key: K,
    value: SessionSettings[K]
  ) => {
    setLocalSettings(prev => ({ ...prev, [key]: value }));
  };

  const updateShortcut = (action: keyof SessionSettings['shortcuts'], shortcut: string) => {
    setLocalSettings(prev => ({
      ...prev,
      shortcuts: {
        ...prev.shortcuts,
        [action]: shortcut
      }
    }));
  };

  return (
    <div className="h-full flex flex-col max-w-4xl mx-auto">
      {/* Header */}
      <div className="flex items-center justify-between p-6 border-b">
        <div>
          <h2 className="text-2xl font-bold">Advanced Settings</h2>
          <p className="text-sm text-muted-foreground mt-1">
            Customize your Claude Code Session experience
          </p>
        </div>
        <div className="flex items-center gap-2">
          {hasChanges && (
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              className="flex items-center gap-2"
            >
              <Button
                variant="outline"
                size="sm"
                onClick={() => setLocalSettings(settings)}
              >
                Reset
              </Button>
              <Button
                size="sm"
                onClick={handleSave}
                className="bg-primary hover:bg-primary/90"
              >
                <Save className="h-4 w-4 mr-2" />
                Save Changes
              </Button>
            </motion.div>
          )}
          <Button variant="ghost" size="sm" onClick={onClose}>
            Close
          </Button>
        </div>
      </div>

      {/* Settings Content */}
      <div className="flex-1 overflow-y-auto p-6">
        <Tabs defaultValue="general" className="w-full">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="general" className="flex items-center gap-2">
              <Settings className="h-4 w-4" />
              General
            </TabsTrigger>
            <TabsTrigger value="appearance" className="flex items-center gap-2">
              <Palette className="h-4 w-4" />
              Appearance
            </TabsTrigger>
            <TabsTrigger value="behavior" className="flex items-center gap-2">
              <Zap className="h-4 w-4" />
              Behavior
            </TabsTrigger>
            <TabsTrigger value="export" className="flex items-center gap-2">
              <Download className="h-4 w-4" />
              Export
            </TabsTrigger>
            <TabsTrigger value="shortcuts" className="flex items-center gap-2">
              <Keyboard className="h-4 w-4" />
              Shortcuts
            </TabsTrigger>
          </TabsList>

          {/* General Settings */}
          <TabsContent value="general" className="space-y-6 mt-6">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Model & Performance</h3>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Default Model</Label>
                  <Select
                    value={localSettings.defaultModel}
                    onValueChange={(value: 'sonnet' | 'opus') => updateSetting('defaultModel', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="sonnet">
                        <div className="flex items-center gap-2">
                          <Zap className="h-4 w-4" />
                          Claude 4 Sonnet (Faster)
                        </div>
                      </SelectItem>
                      <SelectItem value="opus">
                        <div className="flex items-center gap-2">
                          <Brain className="h-4 w-4" />
                          Claude 4 Opus (More Capable)
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label>Max Tokens per Request</Label>
                  <Input
                    type="number"
                    value={localSettings.maxTokensPerRequest}
                    onChange={(e) => updateSetting('maxTokensPerRequest', parseInt(e.target.value))}
                    min={1024}
                    max={8192}
                  />
                </div>
              </div>
            </div>

            <Separator />

            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Auto-Save</h3>
              
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Enable Auto-Save</Label>
                  <p className="text-sm text-muted-foreground">
                    Automatically save session progress
                  </p>
                </div>
                <Switch
                  checked={localSettings.autoSave}
                  onCheckedChange={(checked) => updateSetting('autoSave', checked)}
                />
              </div>
              
              {localSettings.autoSave && (
                <div className="space-y-2">
                  <Label>Auto-Save Interval (seconds)</Label>
                  <div className="px-3">
                    <Slider
                      value={[localSettings.autoSaveInterval]}
                      onValueChange={([value]: number[]) => updateSetting('autoSaveInterval', value)}
                      max={300}
                      min={10}
                      step={10}
                      className="w-full"
                    />
                    <div className="flex justify-between text-sm text-muted-foreground mt-1">
                      <span>10s</span>
                      <span>{localSettings.autoSaveInterval}s</span>
                      <span>5m</span>
                    </div>
                  </div>
                </div>
              )}
            </div>

            <Separator />

            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Advanced</h3>
              
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Performance Mode</Label>
                    <p className="text-sm text-muted-foreground">
                      Optimize for speed over visual effects
                    </p>
                  </div>
                  <Switch
                    checked={localSettings.performanceMode}
                    onCheckedChange={(checked) => updateSetting('performanceMode', checked)}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Debug Mode</Label>
                    <p className="text-sm text-muted-foreground">
                      Show additional debugging information
                    </p>
                  </div>
                  <Switch
                    checked={localSettings.debugMode}
                    onCheckedChange={(checked) => updateSetting('debugMode', checked)}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Experimental Features</Label>
                    <p className="text-sm text-muted-foreground">
                      Enable beta features (may be unstable)
                    </p>
                  </div>
                  <Switch
                    checked={localSettings.enableExperimentalFeatures}
                    onCheckedChange={(checked) => updateSetting('enableExperimentalFeatures', checked)}
                  />
                </div>
              </div>
            </div>
          </TabsContent>

          {/* Appearance Settings */}
          <TabsContent value="appearance" className="space-y-6 mt-6">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Theme</h3>
              
              <div className="grid grid-cols-3 gap-3">
                {[
                  { value: 'light', label: 'Light', icon: Sun },
                  { value: 'dark', label: 'Dark', icon: Moon },
                  { value: 'system', label: 'System', icon: Monitor }
                ].map(({ value, label, icon: Icon }) => (
                  <button
                    key={value}
                    onClick={() => updateSetting('theme', value as any)}
                    className={cn(
                      "flex flex-col items-center gap-2 p-4 border rounded-lg transition-colors",
                      localSettings.theme === value
                        ? "border-primary bg-primary/5"
                        : "border-border hover:bg-muted/50"
                    )}
                  >
                    <Icon className="h-5 w-5" />
                    <span className="text-sm font-medium">{label}</span>
                  </button>
                ))}
              </div>
            </div>

            <Separator />

            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Typography</h3>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Font Size</Label>
                  <div className="px-3">
                    <Slider
                      value={[localSettings.fontSize]}
                      onValueChange={([value]: number[]) => updateSetting('fontSize', value)}
                      max={20}
                      min={10}
                      step={1}
                      className="w-full"
                    />
                    <div className="flex justify-between text-sm text-muted-foreground mt-1">
                      <span>10px</span>
                      <span>{localSettings.fontSize}px</span>
                      <span>20px</span>
                    </div>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label>Font Family</Label>
                  <Select
                    value={localSettings.fontFamily}
                    onValueChange={(value) => updateSetting('fontFamily', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Inter">Inter</SelectItem>
                      <SelectItem value="SF Pro">SF Pro</SelectItem>
                      <SelectItem value="Roboto">Roboto</SelectItem>
                      <SelectItem value="JetBrains Mono">JetBrains Mono</SelectItem>
                      <SelectItem value="Fira Code">Fira Code</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            <Separator />

            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Layout</h3>
              
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Compact Mode</Label>
                    <p className="text-sm text-muted-foreground">
                      Reduce spacing and padding
                    </p>
                  </div>
                  <Switch
                    checked={localSettings.compactMode}
                    onCheckedChange={(checked) => updateSetting('compactMode', checked)}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Show Line Numbers</Label>
                    <p className="text-sm text-muted-foreground">
                      Display line numbers in code blocks
                    </p>
                  </div>
                  <Switch
                    checked={localSettings.showLineNumbers}
                    onCheckedChange={(checked) => updateSetting('showLineNumbers', checked)}
                  />
                </div>
              </div>
            </div>
          </TabsContent>

          {/* Behavior Settings */}
          <TabsContent value="behavior" className="space-y-6 mt-6">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Notifications</h3>
              
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Enable Notifications</Label>
                    <p className="text-sm text-muted-foreground">
                      Show system notifications for responses
                    </p>
                  </div>
                  <Switch
                    checked={localSettings.enableNotifications}
                    onCheckedChange={(checked) => updateSetting('enableNotifications', checked)}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Sound Effects</Label>
                    <p className="text-sm text-muted-foreground">
                      Play sounds for notifications
                    </p>
                  </div>
                  <Switch
                    checked={localSettings.soundEnabled}
                    onCheckedChange={(checked) => updateSetting('soundEnabled', checked)}
                  />
                </div>
              </div>
            </div>

            <Separator />

            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Session Behavior</h3>
              
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Auto Scroll</Label>
                    <p className="text-sm text-muted-foreground">
                      Automatically scroll to new messages
                    </p>
                  </div>
                  <Switch
                    checked={localSettings.autoScroll}
                    onCheckedChange={(checked) => updateSetting('autoScroll', checked)}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Confirm Before Exit</Label>
                    <p className="text-sm text-muted-foreground">
                      Ask for confirmation before closing session
                    </p>
                  </div>
                  <Switch
                    checked={localSettings.confirmBeforeExit}
                    onCheckedChange={(checked) => updateSetting('confirmBeforeExit', checked)}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Save Session History</Label>
                    <p className="text-sm text-muted-foreground">
                      Automatically save conversation history
                    </p>
                  </div>
                  <Switch
                    checked={localSettings.saveSessionHistory}
                    onCheckedChange={(checked) => updateSetting('saveSessionHistory', checked)}
                  />
                </div>
              </div>
            </div>
          </TabsContent>

          {/* Export Settings */}
          <TabsContent value="export" className="space-y-6 mt-6">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Default Export Format</h3>
              
              <div className="grid grid-cols-2 gap-3">
                {[
                  { value: 'markdown', label: 'Markdown', desc: 'Plain text with formatting' },
                  { value: 'html', label: 'HTML', desc: 'Web page format' },
                  { value: 'pdf', label: 'PDF', desc: 'Printable document' },
                  { value: 'json', label: 'JSON', desc: 'Raw data format' }
                ].map(({ value, label, desc }) => (
                  <button
                    key={value}
                    onClick={() => updateSetting('defaultExportFormat', value as any)}
                    className={cn(
                      "flex flex-col items-start gap-1 p-3 border rounded-lg transition-colors text-left",
                      localSettings.defaultExportFormat === value
                        ? "border-primary bg-primary/5"
                        : "border-border hover:bg-muted/50"
                    )}
                  >
                    <span className="font-medium">{label}</span>
                    <span className="text-xs text-muted-foreground">{desc}</span>
                  </button>
                ))}
              </div>
            </div>

            <Separator />

            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Export Options</h3>
              
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Include Timestamps</Label>
                    <p className="text-sm text-muted-foreground">
                      Add timestamps to exported messages
                    </p>
                  </div>
                  <Switch
                    checked={localSettings.includeTimestamps}
                    onCheckedChange={(checked) => updateSetting('includeTimestamps', checked)}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Include Token Counts</Label>
                    <p className="text-sm text-muted-foreground">
                      Show token usage in exported content
                    </p>
                  </div>
                  <Switch
                    checked={localSettings.includeTokenCounts}
                    onCheckedChange={(checked) => updateSetting('includeTokenCounts', checked)}
                  />
                </div>
              </div>
            </div>
          </TabsContent>

          {/* Keyboard Shortcuts */}
          <TabsContent value="shortcuts" className="space-y-6 mt-6">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Keyboard Shortcuts</h3>
              
              <div className="space-y-3">
                {Object.entries(localSettings.shortcuts).map(([action, shortcut]) => (
                  <div key={action} className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label className="capitalize">
                        {action.replace(/([A-Z])/g, ' $1').trim()}
                      </Label>
                    </div>
                    <Input
                      value={shortcut}
                      onChange={(e) => updateShortcut(action as any, e.target.value)}
                      className="w-32 text-center font-mono"
                      placeholder="Cmd+Key"
                    />
                  </div>
                ))}
              </div>
              
              <div className="text-xs text-muted-foreground bg-muted/50 rounded p-3">
                <strong>Tip:</strong> Use standard modifier keys like Cmd, Ctrl, Alt, Shift followed by a letter or symbol.
                Examples: Cmd+Enter, Ctrl+S, Alt+T
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};