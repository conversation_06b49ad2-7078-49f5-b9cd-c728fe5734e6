import React, { useState, useMemo } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Bookmark,
  BookmarkPlus,
  Search,
  Tag,
  Calendar,
  User,
  Bot,
  Star,
  Heart,
  Flag,
  Lightbulb,
  Code,
  FileText,
  Trash2,
  Edit3,
  ExternalLink,
  Hash,
  Copy
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";

interface BookmarkedMessage {
  id: string;
  messageId: string;
  title: string;
  content: string;
  excerpt: string;
  role: 'user' | 'assistant';
  timestamp: Date;
  bookmarkedAt: Date;
  tags: string[];
  category: BookmarkCategory;
  notes?: string;
  color?: string;
  priority: 'low' | 'medium' | 'high';
}

type BookmarkCategory = 'important' | 'reference' | 'code' | 'idea' | 'question' | 'solution' | 'custom';

interface BookmarkManagerProps {
  bookmarks: BookmarkedMessage[];
  onAddBookmark: (bookmark: Omit<BookmarkedMessage, 'id' | 'bookmarkedAt'>) => void;
  onUpdateBookmark: (id: string, updates: Partial<BookmarkedMessage>) => void;
  onDeleteBookmark: (id: string) => void;
  onJumpToMessage: (messageId: string) => void;
  className?: string;
}

const BOOKMARK_CATEGORIES: { value: BookmarkCategory; label: string; icon: React.ComponentType<{ className?: string }>; color: string }[] = [
  { value: 'important', label: 'Important', icon: Star, color: 'text-yellow-600' },
  { value: 'reference', label: 'Reference', icon: FileText, color: 'text-blue-600' },
  { value: 'code', label: 'Code', icon: Code, color: 'text-green-600' },
  { value: 'idea', label: 'Idea', icon: Lightbulb, color: 'text-purple-600' },
  { value: 'question', label: 'Question', icon: Flag, color: 'text-orange-600' },
  { value: 'solution', label: 'Solution', icon: Heart, color: 'text-red-600' },
  { value: 'custom', label: 'Custom', icon: Tag, color: 'text-gray-600' }
];

const PRIORITY_COLORS = {
  low: 'border-l-gray-400',
  medium: 'border-l-yellow-400',
  high: 'border-l-red-400'
};

export const BookmarkManager: React.FC<BookmarkManagerProps> = ({
  bookmarks,
  onAddBookmark,
  onUpdateBookmark,
  onDeleteBookmark,
  onJumpToMessage,
  className
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<BookmarkCategory | 'all'>('all');
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [sortBy, setSortBy] = useState<'recent' | 'oldest' | 'title' | 'priority'>('recent');
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [editingBookmark, setEditingBookmark] = useState<BookmarkedMessage | null>(null);
  const [newBookmark, setNewBookmark] = useState<Partial<BookmarkedMessage>>({
    title: '',
    notes: '',
    tags: [],
    category: 'important',
    priority: 'medium'
  });

  // Get all unique tags
  const allTags = useMemo(() => {
    const tags = new Set<string>();
    bookmarks.forEach(bookmark => {
      bookmark.tags.forEach(tag => tags.add(tag));
    });
    return Array.from(tags).sort();
  }, [bookmarks]);

  // Filter and sort bookmarks
  const filteredBookmarks = useMemo(() => {
    let filtered = bookmarks;

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(bookmark => 
        bookmark.title.toLowerCase().includes(query) ||
        bookmark.content.toLowerCase().includes(query) ||
        bookmark.notes?.toLowerCase().includes(query) ||
        bookmark.tags.some(tag => tag.toLowerCase().includes(query))
      );
    }

    // Apply category filter
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(bookmark => bookmark.category === selectedCategory);
    }

    // Apply tags filter
    if (selectedTags.length > 0) {
      filtered = filtered.filter(bookmark => 
        selectedTags.every(tag => bookmark.tags.includes(tag))
      );
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'recent':
          return b.bookmarkedAt.getTime() - a.bookmarkedAt.getTime();
        case 'oldest':
          return a.bookmarkedAt.getTime() - b.bookmarkedAt.getTime();
        case 'title':
          return a.title.localeCompare(b.title);
        case 'priority':
          const priorityOrder = { high: 3, medium: 2, low: 1 };
          return priorityOrder[b.priority] - priorityOrder[a.priority];
        default:
          return 0;
      }
    });

    return filtered;
  }, [bookmarks, searchQuery, selectedCategory, selectedTags, sortBy]);

  const handleAddBookmark = () => {
    if (newBookmark.title && newBookmark.content) {
      onAddBookmark({
        ...newBookmark,
        messageId: newBookmark.messageId || '',
        content: newBookmark.content || '',
        excerpt: newBookmark.content?.slice(0, 150) + '...' || '',
        role: newBookmark.role || 'assistant',
        timestamp: new Date(),
        tags: newBookmark.tags || [],
        category: newBookmark.category || 'important',
        priority: newBookmark.priority || 'medium'
      } as Omit<BookmarkedMessage, 'id' | 'bookmarkedAt'>);
      
      setNewBookmark({
        title: '',
        notes: '',
        tags: [],
        category: 'important',
        priority: 'medium'
      });
      setIsAddDialogOpen(false);
    }
  };

  const handleUpdateBookmark = (bookmark: BookmarkedMessage) => {
    if (editingBookmark) {
      onUpdateBookmark(editingBookmark.id, {
        title: bookmark.title,
        notes: bookmark.notes,
        tags: bookmark.tags,
        category: bookmark.category,
        priority: bookmark.priority
      });
      setEditingBookmark(null);
    }
  };

  const toggleTag = (tag: string) => {
    setSelectedTags(prev => 
      prev.includes(tag) 
        ? prev.filter(t => t !== tag)
        : [...prev, tag]
    );
  };


  const getCategoryIcon = (category: BookmarkCategory) => {
    const categoryData = BOOKMARK_CATEGORIES.find(c => c.value === category);
    return categoryData?.icon || Tag;
  };

  const getCategoryColor = (category: BookmarkCategory) => {
    const categoryData = BOOKMARK_CATEGORIES.find(c => c.value === category);
    return categoryData?.color || 'text-gray-600';
  };

  const BookmarkCard: React.FC<{ bookmark: BookmarkedMessage }> = ({ bookmark }) => {
    const CategoryIcon = getCategoryIcon(bookmark.category);
    
    return (
      <motion.div
        layout
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        className={cn(
          "group relative",
          PRIORITY_COLORS[bookmark.priority]
        )}
      >
        <Card className="border-l-4 hover:shadow-md transition-shadow">
          <CardHeader className="pb-3">
            <div className="flex items-start justify-between">
              <div className="flex items-start gap-3 flex-1">
                <div className={cn("p-2 rounded-lg bg-muted", getCategoryColor(bookmark.category))}>
                  <CategoryIcon className="h-4 w-4" />
                </div>
                <div className="flex-1 min-w-0">
                  <h3 className="font-semibold text-sm leading-tight mb-1 truncate">
                    {bookmark.title}
                  </h3>
                  <div className="flex items-center gap-2 text-xs text-muted-foreground mb-2">
                    {bookmark.role === 'user' ? (
                      <User className="h-3 w-3" />
                    ) : (
                      <Bot className="h-3 w-3" />
                    )}
                    <span>{bookmark.role}</span>
                    <span>•</span>
                    <Calendar className="h-3 w-3" />
                    <span>{bookmark.bookmarkedAt.toLocaleDateString()}</span>
                  </div>
                  <p className="text-xs text-muted-foreground line-clamp-2">
                    {bookmark.excerpt}
                  </p>
                </div>
              </div>
              
              <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onJumpToMessage(bookmark.messageId)}
                      className="h-8 w-8 p-0"
                    >
                      <ExternalLink className="h-3 w-3" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Jump to message</TooltipContent>
                </Tooltip>
                
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setEditingBookmark(bookmark)}
                      className="h-8 w-8 p-0"
                    >
                      <Edit3 className="h-3 w-3" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Edit bookmark</TooltipContent>
                </Tooltip>
                
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => navigator.clipboard.writeText(bookmark.content)}
                      className="h-8 w-8 p-0"
                    >
                      <Copy className="h-3 w-3" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Copy content</TooltipContent>
                </Tooltip>
                
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onDeleteBookmark(bookmark.id)}
                      className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Delete bookmark</TooltipContent>
                </Tooltip>
              </div>
            </div>
          </CardHeader>
          
          {(bookmark.tags.length > 0 || bookmark.notes) && (
            <CardContent className="pt-0">
              {bookmark.tags.length > 0 && (
                <div className="flex flex-wrap gap-1 mb-2">
                  {bookmark.tags.map(tag => (
                    <Badge key={tag} variant="secondary" className="text-xs">
                      <Hash className="h-2 w-2 mr-1" />
                      {tag}
                    </Badge>
                  ))}
                </div>
              )}
              
              {bookmark.notes && (
                <div className="text-xs text-muted-foreground bg-muted/50 rounded p-2">
                  {bookmark.notes}
                </div>
              )}
            </CardContent>
          )}
        </Card>
      </motion.div>
    );
  };

  return (
    <div className={cn("space-y-4", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Bookmark className="h-5 w-5" />
          <h2 className="text-xl font-semibold">Bookmarks</h2>
          <Badge variant="secondary">{bookmarks.length}</Badge>
        </div>
        
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button size="sm">
              <BookmarkPlus className="h-4 w-4 mr-2" />
              Add Bookmark
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Add New Bookmark</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>Title</Label>
                <Input
                  value={newBookmark.title || ''}
                  onChange={(e) => setNewBookmark(prev => ({ ...prev, title: e.target.value }))}
                  placeholder="Bookmark title..."
                />
              </div>
              
              <div className="space-y-2">
                <Label>Content</Label>
                <Textarea
                  value={newBookmark.content || ''}
                  onChange={(e) => setNewBookmark(prev => ({ ...prev, content: e.target.value }))}
                  placeholder="Message content..."
                  rows={3}
                />
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Category</Label>
                  <Select
                    value={newBookmark.category}
                    onValueChange={(value: BookmarkCategory) => 
                      setNewBookmark(prev => ({ ...prev, category: value }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {BOOKMARK_CATEGORIES.map(category => {
                        const Icon = category.icon;
                        return (
                          <SelectItem key={category.value} value={category.value}>
                            <div className="flex items-center gap-2">
                              <Icon className={cn("h-4 w-4", category.color)} />
                              {category.label}
                            </div>
                          </SelectItem>
                        );
                      })}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label>Priority</Label>
                  <Select
                    value={newBookmark.priority}
                    onValueChange={(value: 'low' | 'medium' | 'high') => 
                      setNewBookmark(prev => ({ ...prev, priority: value }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="low">Low</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="high">High</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <div className="space-y-2">
                <Label>Notes (Optional)</Label>
                <Textarea
                  value={newBookmark.notes || ''}
                  onChange={(e) => setNewBookmark(prev => ({ ...prev, notes: e.target.value }))}
                  placeholder="Additional notes..."
                  rows={2}
                />
              </div>
              
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleAddBookmark}>
                  Add Bookmark
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Search and Filters */}
      <div className="space-y-3">
        <div className="flex gap-2">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search bookmarks..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <Select value={selectedCategory} onValueChange={(value: any) => setSelectedCategory(value)}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              {BOOKMARK_CATEGORIES.map(category => (
                <SelectItem key={category.value} value={category.value}>
                  {category.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          
          <Select value={sortBy} onValueChange={(value: any) => setSortBy(value)}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="recent">Recent</SelectItem>
              <SelectItem value="oldest">Oldest</SelectItem>
              <SelectItem value="title">Title</SelectItem>
              <SelectItem value="priority">Priority</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        {/* Tags Filter */}
        {allTags.length > 0 && (
          <div className="flex flex-wrap gap-2">
            <span className="text-sm text-muted-foreground self-center">Tags:</span>
            {allTags.map(tag => (
              <button
                key={tag}
                onClick={() => toggleTag(tag)}
                className={cn(
                  "inline-flex items-center gap-1 px-2 py-1 rounded-md text-xs transition-colors",
                  selectedTags.includes(tag)
                    ? "bg-primary text-primary-foreground"
                    : "bg-muted hover:bg-muted/80"
                )}
              >
                <Hash className="h-2 w-2" />
                {tag}
              </button>
            ))}
          </div>
        )}
      </div>

      {/* Results */}
      <div className="space-y-3">
        <div className="text-sm text-muted-foreground">
          {filteredBookmarks.length === 0 ? (
            "No bookmarks match your criteria"
          ) : filteredBookmarks.length === bookmarks.length ? (
            `Showing all ${bookmarks.length} bookmarks`
          ) : (
            `Found ${filteredBookmarks.length} of ${bookmarks.length} bookmarks`
          )}
        </div>
        
        <AnimatePresence mode="popLayout">
          {filteredBookmarks.length === 0 ? (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="text-center py-12 text-muted-foreground"
            >
              <Bookmark className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No bookmarks found</p>
              <p className="text-sm mt-1">Try adjusting your search or filters</p>
            </motion.div>
          ) : (
            <div className="grid gap-3">
              {filteredBookmarks.map(bookmark => (
                <BookmarkCard key={bookmark.id} bookmark={bookmark} />
              ))}
            </div>
          )}
        </AnimatePresence>
      </div>

      {/* Edit Dialog */}
      <Dialog open={!!editingBookmark} onOpenChange={() => setEditingBookmark(null)}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Edit Bookmark</DialogTitle>
          </DialogHeader>
          {editingBookmark && (
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>Title</Label>
                <Input
                  value={editingBookmark.title}
                  onChange={(e) => setEditingBookmark(prev => prev ? { ...prev, title: e.target.value } : null)}
                />
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Category</Label>
                  <Select
                    value={editingBookmark.category}
                    onValueChange={(value: BookmarkCategory) => 
                      setEditingBookmark(prev => prev ? { ...prev, category: value } : null)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {BOOKMARK_CATEGORIES.map(category => {
                        const Icon = category.icon;
                        return (
                          <SelectItem key={category.value} value={category.value}>
                            <div className="flex items-center gap-2">
                              <Icon className={cn("h-4 w-4", category.color)} />
                              {category.label}
                            </div>
                          </SelectItem>
                        );
                      })}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label>Priority</Label>
                  <Select
                    value={editingBookmark.priority}
                    onValueChange={(value: 'low' | 'medium' | 'high') => 
                      setEditingBookmark(prev => prev ? { ...prev, priority: value } : null)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="low">Low</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="high">High</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <div className="space-y-2">
                <Label>Notes</Label>
                <Textarea
                  value={editingBookmark.notes || ''}
                  onChange={(e) => setEditingBookmark(prev => prev ? { ...prev, notes: e.target.value } : null)}
                  rows={3}
                />
              </div>
              
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setEditingBookmark(null)}>
                  Cancel
                </Button>
                <Button onClick={() => handleUpdateBookmark(editingBookmark)}>
                  Save Changes
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};