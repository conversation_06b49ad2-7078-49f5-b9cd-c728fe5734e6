import React, { useState, useMemo } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  GitBranch,
  GitCommit,
  GitMerge,
  Plus,
  Minus,
  Copy,
  Download,
  Maximize2,
  Minimize2,
  Settings,
  FileText,
  Code,
  ChevronDown,
  ChevronRight,
  Search,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Popover } from "@/components/ui/popover";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";

interface DiffLine {
  type: 'added' | 'removed' | 'unchanged' | 'modified';
  oldLineNumber?: number;
  newLineNumber?: number;
  content: string;
  highlight?: boolean;
}

interface FileDiff {
  filename: string;
  language: string;
  oldContent: string;
  newContent: string;
  lines: DiffLine[];
  stats: {
    additions: number;
    deletions: number;
    modifications: number;
  };
}

interface CodeDiffViewerProps {
  diffs: FileDiff[];
  title?: string;
  onExport?: (format: 'patch' | 'unified' | 'html') => void;
  className?: string;
}

interface DiffSettings {
  showLineNumbers: boolean;
  showWhitespace: boolean;
  contextLines: number;
  splitView: boolean;
  highlightSyntax: boolean;
  wordWrap: boolean;
  fontSize: number;
}

const DEFAULT_SETTINGS: DiffSettings = {
  showLineNumbers: true,
  showWhitespace: false,
  contextLines: 3,
  splitView: true,
  highlightSyntax: true,
  wordWrap: false,
  fontSize: 14
};

export const CodeDiffViewer: React.FC<CodeDiffViewerProps> = ({
  diffs,
  title = "Code Diff",
  onExport,
  className
}) => {
  const [settings, setSettings] = useState<DiffSettings>(DEFAULT_SETTINGS);
  const [selectedFile, setSelectedFile] = useState<string>(diffs[0]?.filename || '');
  const [searchQuery, setSearchQuery] = useState('');
  const [expandedFiles, setExpandedFiles] = useState<Set<string>>(new Set([diffs[0]?.filename]));
  const [isFullscreen, setIsFullscreen] = useState(false);

  const currentDiff = useMemo(() => {
    return diffs.find(diff => diff.filename === selectedFile);
  }, [diffs, selectedFile]);

  const totalStats = useMemo(() => {
    return diffs.reduce(
      (acc, diff) => ({
        additions: acc.additions + diff.stats.additions,
        deletions: acc.deletions + diff.stats.deletions,
        modifications: acc.modifications + diff.stats.modifications,
        files: acc.files + 1
      }),
      { additions: 0, deletions: 0, modifications: 0, files: 0 }
    );
  }, [diffs]);

  const filteredLines = useMemo(() => {
    if (!currentDiff || !searchQuery.trim()) {
      return currentDiff?.lines || [];
    }

    const query = searchQuery.toLowerCase();
    return currentDiff.lines.filter(line => 
      line.content.toLowerCase().includes(query)
    );
  }, [currentDiff, searchQuery]);

  const updateSetting = <K extends keyof DiffSettings>(
    key: K,
    value: DiffSettings[K]
  ) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };

  const toggleFileExpansion = (filename: string) => {
    setExpandedFiles(prev => {
      const newSet = new Set(prev);
      if (newSet.has(filename)) {
        newSet.delete(filename);
      } else {
        newSet.add(filename);
      }
      return newSet;
    });
  };

  const getLineTypeColor = (type: DiffLine['type']) => {
    switch (type) {
      case 'added':
        return 'bg-green-50 border-l-green-500 dark:bg-green-950/20';
      case 'removed':
        return 'bg-red-50 border-l-red-500 dark:bg-red-950/20';
      case 'modified':
        return 'bg-yellow-50 border-l-yellow-500 dark:bg-yellow-950/20';
      default:
        return 'bg-background';
    }
  };

  const getLineTypeIcon = (type: DiffLine['type']) => {
    switch (type) {
      case 'added':
        return <Plus className="h-3 w-3 text-green-600" />;
      case 'removed':
        return <Minus className="h-3 w-3 text-red-600" />;
      case 'modified':
        return <GitCommit className="h-3 w-3 text-yellow-600" />;
      default:
        return null;
    }
  };

  const getLanguageIcon = (language: string) => {
    // Simple language detection for icons
    if (['javascript', 'typescript', 'jsx', 'tsx'].includes(language.toLowerCase())) {
      return '🟨';
    } else if (['python', 'py'].includes(language.toLowerCase())) {
      return '🐍';
    } else if (['java'].includes(language.toLowerCase())) {
      return '☕';
    } else if (['html', 'xml'].includes(language.toLowerCase())) {
      return '🌐';
    } else if (['css', 'scss', 'sass'].includes(language.toLowerCase())) {
      return '🎨';
    } else if (['json'].includes(language.toLowerCase())) {
      return '📋';
    } else if (['markdown', 'md'].includes(language.toLowerCase())) {
      return '📝';
    }
    return '📄';
  };

  const DiffLineComponent: React.FC<{ line: DiffLine; index: number }> = ({ line, index }) => {
    const isHighlighted = searchQuery && line.content.toLowerCase().includes(searchQuery.toLowerCase());
    
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: index * 0.01 }}
        className={cn(
          "flex items-center border-l-2 transition-colors",
          getLineTypeColor(line.type),
          isHighlighted && "ring-2 ring-blue-500/50",
          "hover:bg-muted/50"
        )}
        style={{ fontSize: `${settings.fontSize}px` }}
      >
        {/* Line Numbers */}
        {settings.showLineNumbers && (
          <div className="flex-shrink-0 w-20 px-2 py-1 text-xs text-muted-foreground bg-muted/30 border-r">
            <div className="flex justify-between">
              <span>{line.oldLineNumber || ''}</span>
              <span>{line.newLineNumber || ''}</span>
            </div>
          </div>
        )}
        
        {/* Change Type Icon */}
        <div className="flex-shrink-0 w-8 flex items-center justify-center">
          {getLineTypeIcon(line.type)}
        </div>
        
        {/* Content */}
        <div className={cn(
          "flex-1 px-3 py-1 font-mono text-sm",
          settings.wordWrap ? "whitespace-pre-wrap" : "whitespace-pre overflow-x-auto"
        )}>
          {settings.showWhitespace 
            ? line.content.replace(/ /g, '·').replace(/\t/g, '→')
            : line.content
          }
        </div>
        
        {/* Copy Button */}
        <div className="flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity">
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigator.clipboard.writeText(line.content)}
                className="h-6 w-6 p-0"
              >
                <Copy className="h-3 w-3" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>Copy line</TooltipContent>
          </Tooltip>
        </div>
      </motion.div>
    );
  };

  const FileHeader: React.FC<{ diff: FileDiff }> = ({ diff }) => (
    <div className="flex items-center justify-between p-3 bg-muted/50 border-b">
      <div className="flex items-center gap-3">
        <button
          onClick={() => toggleFileExpansion(diff.filename)}
          className="flex items-center gap-2 hover:bg-muted rounded p-1"
        >
          {expandedFiles.has(diff.filename) ? (
            <ChevronDown className="h-4 w-4" />
          ) : (
            <ChevronRight className="h-4 w-4" />
          )}
          <span className="text-lg">{getLanguageIcon(diff.language)}</span>
          <span className="font-mono text-sm font-medium">{diff.filename}</span>
        </button>
        
        <Badge variant="secondary" className="text-xs">
          {diff.language}
        </Badge>
      </div>
      
      <div className="flex items-center gap-2">
        {diff.stats.additions > 0 && (
          <Badge variant="secondary" className="text-green-600">
            +{diff.stats.additions}
          </Badge>
        )}
        {diff.stats.deletions > 0 && (
          <Badge variant="secondary" className="text-red-600">
            -{diff.stats.deletions}
          </Badge>
        )}
        {diff.stats.modifications > 0 && (
          <Badge variant="secondary" className="text-yellow-600">
            ~{diff.stats.modifications}
          </Badge>
        )}
      </div>
    </div>
  );

  return (
    <div className={cn(
      "flex flex-col h-full",
      isFullscreen && "fixed inset-0 z-50 bg-background",
      className
    )}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center gap-3">
          <GitBranch className="h-5 w-5" />
          <h2 className="text-xl font-semibold">{title}</h2>
          <Badge variant="secondary">
            {totalStats.files} file{totalStats.files !== 1 ? 's' : ''}
          </Badge>
        </div>
        
        <div className="flex items-center gap-2">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-3 w-3 text-muted-foreground" />
            <Input
              placeholder="Search in diff..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-7 w-48 h-8 text-xs"
            />
          </div>
          
          {/* Settings */}
          <Popover
            trigger={
              <Button variant="outline" size="sm">
                <Settings className="h-4 w-4" />
              </Button>
            }
            content={
              <div className="space-y-4">
                <h4 className="font-medium">Diff Settings</h4>
                
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <Label className="text-sm">Show Line Numbers</Label>
                    <Switch
                      checked={settings.showLineNumbers}
                      onCheckedChange={(checked) => updateSetting('showLineNumbers', checked)}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Label className="text-sm">Show Whitespace</Label>
                    <Switch
                      checked={settings.showWhitespace}
                      onCheckedChange={(checked) => updateSetting('showWhitespace', checked)}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Label className="text-sm">Split View</Label>
                    <Switch
                      checked={settings.splitView}
                      onCheckedChange={(checked) => updateSetting('splitView', checked)}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Label className="text-sm">Word Wrap</Label>
                    <Switch
                      checked={settings.wordWrap}
                      onCheckedChange={(checked) => updateSetting('wordWrap', checked)}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label className="text-sm">Font Size</Label>
                    <Select
                      value={settings.fontSize.toString()}
                      onValueChange={(value) => updateSetting('fontSize', parseInt(value))}
                    >
                      <SelectTrigger className="h-8">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="12">12px</SelectItem>
                        <SelectItem value="14">14px</SelectItem>
                        <SelectItem value="16">16px</SelectItem>
                        <SelectItem value="18">18px</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
            }
            className="w-80"
            align="end"
          />
          
          {/* Export */}
          {onExport && (
            <Popover
              trigger={
                <Button variant="outline" size="sm">
                  <Download className="h-4 w-4" />
                </Button>
              }
              content={
                <div className="space-y-2">
                  <h4 className="font-medium text-sm">Export Format</h4>
                  <div className="space-y-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onExport('patch')}
                      className="w-full justify-start"
                    >
                      <FileText className="h-4 w-4 mr-2" />
                      Patch File
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onExport('unified')}
                      className="w-full justify-start"
                    >
                      <Code className="h-4 w-4 mr-2" />
                      Unified Diff
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onExport('html')}
                      className="w-full justify-start"
                    >
                      <GitMerge className="h-4 w-4 mr-2" />
                      HTML Report
                    </Button>
                  </div>
                </div>
              }
              className="w-48"
              align="end"
            />
          )}
          
          {/* Fullscreen Toggle */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsFullscreen(!isFullscreen)}
          >
            {isFullscreen ? (
              <Minimize2 className="h-4 w-4" />
            ) : (
              <Maximize2 className="h-4 w-4" />
            )}
          </Button>
        </div>
      </div>

      {/* Stats Summary */}
      <div className="flex items-center gap-6 px-4 py-2 bg-muted/30 border-b text-sm">
        <div className="flex items-center gap-2">
          <Plus className="h-4 w-4 text-green-600" />
          <span className="text-green-600 font-medium">{totalStats.additions} additions</span>
        </div>
        <div className="flex items-center gap-2">
          <Minus className="h-4 w-4 text-red-600" />
          <span className="text-red-600 font-medium">{totalStats.deletions} deletions</span>
        </div>
        {totalStats.modifications > 0 && (
          <div className="flex items-center gap-2">
            <GitCommit className="h-4 w-4 text-yellow-600" />
            <span className="text-yellow-600 font-medium">{totalStats.modifications} modifications</span>
          </div>
        )}
      </div>

      {/* Content */}
      <div className="flex-1 flex overflow-hidden">
        {/* File List Sidebar */}
        {diffs.length > 1 && (
          <div className="w-64 border-r bg-muted/20">
            <div className="p-3 border-b">
              <h3 className="font-medium text-sm">Changed Files</h3>
            </div>
            <ScrollArea className="h-full">
              <div className="p-2 space-y-1">
                {diffs.map(diff => (
                  <button
                    key={diff.filename}
                    onClick={() => setSelectedFile(diff.filename)}
                    className={cn(
                      "w-full flex items-center gap-2 p-2 rounded text-left text-sm transition-colors",
                      selectedFile === diff.filename
                        ? "bg-primary text-primary-foreground"
                        : "hover:bg-muted"
                    )}
                  >
                    <span className="text-base">{getLanguageIcon(diff.language)}</span>
                    <span className="flex-1 truncate font-mono">{diff.filename}</span>
                    <div className="flex gap-1">
                      {diff.stats.additions > 0 && (
                        <span className="text-xs text-green-600">+{diff.stats.additions}</span>
                      )}
                      {diff.stats.deletions > 0 && (
                        <span className="text-xs text-red-600">-{diff.stats.deletions}</span>
                      )}
                    </div>
                  </button>
                ))}
              </div>
            </ScrollArea>
          </div>
        )}

        {/* Diff Content */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {currentDiff ? (
            <>
              <FileHeader diff={currentDiff} />
              
              {expandedFiles.has(currentDiff.filename) && (
                <ScrollArea className="flex-1">
                  <div className="group">
                    <AnimatePresence>
                      {filteredLines.map((line, index) => (
                        <DiffLineComponent
                          key={`${line.oldLineNumber}-${line.newLineNumber}-${index}`}
                          line={line}
                          index={index}
                        />
                      ))}
                    </AnimatePresence>
                    
                    {filteredLines.length === 0 && searchQuery && (
                      <div className="text-center py-8 text-muted-foreground">
                        <Search className="h-8 w-8 mx-auto mb-2 opacity-50" />
                        <p>No lines match your search</p>
                      </div>
                    )}
                  </div>
                </ScrollArea>
              )}
            </>
          ) : (
            <div className="flex-1 flex items-center justify-center text-muted-foreground">
              <div className="text-center">
                <GitBranch className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No diff selected</p>
                <p className="text-sm mt-1">Select a file to view changes</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};