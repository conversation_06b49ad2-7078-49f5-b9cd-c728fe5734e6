import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Users,
  Plus,
  Clock,
  CheckCircle,
  AlertCircle,
  Play,
  Trash2,
  Eye,
  MoreHorizontal,
  Brain,
  Shield,
  Zap,
  GraduationCap,
  ArrowRight
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";

import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { cn } from "@/lib/utils";
import { formatISOTimestamp } from "@/lib/date-utils";
import { EnsembleSession } from "./EnsembleSession";

// Ensemble Session Summary for listing
interface EnsembleSessionSummary {
  id: string;
  projectPath: string;
  problem: string;
  status: 'idle' | 'planning' | 'executing' | 'reviewing' | 'completed' | 'error';
  progress: number;
  participantCount: number;
  conversationCount: number;
  artifactCount: number;
  createdAt: string;
  completedAt?: string;
  duration?: number;
}

// Mock data for demonstration (in real implementation, this would come from storage)
const MOCK_ENSEMBLE_SESSIONS: EnsembleSessionSummary[] = [
  {
    id: "ensemble_1703123456789",
    projectPath: "/Users/<USER>/my-app",
    problem: "Design a scalable microservices architecture for an e-commerce platform",
    status: "completed",
    progress: 100,
    participantCount: 4,
    conversationCount: 24,
    artifactCount: 6,
    createdAt: "2024-01-15T10:30:00Z",
    completedAt: "2024-01-15T12:45:00Z",
    duration: 135
  },
  {
    id: "ensemble_1703123456790",
    projectPath: "/Users/<USER>/analytics-dashboard",
    problem: "Optimize database queries and implement caching strategy",
    status: "executing",
    progress: 65,
    participantCount: 4,
    conversationCount: 18,
    artifactCount: 3,
    createdAt: "2024-01-16T09:15:00Z"
  },
  {
    id: "ensemble_1703123456791",
    projectPath: "/Users/<USER>/security-audit",
    problem: "Conduct comprehensive security audit and implement fixes",
    status: "reviewing",
    progress: 85,
    participantCount: 4,
    conversationCount: 31,
    artifactCount: 8,
    createdAt: "2024-01-16T14:20:00Z"
  }
];

interface EnsembleSessionManagerProps {
  projectPath?: string;
  onBack: () => void;
  className?: string;
}

export const EnsembleSessionManager: React.FC<EnsembleSessionManagerProps> = ({
  projectPath,
  onBack,
  className
}) => {
  const [sessions, setSessions] = useState<EnsembleSessionSummary[]>(MOCK_ENSEMBLE_SESSIONS);
  const [selectedSession, setSelectedSession] = useState<string | null>(null);
  const [showNewSession, setShowNewSession] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [filterStatus, setFilterStatus] = useState<string>("all");
  const [isLoading, setIsLoading] = useState(false);

  // Filter sessions based on search and status
  const filteredSessions = sessions.filter(session => {
    const matchesSearch = session.problem.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         session.projectPath.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = filterStatus === "all" || session.status === filterStatus;
    const matchesProject = !projectPath || session.projectPath === projectPath;
    
    return matchesSearch && matchesStatus && matchesProject;
  });

  // Load sessions (in real implementation, this would fetch from storage)
  useEffect(() => {
    const loadSessions = async () => {
      setIsLoading(true);
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      setIsLoading(false);
    };
    
    loadSessions();
  }, [projectPath]);

  // Handle session deletion
  const handleDeleteSession = async (sessionId: string) => {
    setSessions(prev => prev.filter(s => s.id !== sessionId));
  };

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-600 bg-green-50';
      case 'executing': return 'text-blue-600 bg-blue-50';
      case 'reviewing': return 'text-yellow-600 bg-yellow-50';
      case 'error': return 'text-red-600 bg-red-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  // Get status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return CheckCircle;
      case 'executing': return Play;
      case 'reviewing': return Eye;
      case 'error': return AlertCircle;
      default: return Clock;
    }
  };

  // Claude personality icons
  const personalityIcons = {
    architect: Brain,
    reviewer: Shield,
    optimizer: Zap,
    teacher: GraduationCap
  };

  if (selectedSession) {
    const session = sessions.find(s => s.id === selectedSession);
    if (session) {
      return (
        <EnsembleSession
          projectPath={session.projectPath}
          initialProblem={session.problem}
          onBack={() => setSelectedSession(null)}
          className={className}
        />
      );
    }
  }

  if (showNewSession) {
    return (
      <EnsembleSession
        projectPath={projectPath || ""}
        onBack={() => setShowNewSession(false)}
        className={className}
      />
    );
  }

  return (
    <div className={cn("flex flex-col h-full", className)}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center gap-3">
          <Button variant="ghost" size="sm" onClick={onBack}>
            <ArrowRight className="h-4 w-4 rotate-180" />
          </Button>
          <div className="flex items-center gap-2">
            <Users className="h-5 w-5 text-blue-500" />
            <div>
              <h1 className="text-lg font-semibold">Multi-Claude Ensemble Sessions</h1>
              <p className="text-sm text-muted-foreground">
                Collaborative AI problem-solving with multiple Claude personalities
              </p>
            </div>
          </div>
        </div>
        
        <Button onClick={() => setShowNewSession(true)}>
          <Plus className="h-4 w-4 mr-2" />
          New Ensemble Session
        </Button>
      </div>

      {/* Filters and Search */}
      <div className="flex items-center gap-4 p-4 border-b">
        <div className="flex-1">
          <Input
            placeholder="Search sessions by problem or project..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="max-w-md"
          />
        </div>
        
        <div className="flex items-center gap-2">
          <span className="text-sm text-muted-foreground">Status:</span>
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            className="px-3 py-1 border rounded-md text-sm"
          >
            <option value="all">All</option>
            <option value="idle">Idle</option>
            <option value="planning">Planning</option>
            <option value="executing">Executing</option>
            <option value="reviewing">Reviewing</option>
            <option value="completed">Completed</option>
            <option value="error">Error</option>
          </select>
        </div>
      </div>

      {/* Ensemble Overview */}
      <div className="p-4 border-b bg-muted/30">
        <div className="grid grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Brain className="h-4 w-4 text-blue-500" />
                <span className="text-sm font-medium">Architect Claude</span>
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                System design & architecture decisions
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Shield className="h-4 w-4 text-red-500" />
                <span className="text-sm font-medium">Reviewer Claude</span>
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                Code quality & security analysis
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <Zap className="h-4 w-4 text-yellow-500" />
                <span className="text-sm font-medium">Optimizer Claude</span>
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                Performance & efficiency improvements
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-2">
                <GraduationCap className="h-4 w-4 text-green-500" />
                <span className="text-sm font-medium">Teacher Claude</span>
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                Educational explanations & mentorship
              </p>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Sessions List */}
      <div className="flex-1 overflow-hidden">
        <ScrollArea className="h-full">
          <div className="p-4 space-y-4">
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <div className="text-muted-foreground">Loading ensemble sessions...</div>
              </div>
            ) : filteredSessions.length === 0 ? (
              <div className="text-center py-8">
                <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">No ensemble sessions found</h3>
                <p className="text-muted-foreground mb-4">
                  {searchQuery || filterStatus !== "all" 
                    ? "Try adjusting your search or filter criteria"
                    : "Start your first multi-Claude collaboration session"
                  }
                </p>
                <Button onClick={() => setShowNewSession(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Ensemble Session
                </Button>
              </div>
            ) : (
              <AnimatePresence>
                {filteredSessions.map((session) => {
                  const StatusIcon = getStatusIcon(session.status);
                  
                  return (
                    <motion.div
                      key={session.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      transition={{ duration: 0.2 }}
                    >
                      <Card className="hover:shadow-md transition-shadow cursor-pointer">
                        <CardHeader className="pb-3">
                          <div className="flex items-start justify-between">
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center gap-2 mb-2">
                                <StatusIcon className="h-4 w-4" />
                                <Badge className={cn("text-xs", getStatusColor(session.status))}>
                                  {session.status}
                                </Badge>
                                {session.progress > 0 && (
                                  <span className="text-xs text-muted-foreground">
                                    {Math.round(session.progress)}% complete
                                  </span>
                                )}
                              </div>
                              
                              <CardTitle className="text-base mb-1 line-clamp-2">
                                {session.problem}
                              </CardTitle>
                              
                              <p className="text-sm text-muted-foreground">
                                {session.projectPath}
                              </p>
                            </div>
                            
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem onClick={() => setSelectedSession(session.id)}>
                                  <Eye className="h-4 w-4 mr-2" />
                                  View Session
                                </DropdownMenuItem>
                                <DropdownMenuItem 
                                  onClick={() => handleDeleteSession(session.id)}
                                  className="text-red-600"
                                >
                                  <Trash2 className="h-4 w-4 mr-2" />
                                  Delete
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </CardHeader>
                        
                        <CardContent className="pt-0">
                          <div className="grid grid-cols-3 gap-4 mb-4">
                            <div className="text-center">
                              <div className="text-lg font-semibold">{session.conversationCount}</div>
                              <div className="text-xs text-muted-foreground">Conversations</div>
                            </div>
                            <div className="text-center">
                              <div className="text-lg font-semibold">{session.artifactCount}</div>
                              <div className="text-xs text-muted-foreground">Artifacts</div>
                            </div>
                            <div className="text-center">
                              <div className="text-lg font-semibold">{session.participantCount}</div>
                              <div className="text-xs text-muted-foreground">Participants</div>
                            </div>
                          </div>
                          
                          {/* Progress bar for active sessions */}
                          {session.status !== 'completed' && session.status !== 'error' && session.progress > 0 && (
                            <div className="mb-4">
                              <div className="flex justify-between text-xs mb-1">
                                <span>Progress</span>
                                <span>{Math.round(session.progress)}%</span>
                              </div>
                              <div className="w-full bg-gray-200 rounded-full h-2">
                                <div 
                                  className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                                  style={{ width: `${session.progress}%` }}
                                />
                              </div>
                            </div>
                          )}
                          
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-1">
                              {Object.entries(personalityIcons).map(([personality, Icon]) => (
                                <div key={personality} className="p-1 rounded-full bg-muted">
                                  <Icon className="h-3 w-3" />
                                </div>
                              ))}
                            </div>
                            
                            <div className="text-xs text-muted-foreground">
                              {formatISOTimestamp(session.createdAt)}
                              {session.completedAt && session.duration && (
                                <span className="ml-2">• {session.duration}m duration</span>
                              )}
                            </div>
                          </div>
                          
                          <Separator className="my-3" />
                          
                          <Button 
                            variant="outline" 
                            size="sm" 
                            className="w-full"
                            onClick={() => setSelectedSession(session.id)}
                          >
                            {session.status === 'completed' ? 'View Results' : 'Continue Session'}
                            <ArrowRight className="h-4 w-4 ml-2" />
                          </Button>
                        </CardContent>
                      </Card>
                    </motion.div>
                  );
                })}
              </AnimatePresence>
            )}
          </div>
        </ScrollArea>
      </div>

      {/* Quick Stats Footer */}
      <div className="border-t p-4">
        <div className="grid grid-cols-4 gap-4 text-center">
          <div>
            <div className="text-lg font-semibold">{sessions.length}</div>
            <div className="text-xs text-muted-foreground">Total Sessions</div>
          </div>
          <div>
            <div className="text-lg font-semibold">
              {sessions.filter(s => s.status === 'completed').length}
            </div>
            <div className="text-xs text-muted-foreground">Completed</div>
          </div>
          <div>
            <div className="text-lg font-semibold">
              {sessions.filter(s => s.status === 'executing' || s.status === 'reviewing').length}
            </div>
            <div className="text-xs text-muted-foreground">Active</div>
          </div>
          <div>
            <div className="text-lg font-semibold">
              {sessions.reduce((sum, s) => sum + s.conversationCount, 0)}
            </div>
            <div className="text-xs text-muted-foreground">Total Conversations</div>
          </div>
        </div>
      </div>
    </div>
  );
};