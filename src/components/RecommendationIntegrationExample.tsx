/**
 * Example: How to integrate the Recommendation System into your Claudia app
 * This file demonstrates proper integration patterns and can be adapted to your existing components
 */

import React, { useEffect, useState } from 'react';
import { RecommendationPanel } from './RecommendationPanel';
import { smartTracker, initializeRecommendationSystem, checkRecommendationSystemHealth } from '@/lib/recommendation-integration';
import { useRecommendationTracking } from '@/lib/recommendation-tracking';
import type { Recommendation } from '@/lib/recommendation-api';

interface AppLayoutProps {
  children: React.ReactNode;
  currentProject?: {
    id: string;
    path: string;
    type: string;
    fileExtensions: string[];
  };
  currentSession?: {
    id: string;
    startTime: Date;
  };
}

/**
 * Main app layout with integrated recommendations
 */
export const AppLayoutWithRecommendations: React.FC<AppLayoutProps> = ({
  children,
  currentProject,
  currentSession
}) => {
  const [isRecommendationSystemReady, setIsRecommendationSystemReady] = useState(false);
  const [systemHealth, setSystemHealth] = useState<any>(null);
  const _tracking = useRecommendationTracking();

  // Initialize recommendation system on app start
  useEffect(() => {
    const initSystem = async () => {
      try {
        await initializeRecommendationSystem();
        setIsRecommendationSystemReady(true);

        // Check system health
        const health = await checkRecommendationSystemHealth();
        setSystemHealth(health);
        
        if (health.status !== 'healthy') {
          console.warn('⚠️ Recommendation system health issues:', health.issues);
        }
      } catch (error) {
        console.error('❌ Failed to initialize recommendation system:', error);
      }
    };

    initSystem();
  }, []);

  // Track project changes
  useEffect(() => {
    if (currentProject && isRecommendationSystemReady) {
      smartTracker.trackSessionEvent('start', currentSession?.id || 'unknown', currentProject.id, {
        projectType: currentProject.type,
        fileExtensions: currentProject.fileExtensions,
        projectPath: currentProject.path
      });
    }
  }, [currentProject, currentSession, isRecommendationSystemReady]);

  // Handle recommendation actions
  const handleRecommendationAction = async (recommendation: Recommendation, action: string) => {
    switch (action) {
      case 'adopted':
        if (recommendation.recommendationType === 'agent') {
          // Navigate to marketplace and install agent
          await handleAgentInstallation(recommendation.itemId);
        } else if (recommendation.recommendationType === 'feature') {
          // Navigate to feature or show tutorial
          await handleFeatureNavigation(recommendation.itemId);
        }
        break;
      
      case 'learn_more':
        if (recommendation.learnMoreUrl) {
          window.open(recommendation.learnMoreUrl, '_blank');
        }
        break;
    }
  };

  const handleAgentInstallation = async (agentId: string) => {
    try {
      // Example marketplace installation logic
      console.log('Installing agent:', agentId);
      
      // Track the installation
      await smartTracker.trackMarketplaceInteraction('install', agentId);
      
      // You would integrate with your actual marketplace installation logic here
      // await marketplace.installAgent(agentId);
      
    } catch (error) {
      console.error('Failed to install agent:', error);
    }
  };

  const handleFeatureNavigation = async (featureId: string) => {
    try {
      console.log('Navigating to feature:', featureId);
      
      // Track feature usage
      await smartTracker.trackFeatureUsage(featureId, 'ui_feature', true);
      
      // Navigate to the feature (you would implement this based on your routing)
      switch (featureId) {
        case 'session_templates':
          // Navigate to session templates
          // router.push('/templates');
          break;
        case 'slash_commands':
          // Show slash commands help
          // showSlashCommandsModal();
          break;
        case 'checkpoint_system':
          // Navigate to checkpoints
          // router.push('/checkpoints');
          break;
        default:
          console.log('Feature navigation not implemented for:', featureId);
      }
      
    } catch (error) {
      console.error('Failed to navigate to feature:', error);
    }
  };

  // Get project context for recommendations
  const getProjectContext = () => {
    if (!currentProject) return undefined;
    
    return {
      projectType: currentProject.type,
      fileExtensions: currentProject.fileExtensions,
      projectPath: currentProject.path,
      sessionDuration: currentSession ? 
        (Date.now() - currentSession.startTime.getTime()) / 1000 : 0
    };
  };

  return (
    <div className="flex h-screen bg-background">
      {/* Main content area */}
      <div className="flex-1 flex flex-col">
        {children}
      </div>

      {/* Recommendation sidebar */}
      {isRecommendationSystemReady && (
        <div className="w-80 border-l bg-card p-4 overflow-y-auto">
          <RecommendationPanel
            projectContext={getProjectContext()}
            onRecommendationAction={handleRecommendationAction}
            maxRecommendations={5}
            showExplanations={true}
            className="mb-6"
          />

          {/* System health indicator (development only) */}
          {process.env.NODE_ENV === 'development' && systemHealth && (
            <div className="mt-4 p-3 rounded border text-xs">
              <div className="font-medium mb-2">
                System Health: {systemHealth.status}
              </div>
              {systemHealth.issues.length > 0 && (
                <div className="text-red-600">
                  Issues: {systemHealth.issues.join(', ')}
                </div>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

/**
 * Enhanced agent execution component with recommendation tracking
 */
interface AgentExecutionWrapperProps {
  agentId: string;
  onExecute: (agentId: string) => Promise<{ success: boolean; duration: number; tokens: number }>;
  children: React.ReactNode;
}

export const AgentExecutionWrapper: React.FC<AgentExecutionWrapperProps> = ({
  agentId,
  onExecute,
  children
}) => {
  const handleExecution = async () => {
    const startTime = Date.now();
    
    try {
      const result = await onExecute(agentId);
      const duration = (Date.now() - startTime) / 1000;
      
      // Track the execution with enhanced context
      await smartTracker.trackAgentExecution(
        agentId,
        result.success,
        duration,
        result.tokens,
        {
          executionContext: 'user_initiated',
          startTime: new Date(startTime).toISOString()
        }
      );
      
      return result;
    } catch (error) {
      const duration = (Date.now() - startTime) / 1000;
      
      // Track failed execution
      await smartTracker.trackAgentExecution(agentId, false, duration, 0, {
        error: error instanceof Error ? error.message : 'Unknown error',
        executionContext: 'user_initiated'
      });
      
      throw error;
    }
  };

  return React.cloneElement(children as React.ReactElement, {
    onClick: handleExecution
  });
};

/**
 * Marketplace browser component with recommendation tracking
 */
interface MarketplaceBrowserWrapperProps {
  onAgentInstall: (agentId: string) => Promise<void>;
  onAgentRate: (agentId: string, rating: number) => Promise<void>;
  children: React.ReactNode;
}

export const MarketplaceBrowserWrapper: React.FC<MarketplaceBrowserWrapperProps> = ({
  onAgentInstall,
  onAgentRate,
  children
}) => {
  const handleSearch = async (query: string) => {
    await smartTracker.trackMarketplaceInteraction('search', undefined, query);
  };

  const handleBrowse = async (_category?: string) => {
    await smartTracker.trackMarketplaceInteraction('browse');
  };

  const handleInstall = async (agentId: string) => {
    try {
      await onAgentInstall(agentId);
      await smartTracker.trackMarketplaceInteraction('install', agentId);
    } catch (error) {
      console.error('Installation failed:', error);
      // Track failed installation
      await smartTracker.trackMarketplaceInteraction('install', agentId);
    }
  };

  const handleRate = async (agentId: string, rating: number) => {
    try {
      await onAgentRate(agentId, rating);
      await smartTracker.trackMarketplaceInteraction('rate', agentId, undefined, rating);
    } catch (error) {
      console.error('Rating failed:', error);
    }
  };

  // Enhance the children with tracking capabilities
  return React.cloneElement(children as React.ReactElement, {
    onSearch: handleSearch,
    onBrowse: handleBrowse,
    onInstall: handleInstall,
    onRate: handleRate
  });
};

/**
 * Feature usage tracker component
 */
interface FeatureUsageTrackerProps {
  featureName: string;
  category: 'productivity' | 'automation' | 'collaboration' | 'advanced' | 'integration';
  children: React.ReactNode;
}

export const FeatureUsageTracker: React.FC<FeatureUsageTrackerProps> = ({
  featureName,
  category,
  children
}) => {
  const [startTime, setStartTime] = useState<number | null>(null);

  const handleFeatureStart = () => {
    setStartTime(Date.now());
  };

  const handleFeatureEnd = (success: boolean = true) => {
    if (startTime) {
      const duration = (Date.now() - startTime) / 1000;
      smartTracker.trackFeatureUsage(featureName, category, success, duration);
      setStartTime(null);
    }
  };

  return React.cloneElement(children as React.ReactElement, {
    onFeatureStart: handleFeatureStart,
    onFeatureEnd: handleFeatureEnd
  });
};

/**
 * Quick integration hook for existing components
 */
export const useRecommendationIntegration = () => {
  const [isReady, setIsReady] = useState(false);

  useEffect(() => {
    initializeRecommendationSystem()
      .then(() => setIsReady(true))
      .catch(console.error);
  }, []);

  return {
    isReady,
    tracker: smartTracker,
    trackAgent: smartTracker.trackAgentExecution.bind(smartTracker),
    trackMarketplace: smartTracker.trackMarketplaceInteraction.bind(smartTracker),
    trackFeature: smartTracker.trackFeatureUsage.bind(smartTracker),
    trackSession: smartTracker.trackSessionEvent.bind(smartTracker)
  };
};

/**
 * Example usage in your main App component:
 * 
 * ```tsx
 * import { AppLayoutWithRecommendations } from './components/RecommendationIntegrationExample';
 * 
 * function App() {
 *   const [currentProject, setCurrentProject] = useState(null);
 *   const [currentSession, setCurrentSession] = useState(null);
 *   
 *   return (
 *     <AppLayoutWithRecommendations 
 *       currentProject={currentProject}
 *       currentSession={currentSession}
 *     >
 *       <YourMainContent />
 *     </AppLayoutWithRecommendations>
 *   );
 * }
 * ```
 */