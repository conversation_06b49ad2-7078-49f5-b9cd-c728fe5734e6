import React, { useState, useEffect, useMemo } from 'react';
import { motion } from 'framer-motion';
import {
  Search,
  FileText,
  MessageSquare,
  Settings,
  FolderOpen,
  Clock,
  Hash,
  Sparkles,
  Bot,
  Zap,
  Terminal,
  Package,
  Layers,
  Database
} from 'lucide-react';
import { Dialog, DialogContent, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { cn } from '@/lib/utils';
import { api } from '@/lib/api';
import { useTabState } from '@/hooks/useTabState';
// We'll use native keyboard event handling instead of react-hotkeys-hook

interface CommandItem {
  id: string;
  title: string;
  description?: string;
  icon: React.ElementType;
  category: 'action' | 'navigation' | 'session' | 'agent' | 'recent' | 'setting';
  keywords: string[];
  action: () => void;
  shortcut?: string;
  badge?: string;
  badgeVariant?: 'default' | 'secondary' | 'destructive' | 'outline';
}

interface CommandPaletteProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onShowSessionTemplates?: () => void;
}

export function CommandPalette({ open, onOpenChange, onShowSessionTemplates }: CommandPaletteProps) {
  const [search, setSearch] = useState('');
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [recentSessions, setRecentSessions] = useState<any[]>([]);
  
  const {
    createChatTab,
    createClaudeMdTab,
    createSettingsTab,
    createUsageTab,
    createMCPTab,
    createMarketplaceTab,
    createProjectsTab,
    createWelcomeTab,
    tabs,
    activeTabId,
    switchToTab
  } = useTabState();

  // Load recent data
  useEffect(() => {
    if (open) {
      loadRecentData();
    }
  }, [open]);

  const loadRecentData = async () => {
    try {
      // Only load projects for now since getRecentSessions command doesn't exist yet
      await api.listProjects();
      setRecentSessions([]); // Will be implemented when backend command is available
    } catch (error) {
      console.error('Failed to load recent data:', error);
    }
  };

  // Define all available commands
  const commands = useMemo<CommandItem[]>(() => {
    const baseCommands: CommandItem[] = [
      // Actions
      {
        id: 'new-chat',
        title: 'New Chat Session',
        description: 'Start a new Claude Code session',
        icon: MessageSquare,
        category: 'action',
        keywords: ['new', 'chat', 'session', 'create', 'start'],
        action: () => {
          createChatTab();
          onOpenChange(false);
        },
        shortcut: '⌘T',
        badge: 'Popular',
        badgeVariant: 'default'
      },
      {
        id: 'new-project',
        title: 'Browse Projects',
        description: 'View and manage your projects',
        icon: FolderOpen,
        category: 'navigation',
        keywords: ['project', 'browse', 'open', 'manage'],
        action: () => {
          createProjectsTab();
          onOpenChange(false);
        }
      },
      {
        id: 'marketplace',
        title: 'Agent Marketplace',
        description: 'Browse and install AI agents',
        icon: Bot,
        category: 'navigation',
        keywords: ['agent', 'marketplace', 'store', 'install', 'browse'],
        action: () => {
          createMarketplaceTab();
          onOpenChange(false);
        },
        badge: 'New',
        badgeVariant: 'secondary'
      },
      {
        id: 'session-templates',
        title: 'Session Templates',
        description: 'Start a guided session with pre-built templates',
        icon: Sparkles,
        category: 'action',
        keywords: ['template', 'session', 'guided', 'workflow', 'starter'],
        action: () => {
          if (onShowSessionTemplates) {
            onShowSessionTemplates();
          }
          onOpenChange(false);
        },
        badge: 'Popular',
        badgeVariant: 'default'
      },
      {
        id: 'claude-md',
        title: 'Edit CLAUDE.md',
        description: 'Configure global Claude settings',
        icon: FileText,
        category: 'action',
        keywords: ['claude', 'md', 'config', 'settings', 'global'],
        action: () => {
          createClaudeMdTab();
          onOpenChange(false);
        }
      },
      {
        id: 'settings',
        title: 'Settings',
        description: 'Application settings and preferences',
        icon: Settings,
        category: 'setting',
        keywords: ['settings', 'preferences', 'config', 'options'],
        action: () => {
          createSettingsTab();
          onOpenChange(false);
        },
        shortcut: '⌘,'
      },
      {
        id: 'mcp-servers',
        title: 'MCP Servers',
        description: 'Manage Model Context Protocol servers',
        icon: Database,
        category: 'setting',
        keywords: ['mcp', 'server', 'model', 'context', 'protocol'],
        action: () => {
          createMCPTab();
          onOpenChange(false);
        }
      },
      {
        id: 'usage',
        title: 'Usage Dashboard',
        description: 'View API usage and statistics',
        icon: Zap,
        category: 'navigation',
        keywords: ['usage', 'dashboard', 'stats', 'api', 'metrics'],
        action: () => {
          createUsageTab();
          onOpenChange(false);
        }
      },
      {
        id: 'welcome',
        title: 'Welcome Page',
        description: 'Return to welcome screen',
        icon: Sparkles,
        category: 'navigation',
        keywords: ['welcome', 'home', 'start'],
        action: () => {
          createWelcomeTab();
          onOpenChange(false);
        }
      }
    ];

    // Add recent sessions
    const sessionCommands: CommandItem[] = recentSessions.map(session => ({
      id: `session-${session.id}`,
      title: session.title || 'Untitled Session',
      description: `${session.project_path} · ${new Date(session.created_at).toLocaleDateString()}`,
      icon: Clock,
      category: 'recent',
      keywords: [session.title, session.project_path, 'recent', 'session'].filter(Boolean),
      action: () => {
        // Create a new tab for this session
        switchToTab(session.id);
        onOpenChange(false);
      }
    }));

    // Add open tabs
    const tabCommands: CommandItem[] = tabs
      .filter(tab => tab.id !== activeTabId)
      .map(tab => ({
        id: `tab-${tab.id}`,
        title: tab.title,
        description: `Switch to ${tab.type} tab`,
        icon: getIconForTabType(tab.type),
        category: 'navigation',
        keywords: [tab.title, tab.type, 'tab', 'switch'],
        action: () => {
          switchToTab(tab.id);
          onOpenChange(false);
        }
      }));

    return [...baseCommands, ...sessionCommands, ...tabCommands];
  }, [recentSessions, tabs, activeTabId]);

  // Filter commands based on search
  const filteredCommands = useMemo(() => {
    if (!search) return commands;
    
    const searchLower = search.toLowerCase();
    return commands.filter(cmd => 
      cmd.title.toLowerCase().includes(searchLower) ||
      cmd.description?.toLowerCase().includes(searchLower) ||
      cmd.keywords.some(k => k.toLowerCase().includes(searchLower))
    );
  }, [commands, search]);

  // Group commands by category
  const groupedCommands = useMemo(() => {
    const groups: Record<string, CommandItem[]> = {
      action: [],
      navigation: [],
      recent: [],
      setting: [],
      agent: []
    };

    filteredCommands.forEach(cmd => {
      if (groups[cmd.category]) {
        groups[cmd.category].push(cmd);
      }
    });

    return groups;
  }, [filteredCommands]);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!open) return;

      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          setSelectedIndex(prev => 
            prev < filteredCommands.length - 1 ? prev + 1 : 0
          );
          break;
        case 'ArrowUp':
          e.preventDefault();
          setSelectedIndex(prev => 
            prev > 0 ? prev - 1 : filteredCommands.length - 1
          );
          break;
        case 'Enter':
          e.preventDefault();
          if (filteredCommands[selectedIndex]) {
            filteredCommands[selectedIndex].action();
          }
          break;
        case 'Escape':
          e.preventDefault();
          onOpenChange(false);
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [open, selectedIndex, filteredCommands, onOpenChange]);

  // Reset state when closed
  useEffect(() => {
    if (!open) {
      setSearch('');
      setSelectedIndex(0);
    }
  }, [open]);

  const getCategoryLabel = (category: string) => {
    const labels: Record<string, string> = {
      action: 'Actions',
      navigation: 'Navigation',
      recent: 'Recent Sessions',
      setting: 'Settings',
      agent: 'Agents'
    };
    return labels[category] || category;
  };

  const getCategoryIcon = (category: string) => {
    const icons: Record<string, React.ElementType> = {
      action: Zap,
      navigation: Layers,
      recent: Clock,
      setting: Settings,
      agent: Bot
    };
    return icons[category] || Hash;
  };

  let commandIndex = 0;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl p-0 gap-0 overflow-hidden">
        <DialogTitle className="sr-only">Command Palette</DialogTitle>
        <DialogDescription className="sr-only">
          Search and execute commands, navigate to different sections, or access recent sessions.
        </DialogDescription>
        <div className="flex items-center gap-3 px-4 py-3 border-b">
          <Search className="h-4 w-4 text-muted-foreground" />
          <Input
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            placeholder="Search commands, sessions, projects..."
            className="flex-1 border-0 bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0"
            autoFocus
          />
          <div className="flex items-center gap-1">
            <kbd className="px-1.5 py-0.5 text-xs font-medium bg-muted rounded">↑</kbd>
            <kbd className="px-1.5 py-0.5 text-xs font-medium bg-muted rounded">↓</kbd>
            <span className="text-xs text-muted-foreground">navigate</span>
            <kbd className="px-1.5 py-0.5 text-xs font-medium bg-muted rounded ml-2">↵</kbd>
            <span className="text-xs text-muted-foreground">select</span>
          </div>
        </div>

        <ScrollArea className="h-[400px]">
          <div className="pb-1">
            {Object.entries(groupedCommands).map(([category, items]) => {
              if (items.length === 0) return null;
              
              const CategoryIcon = getCategoryIcon(category);
              
              return (
                <div key={category}>
                  <div className="flex items-center gap-2 px-4 py-2 text-xs font-medium text-muted-foreground">
                    <CategoryIcon className="h-3 w-3" />
                    {getCategoryLabel(category)}
                  </div>
                  
                  {items.map(item => {
                    const Icon = item.icon;
                    const isSelected = commandIndex === selectedIndex;
                    commandIndex++;
                    
                    return (
                      <motion.button
                        key={item.id}
                        onClick={item.action}
                        className={cn(
                          "w-full flex items-center gap-3 px-4 py-2.5 text-left transition-colors",
                          "hover:bg-accent hover:text-accent-foreground",
                          isSelected && "bg-accent text-accent-foreground"
                        )}
                        whileHover={{ x: 2 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <Icon className="h-4 w-4 flex-shrink-0" />
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2">
                            <span className="font-medium truncate">{item.title}</span>
                            {item.badge && (
                              <Badge variant={item.badgeVariant} className="text-xs">
                                {item.badge}
                              </Badge>
                            )}
                          </div>
                          {item.description && (
                            <p className="text-xs text-muted-foreground truncate">
                              {item.description}
                            </p>
                          )}
                        </div>
                        {item.shortcut && (
                          <kbd className="px-2 py-1 text-xs font-medium bg-muted rounded">
                            {item.shortcut}
                          </kbd>
                        )}
                      </motion.button>
                    );
                  })}
                </div>
              );
            })}
            
            {filteredCommands.length === 0 && (
              <div className="px-4 py-8 text-center text-sm text-muted-foreground">
                No commands found for "{search}"
              </div>
            )}
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
}

function getIconForTabType(type: string): React.ElementType {
  const icons: Record<string, React.ElementType> = {
    chat: MessageSquare,
    'claude-md': FileText,
    settings: Settings,
    usage: Zap,
    mcp: Database,
    marketplace: Bot,
    projects: FolderOpen,
    welcome: Sparkles,
    'agent-run': Terminal,
    'create-agent': Package
  };
  return icons[type] || FileText;
}