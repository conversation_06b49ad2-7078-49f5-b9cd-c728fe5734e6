import React, { useMemo, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Responsive, WidthProvider, Layout } from 'react-grid-layout';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  RefreshCw, 
  X, 
  Settings,
  Github,
  BarChart,
  Brain,
  GraduationCap,
  Clock,
  MessageCircle,
  Move
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useHubStore } from '@/stores/hubStore';
import { usePerformanceMonitor } from '@/hooks/usePerformanceMonitor';
import { ErrorBoundary } from '@/components/ErrorBoundary';
import type { HubWidget, WidgetPosition } from '@/types/hub';
import { 
  LazyRedditWidget,
  LazyCodingProgressWidget,
  LazyAINewsWidget,
  LazyLearningWidget
} from './widgets/LazyWidgets';

// CSS for react-grid-layout
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';

const ResponsiveGridLayout = WidthProvider(Responsive);

interface WidgetGridProps {
  widgets: HubWidget[];
  isEditing: boolean;
  onWidgetUpdate: (widgetId: string, updates: Partial<HubWidget>) => void;
  onWidgetRemove: (widgetId: string) => void;
  onWidgetRefresh: (widgetId: string) => void;
}

interface WidgetCardProps {
  widget: HubWidget;
  isEditing: boolean;
  onUpdate: (updates: Partial<HubWidget>) => void;
  onRemove: () => void;
  onRefresh: () => void;
}

const WidgetCard: React.FC<WidgetCardProps> = React.memo(({
  widget,
  isEditing,
  onUpdate,
  onRemove,
  onRefresh
}) => {
  const { recordWidgetError } = usePerformanceMonitor();
  const getWidgetIcon = (type: HubWidget['type']) => {
    switch (type) {
      case 'reddit':
        return MessageCircle;
      case 'github-trending':
        return Github;
      case 'coding-progress':
        return BarChart;
      case 'ai-news':
        return Brain;
      case 'learning':
        return GraduationCap;
      default:
        return BarChart;
    }
  };

  const handleWidgetError = useCallback((errorInfo: any) => {
    const errorMessage = errorInfo?.error?.message || errorInfo?.errorId || 'Unknown widget error';
    recordWidgetError(widget.id, errorMessage);
  }, [widget.id, recordWidgetError]);

  const getWidgetContent = () => {
    const commonProps = {
      widget,
      isEditing,
      onUpdate,
      onRemove,
      onRefresh,
      onError: handleWidgetError
    };

    switch (widget.type) {
      case 'reddit':
        return <LazyRedditWidget {...commonProps} />;
      case 'coding-progress':
        return <LazyCodingProgressWidget {...commonProps} />;
      case 'ai-news':
        return <LazyAINewsWidget {...commonProps} />;
      case 'learning':
        return <LazyLearningWidget {...commonProps} />;
      default:
        return <div>Unknown widget type</div>;
    }
  };

  const formatLastUpdated = (date?: Date) => {
    if (!date) return null;
    
    // Ensure we have a valid Date object
    const dateObj = date instanceof Date ? date : new Date(date);
    if (isNaN(dateObj.getTime())) return null;
    
    const now = new Date();
    const diffMs = now.getTime() - dateObj.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `${diffHours}h ago`;
    const diffDays = Math.floor(diffHours / 24);
    return `${diffDays}d ago`;
  };

  const Icon = getWidgetIcon(widget.type);
  const lastUpdatedText = formatLastUpdated(widget.lastUpdated);

  return (
    <motion.div
      layout
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.8 }}
      transition={{ duration: 0.3 }}
      className={cn(
        "relative group",
        !widget.isEnabled && "opacity-50"
      )}
    >
      <Card className={cn(
        "h-full transition-all duration-300",
        isEditing && "ring-2 ring-blue-500/20 hover:ring-blue-500/40"
      )}>
        {/* Widget Header */}
        <div className="flex items-center justify-between p-4 border-b">
          <div className="flex items-center gap-2">
            <div className="p-1.5 rounded-md bg-muted">
              <Icon className="h-4 w-4" />
            </div>
            <div>
              <h3 className="font-medium text-sm">{widget.title}</h3>
              {lastUpdatedText && (
                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                  <Clock className="h-3 w-3" />
                  {lastUpdatedText}
                </div>
              )}
            </div>
          </div>

          <div className="flex items-center gap-1">
            {!widget.isEnabled && (
              <Badge variant="secondary" className="text-xs">
                Disabled
              </Badge>
            )}
            
            {/* Widget Controls */}
            <div className={cn(
              "flex items-center gap-1",
              !isEditing && "opacity-0 group-hover:opacity-100 transition-opacity"
            )}>
              <Button
                variant="ghost"
                size="sm"
                onClick={onRefresh}
                className="h-6 w-6 p-0"
                title="Refresh widget"
              >
                <RefreshCw className="h-3 w-3" />
              </Button>
              
              {isEditing && (
                <>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {/* TODO: Open settings modal */}}
                    className="h-6 w-6 p-0"
                    title="Widget settings"
                  >
                    <Settings className="h-3 w-3" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={onRemove}
                    className="h-6 w-6 p-0 text-destructive hover:text-destructive"
                    title="Remove widget"
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </>
              )}
            </div>
          </div>
        </div>

        {/* Widget Content */}
        <div className="p-4 h-[calc(100%-4rem)] overflow-auto">
          {widget.isEnabled ? (
            <ErrorBoundary
              level="widget"
              context={widget.title}
              resetOnPropsChange={true}
              onError={handleWidgetError}
              enableRetry={true}
              maxRetries={2}
            >
              {getWidgetContent()}
            </ErrorBoundary>
          ) : (
            <div className="text-center text-muted-foreground">
              <Icon className="h-12 w-12 mx-auto mb-2 opacity-30" />
              <p>Widget is disabled</p>
              {isEditing && (
                <Button
                  variant="outline"
                  size="sm"
                  className="mt-2"
                  onClick={() => onUpdate({ isEnabled: true })}
                >
                  Enable Widget
                </Button>
              )}
            </div>
          )}
        </div>
      </Card>
    </motion.div>
  );
}, (prevProps, nextProps) => {
  // Custom comparison for performance optimization
  return (
    prevProps.widget.id === nextProps.widget.id &&
    prevProps.widget.lastUpdated === nextProps.widget.lastUpdated &&
    prevProps.widget.isEnabled === nextProps.widget.isEnabled &&
    prevProps.isEditing === nextProps.isEditing &&
    JSON.stringify(prevProps.widget.settings) === JSON.stringify(nextProps.widget.settings)
  );
});

export const WidgetGrid: React.FC<WidgetGridProps> = ({
  widgets,
  isEditing,
  onWidgetUpdate,
  onWidgetRemove,
  onWidgetRefresh
}) => {
  const { settings } = useHubStore();

  // Convert widgets to grid layout format
  const layouts = useMemo(() => {
    const layout: Layout[] = widgets.map((widget) => ({
      i: widget.id,
      x: widget.position.x,
      y: widget.position.y,
      w: widget.position.w,
      h: widget.position.h,
      minW: widget.size.minW,
      minH: widget.size.minH,
      maxW: widget.size.maxW,
      maxH: widget.size.maxH,
    }));

    // Create layouts for all breakpoints
    const breakpoints = settings.layout.responsiveBreakpoints;
    const breakpointLayouts: { [key: string]: Layout[] } = {};
    
    breakpoints.forEach((bp) => {
      breakpointLayouts[bp.name] = layout;
    });

    return breakpointLayouts;
  }, [widgets, settings.layout.responsiveBreakpoints]);

  // Convert breakpoints to react-grid-layout format
  const breakpoints = useMemo(() => {
    const bps: { [key: string]: number } = {};
    settings.layout.responsiveBreakpoints.forEach((bp) => {
      bps[bp.name] = bp.minWidth;
    });
    return bps;
  }, [settings.layout.responsiveBreakpoints]);

  const cols = useMemo(() => {
    const colsConfig: { [key: string]: number } = {};
    settings.layout.responsiveBreakpoints.forEach((bp) => {
      colsConfig[bp.name] = bp.cols;
    });
    return colsConfig;
  }, [settings.layout.responsiveBreakpoints]);

  const handleLayoutChange = (layout: Layout[]) => {
    if (!isEditing || !settings.layout.enableDragDrop) return;

    // Update widget positions
    layout.forEach((item) => {
      const widget = widgets.find(w => w.id === item.i);
      if (widget) {
        const newPosition: WidgetPosition = {
          x: item.x,
          y: item.y,
          w: item.w,
          h: item.h,
        };
        
        // Only update if position actually changed
        if (
          widget.position.x !== newPosition.x ||
          widget.position.y !== newPosition.y ||
          widget.position.w !== newPosition.w ||
          widget.position.h !== newPosition.h
        ) {
          onWidgetUpdate(widget.id, { position: newPosition });
        }
      }
    });
  };

  // Use responsive grid layout or fallback to CSS grid
  if (settings.layout.enableDragDrop && widgets.length > 0) {
    return (
      <div className="w-full">
        <ResponsiveGridLayout
          className="layout"
          layouts={layouts}
          breakpoints={breakpoints}
          cols={cols}
          rowHeight={60}
          margin={[10, 10]}
          containerPadding={[0, 0]}
          isDraggable={isEditing}
          isResizable={isEditing}
          onLayoutChange={handleLayoutChange}
          compactType={settings.layout.snapToGrid ? 'vertical' : null}
          preventCollision={!settings.layout.snapToGrid}
          useCSSTransforms={settings.theme.animationsEnabled}
        >
          {widgets.map((widget) => (
            <div key={widget.id} className="widget-container">
              <WidgetCard
                widget={widget}
                isEditing={isEditing}
                onUpdate={(updates) => onWidgetUpdate(widget.id, updates)}
                onRemove={() => onWidgetRemove(widget.id)}
                onRefresh={() => onWidgetRefresh(widget.id)}
              />
            </div>
          ))}
        </ResponsiveGridLayout>

        {/* Edit Mode Instructions */}
        {isEditing && widgets.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mt-6 p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg border border-blue-200 dark:border-blue-800"
          >
            <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2 flex items-center gap-2">
              <Move className="h-4 w-4" />
              Drag & Drop Mode Active
            </h4>
            <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
              <li>• Drag widgets by their title bar to reposition them</li>
              <li>• Resize widgets by dragging the bottom-right corner</li>
              <li>• Click the refresh button to update widget data</li>
              <li>• Click the settings gear to configure widget options</li>
              <li>• Click the X to remove a widget</li>
            </ul>
          </motion.div>
        )}

        {/* Grid styles */}
        <style dangerouslySetInnerHTML={{__html: `
          .layout {
            position: relative;
          }
          
          .widget-container {
            transition: all 200ms ease;
          }
          
          .widget-container .react-resizable-handle {
            position: absolute;
            width: 20px;
            height: 20px;
            bottom: 0;
            right: 0;
            background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNiIgaGVpZ2h0PSI2IiB2aWV3Qm94PSIwIDAgNiA2IiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgo8ZG90cyBmaWxsPSIjODg4IiBjeD0iMSIgY3k9IjEiIHI9IjEiLz4KPGN5IGZpbGw9IiM4ODgiIGN4PSI1IiBjeT0iMSIgcj0iMSIvPgo8Y3kgZmlsbD0iIzg4OCIgY3g9IjEiIGN5PSI1IiByPSIxIi8+CjxjeGNpcmNsZSBmaWxsPSIjODg4IiBjeD0iNSIgY3k9IjUiIHI9IjEiLz4KPC9zdmc+Cg==') no-repeat;
            background-size: 12px 12px;
            background-position: 2px 2px;
            cursor: nw-resize;
            opacity: 0;
            transition: opacity 200ms ease;
          }
          
          .widget-container:hover .react-resizable-handle {
            opacity: ${isEditing ? 1 : 0};
          }
          
          ${settings.layout.showGridLines && isEditing ? `
            .layout::before {
              content: '';
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              background-image: 
                linear-gradient(to right, rgba(156, 163, 175, 0.2) 1px, transparent 1px),
                linear-gradient(to bottom, rgba(156, 163, 175, 0.2) 1px, transparent 1px);
              background-size: ${settings.layout.gridSize}px ${settings.layout.gridSize}px;
              pointer-events: none;
              z-index: 1;
            }
          ` : ''}
        `}} />
      </div>
    );
  }

  // Fallback to CSS Grid Layout
  return (
    <div className="w-full">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        <AnimatePresence>
          {widgets.map((widget) => (
            <div key={widget.id} className="min-h-[300px]">
              <WidgetCard
                widget={widget}
                isEditing={isEditing}
                onUpdate={(updates) => onWidgetUpdate(widget.id, updates)}
                onRemove={() => onWidgetRemove(widget.id)}
                onRefresh={() => onWidgetRefresh(widget.id)}
              />
            </div>
          ))}
        </AnimatePresence>
      </div>

      {/* Edit Mode Instructions */}
      {isEditing && widgets.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mt-6 p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg border border-blue-200 dark:border-blue-800"
        >
          <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
            Edit Mode Active
          </h4>
          <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
            <li>• Click the refresh button to update widget data</li>
            <li>• Click the settings gear to configure widget options</li>
            <li>• Click the X to remove a widget</li>
            <li>• Enable drag & drop in settings for advanced layout control</li>
          </ul>
        </motion.div>
      )}
    </div>
  );
};