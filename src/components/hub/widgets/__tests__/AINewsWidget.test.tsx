import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { AINewsWidget } from '../AINewsWidget';
import type { HubWidget } from '@/types/hub';

// Mock the useAINews hook
vi.mock('@/hooks/useAINews', () => ({
  useAINews: vi.fn(() => ({
    articles: [
      {
        id: 'test-1',
        title: 'Revolutionary AI Breakthrough in Language Models',
        description: 'Scientists achieve new milestone in AI development with advanced transformer architecture.',
        url: 'https://example.com/ai-breakthrough',
        source: 'AI Weekly',
        publishedAt: new Date('2024-01-15T10:00:00Z'),
        category: 'ai' as const,
        tags: ['ai', 'language models', 'transformer'],
        score: 95,
        isBookmarked: false
      },
      {
        id: 'test-2',
        title: 'New Computer Vision Model Achieves State-of-the-Art Results',
        description: 'Latest research in computer vision shows significant improvements in image recognition accuracy.',
        url: 'https://example.com/cv-model',
        source: 'arXiv',
        publishedAt: new Date('2024-01-14T08:30:00Z'),
        category: 'cv' as const,
        tags: ['computer vision', 'deep learning', 'image recognition'],
        score: 88,
        isBookmarked: true
      }
    ],
    isLoading: false,
    error: null,
    lastUpdated: new Date('2024-01-15T12:00:00Z'),
    refresh: vi.fn(),
    bookmarkArticle: vi.fn(),
    removeBookmark: vi.fn(),
    searchArticles: vi.fn((query: string) => [])
  }))
}));

// Mock Tauri API
vi.mock('@tauri-apps/api/tauri', () => ({
  invoke: vi.fn()
}));

describe('AINewsWidget', () => {
  const mockWidget: HubWidget = {
    id: 'test-widget',
    type: 'ai-news',
    title: 'AI News',
    position: { x: 0, y: 0, w: 4, h: 6 },
    size: { minW: 4, minH: 6 },
    isEnabled: true,
    settings: {
      sources: ['hackernews', 'arxiv'],
      categories: ['ai', 'ml', 'nlp'],
      itemLimit: 10,
      keywords: ['ai', 'machine learning'],
      timeRange: 'week'
    }
  };

  const mockCallbacks = {
    onUpdate: vi.fn(),
    onRemove: vi.fn(),
    onRefresh: vi.fn()
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders correctly when enabled', () => {
    render(
      <AINewsWidget
        widget={mockWidget}
        isEditing={false}
        {...mockCallbacks}
      />
    );

    expect(screen.getByText('Revolutionary AI Breakthrough in Language Models')).toBeInTheDocument();
    expect(screen.getByText('New Computer Vision Model Achieves State-of-the-Art Results')).toBeInTheDocument();
    expect(screen.getByText('2 sources')).toBeInTheDocument();
    expect(screen.getByText('2 articles')).toBeInTheDocument();
  });

  it('shows disabled state when widget is disabled', () => {
    const disabledWidget = { ...mockWidget, isEnabled: false };
    
    render(
      <AINewsWidget
        widget={disabledWidget}
        isEditing={false}
        {...mockCallbacks}
      />
    );

    expect(screen.getByText('AI News Widget Disabled')).toBeInTheDocument();
    expect(screen.getByText('Enable this widget to stay updated with the latest AI/ML news and research')).toBeInTheDocument();
  });

  it('shows enable button when disabled and in editing mode', () => {
    const disabledWidget = { ...mockWidget, isEnabled: false };
    
    render(
      <AINewsWidget
        widget={disabledWidget}
        isEditing={true}
        {...mockCallbacks}
      />
    );

    const enableButton = screen.getByText('Enable Widget');
    fireEvent.click(enableButton);

    expect(mockCallbacks.onUpdate).toHaveBeenCalledWith({ isEnabled: true });
  });

  it('displays article information correctly', () => {
    render(
      <AINewsWidget
        widget={mockWidget}
        isEditing={false}
        {...mockCallbacks}
      />
    );

    // Check for article titles
    expect(screen.getByText('Revolutionary AI Breakthrough in Language Models')).toBeInTheDocument();
    expect(screen.getByText('New Computer Vision Model Achieves State-of-the-Art Results')).toBeInTheDocument();

    // Check for sources
    expect(screen.getByText('AI Weekly')).toBeInTheDocument();
    expect(screen.getByText('arXiv')).toBeInTheDocument();

    // Check for categories
    expect(screen.getByText('AI')).toBeInTheDocument();
    expect(screen.getByText('CV')).toBeInTheDocument();

    // Check for time indicators
    expect(screen.getAllByText(/ago/)).toHaveLength(2);
  });

  it('shows settings panel when editing', () => {
    render(
      <AINewsWidget
        widget={mockWidget}
        isEditing={true}
        {...mockCallbacks}
      />
    );

    const settingsButton = screen.getByTitle('Widget settings');
    fireEvent.click(settingsButton);

    expect(screen.getByText('AI News Settings')).toBeInTheDocument();
    expect(screen.getByText('News Sources')).toBeInTheDocument();
    expect(screen.getByText('Categories')).toBeInTheDocument();
    expect(screen.getByText('Time Range')).toBeInTheDocument();
  });

  it('handles search functionality', () => {
    render(
      <AINewsWidget
        widget={mockWidget}
        isEditing={false}
        {...mockCallbacks}
      />
    );

    const searchInput = screen.getByPlaceholderText('Search articles...');
    fireEvent.change(searchInput, { target: { value: 'breakthrough' } });

    expect(searchInput).toHaveValue('breakthrough');
  });

  it('handles tab switching between all and bookmarked', () => {
    render(
      <AINewsWidget
        widget={mockWidget}
        isEditing={false}
        {...mockCallbacks}
      />
    );

    const bookmarkedTab = screen.getByText('Saved');
    fireEvent.click(bookmarkedTab);

    // Should switch to bookmarked view
    expect(bookmarkedTab.closest('[role="tab"]')).toHaveAttribute('aria-selected', 'true');
  });

  it('calls refresh when refresh button is clicked', () => {
    const mockRefresh = vi.fn();
    
    // Update the mock to return our custom refresh function
    const { useAINews } = require('@/hooks/useAINews');
    useAINews.mockReturnValue({
      articles: [],
      isLoading: false,
      error: null,
      lastUpdated: null,
      refresh: mockRefresh,
      bookmarkArticle: vi.fn(),
      removeBookmark: vi.fn(),
      searchArticles: vi.fn()
    });

    render(
      <AINewsWidget
        widget={mockWidget}
        isEditing={false}
        {...mockCallbacks}
      />
    );

    const refreshButton = screen.getByTitle('Refresh news');
    fireEvent.click(refreshButton);

    expect(mockRefresh).toHaveBeenCalled();
    expect(mockCallbacks.onRefresh).toHaveBeenCalled();
  });

  it('shows loading state correctly', () => {
    const { useAINews } = require('@/hooks/useAINews');
    useAINews.mockReturnValue({
      articles: [],
      isLoading: true,
      error: null,
      lastUpdated: null,
      refresh: vi.fn(),
      bookmarkArticle: vi.fn(),
      removeBookmark: vi.fn(),
      searchArticles: vi.fn()
    });

    render(
      <AINewsWidget
        widget={mockWidget}
        isEditing={false}
        {...mockCallbacks}
      />
    );

    expect(screen.getByText('Loading AI news...')).toBeInTheDocument();
  });

  it('shows error state correctly', () => {
    const { useAINews } = require('@/hooks/useAINews');
    useAINews.mockReturnValue({
      articles: [],
      isLoading: false,
      error: 'Failed to fetch news',
      lastUpdated: null,
      refresh: vi.fn(),
      bookmarkArticle: vi.fn(),
      removeBookmark: vi.fn(),
      searchArticles: vi.fn()
    });

    render(
      <AINewsWidget
        widget={mockWidget}
        isEditing={false}
        {...mockCallbacks}
      />
    );

    expect(screen.getByText('Failed to Load')).toBeInTheDocument();
    expect(screen.getByText('Failed to fetch news')).toBeInTheDocument();
    expect(screen.getByText('Try Again')).toBeInTheDocument();
  });

  it('handles bookmark actions correctly', () => {
    const mockBookmark = vi.fn();
    const mockRemoveBookmark = vi.fn();

    const { useAINews } = require('@/hooks/useAINews');
    useAINews.mockReturnValue({
      articles: [
        {
          id: 'test-1',
          title: 'Test Article',
          description: 'Test description',
          url: 'https://example.com',
          source: 'Test Source',
          publishedAt: new Date(),
          category: 'ai' as const,
          tags: ['test'],
          score: 50,
          isBookmarked: false
        }
      ],
      isLoading: false,
      error: null,
      lastUpdated: new Date(),
      refresh: vi.fn(),
      bookmarkArticle: mockBookmark,
      removeBookmark: mockRemoveBookmark,
      searchArticles: vi.fn()
    });

    render(
      <AINewsWidget
        widget={mockWidget}
        isEditing={false}
        {...mockCallbacks}
      />
    );

    const bookmarkButton = screen.getByTitle('Bookmark article');
    fireEvent.click(bookmarkButton);

    expect(mockBookmark).toHaveBeenCalledWith('test-1');
  });

  it('updates settings when configuration changes', () => {
    render(
      <AINewsWidget
        widget={mockWidget}
        isEditing={true}
        {...mockCallbacks}
      />
    );

    // Open settings
    const settingsButton = screen.getByTitle('Widget settings');
    fireEvent.click(settingsButton);

    // Change article limit
    const limitInput = screen.getByDisplayValue('10');
    fireEvent.change(limitInput, { target: { value: '15' } });

    expect(mockCallbacks.onUpdate).toHaveBeenCalledWith({
      settings: expect.objectContaining({
        itemLimit: 15
      })
    });
  });
});