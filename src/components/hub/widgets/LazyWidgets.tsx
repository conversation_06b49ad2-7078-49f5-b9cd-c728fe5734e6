/**
 * Lazy-loaded widget components for code splitting and performance optimization
 */

import React, { lazy, Suspense } from 'react';
import { motion } from 'framer-motion';
import { Loader2 } from 'lucide-react';
import { Card } from '@/components/ui/card';
import type { WidgetProps } from '@/types/hub';

// Lazy load widget components
// Using a wrapper function to handle named exports properly
const RedditWidget = lazy(async () => {
  try {
    const module = await import('./RedditWidget');
    console.log('[LazyWidgets] RedditWidget module loaded:', module);
    return { default: module.RedditWidget };
  } catch (error) {
    console.error('[LazyWidgets] Failed to load RedditWidget:', error);
    throw error;
  }
});

const CodingProgressWidget = lazy(async () => {
  try {
    const module = await import('./CodingProgressWidget');
    console.log('[LazyWidgets] CodingProgressWidget module loaded:', module);
    return { default: module.CodingProgressWidget };
  } catch (error) {
    console.error('[LazyWidgets] Failed to load CodingProgressWidget:', error);
    throw error;
  }
});

const AINewsWidget = lazy(async () => {
  try {
    const module = await import('./AINewsWidget');
    console.log('[LazyWidgets] AINewsWidget module loaded:', module);
    return { default: module.AINewsWidget };
  } catch (error) {
    console.error('[LazyWidgets] Failed to load AINewsWidget:', error);
    throw error;
  }
});

const LearningWidget = lazy(async () => {
  try {
    const module = await import('./LearningWidget');
    console.log('[LazyWidgets] LearningWidget module loaded:', module);
    return { default: module.LearningWidget };
  } catch (error) {
    console.error('[LazyWidgets] Failed to load LearningWidget:', error);
    throw error;
  }
});

// Loading skeleton component
const WidgetSkeleton: React.FC<{ height?: string }> = ({ height = "h-64" }) => (
  <Card className={`${height} flex items-center justify-center`}>
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="flex flex-col items-center gap-3"
    >
      <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
      <div className="text-sm text-muted-foreground">Loading widget...</div>
    </motion.div>
  </Card>
);

// Error boundary for individual widgets
class WidgetErrorBoundary extends React.Component<
  { children: React.ReactNode; widgetType: string; onError?: (error: Error) => void },
  { hasError: boolean; error?: Error }
> {
  constructor(props: any) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error(`Widget error (${this.props.widgetType}):`, error, errorInfo);
    this.props.onError?.(error);
  }

  render() {
    if (this.state.hasError) {
      return (
        <Card className="h-64 flex items-center justify-center">
          <div className="text-center p-4">
            <div className="text-destructive font-medium mb-2">Widget Error</div>
            <div className="text-sm text-muted-foreground mb-3">
              Failed to load {this.props.widgetType} widget
            </div>
            <button
              onClick={() => this.setState({ hasError: false })}
              className="text-xs text-primary hover:underline"
            >
              Try again
            </button>
          </div>
        </Card>
      );
    }

    return this.props.children;
  }
}

// Lazy widget wrapper
const LazyWidgetWrapper: React.FC<{
  Component: React.LazyExoticComponent<React.ComponentType<WidgetProps>>;
  widgetType: string;
  props: WidgetProps;
  onError?: (error: Error) => void;
}> = ({ Component, widgetType, props, onError }) => (
  <WidgetErrorBoundary widgetType={widgetType} onError={onError}>
    <Suspense fallback={<WidgetSkeleton />}>
      <Component {...props} />
    </Suspense>
  </WidgetErrorBoundary>
);

// Export lazy widget components
export const LazyRedditWidget: React.FC<WidgetProps & { onError?: (error: Error) => void }> = (props) => (
  <LazyWidgetWrapper
    Component={RedditWidget}
    widgetType="reddit"
    props={props}
    onError={props.onError}
  />
);

export const LazyCodingProgressWidget: React.FC<WidgetProps & { onError?: (error: Error) => void }> = (props) => (
  <LazyWidgetWrapper
    Component={CodingProgressWidget}
    widgetType="coding-progress"
    props={props}
    onError={props.onError}
  />
);

export const LazyAINewsWidget: React.FC<WidgetProps & { onError?: (error: Error) => void }> = (props) => (
  <LazyWidgetWrapper
    Component={AINewsWidget}
    widgetType="ai-news"
    props={props}
    onError={props.onError}
  />
);

export const LazyLearningWidget: React.FC<WidgetProps & { onError?: (error: Error) => void }> = (props) => (
  <LazyWidgetWrapper
    Component={LearningWidget}
    widgetType="learning"
    props={props}
    onError={props.onError}
  />
);

