import React, { useState, useMemo } from 'react';
import { motion } from 'framer-motion';
import { 
  BookOpen, 
  Star, 
  Clock, 
  TrendingUp, 
  Search,
  Filter,
  ExternalLink,
  Bookmark,
  BookmarkCheck,
  Target,
  Trophy,
  FileText,
  Video,
  Globe,
  Users,
  CheckCircle,
  BarChart3,
  Lightbulb,
  GraduationCap
} from 'lucide-react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';
import { 
  useLearningWidget,
  type LearningResource,
  type LearningPath,
  type LearningGoal
} from '@/hooks/useLearningContent';
import type { WidgetProps } from '@/types/hub';

interface LearningWidgetProps extends WidgetProps {}

// Type icon mapping
const getTypeIcon = (type: LearningResource['type']) => {
  switch (type) {
    case 'course':
      return <GraduationCap className="h-4 w-4" />;
    case 'tutorial':
      return <BookOpen className="h-4 w-4" />;
    case 'video':
      return <Video className="h-4 w-4" />;
    case 'paper':
      return <FileText className="h-4 w-4" />;
    case 'book':
      return <BookOpen className="h-4 w-4" />;
    case 'documentation':
      return <Globe className="h-4 w-4" />;
    default:
      return <BookOpen className="h-4 w-4" />;
  }
};

// Difficulty color mapping
const getDifficultyColor = (difficulty: string) => {
  switch (difficulty) {
    case 'beginner':
      return 'bg-green-100 text-green-800 border-green-200';
    case 'intermediate':
      return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    case 'advanced':
      return 'bg-red-100 text-red-800 border-red-200';
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200';
  }
};

// Learning Resource Card Component
const LearningResourceCard: React.FC<{ 
  resource: LearningResource; 
  compact?: boolean;
  onBookmark: (id: string) => void;
}> = ({ resource, compact = false, onBookmark }) => {
  const formatDuration = (duration?: string) => {
    if (!duration) return null;
    return duration;
  };

  const formatRating = (rating?: number) => {
    if (!rating) return null;
    return rating.toFixed(1);
  };

  if (compact) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center gap-3 p-3 rounded-lg border bg-card hover:bg-accent/50 transition-colors"
      >
        <div className="flex items-center gap-2 min-w-0 flex-1">
          <div className="flex items-center gap-1">
            {getTypeIcon(resource.type)}
            <Badge variant="outline" className={cn("text-xs", getDifficultyColor(resource.difficulty))}>
              {resource.difficulty}
            </Badge>
          </div>
          
          <div className="min-w-0 flex-1">
            <div className="flex items-center gap-2">
              <a
                href={resource.url}
                target="_blank"
                rel="noopener noreferrer"
                className="font-medium text-sm hover:text-primary truncate"
              >
                {resource.title}
              </a>
              {resource.certificateAvailable && (
                <Trophy className="h-3 w-3 text-yellow-500" />
              )}
            </div>
            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              <span>{resource.provider}</span>
              {resource.duration && (
                <>
                  <span>•</span>
                  <span>{formatDuration(resource.duration)}</span>
                </>
              )}
              {resource.rating && (
                <>
                  <span>•</span>
                  <div className="flex items-center gap-1">
                    <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                    <span>{formatRating(resource.rating)}</span>
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          {resource.progress !== undefined && resource.progress > 0 && (
            <div className="flex items-center gap-1 text-xs">
              <CheckCircle className="h-3 w-3" />
              <span>{resource.progress}%</span>
            </div>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onBookmark(resource.id)}
            className="h-6 w-6 p-0"
          >
            {resource.isBookmarked ? (
              <BookmarkCheck className="h-3 w-3 text-blue-500" />
            ) : (
              <Bookmark className="h-3 w-3" />
            )}
          </Button>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="p-4 rounded-lg border bg-card hover:bg-accent/50 transition-colors"
    >
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center gap-2">
          {getTypeIcon(resource.type)}
          <Badge variant="outline" className={cn("text-xs", getDifficultyColor(resource.difficulty))}>
            {resource.difficulty}
          </Badge>
          {resource.price === 'free' && (
            <Badge variant="secondary" className="text-xs">
              Free
            </Badge>
          )}
          {resource.certificateAvailable && (
            <div className="flex items-center gap-1">
              <Trophy className="h-3 w-3 text-yellow-500" />
              <span className="text-xs text-muted-foreground">Certificate</span>
            </div>
          )}
        </div>
        
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onBookmark(resource.id)}
            className="h-6 w-6 p-0"
          >
            {resource.isBookmarked ? (
              <BookmarkCheck className="h-3 w-3 text-blue-500" />
            ) : (
              <Bookmark className="h-3 w-3" />
            )}
          </Button>
          <Button variant="ghost" size="sm" asChild className="h-6 w-6 p-0">
            <a href={resource.url} target="_blank" rel="noopener noreferrer">
              <ExternalLink className="h-3 w-3" />
            </a>
          </Button>
        </div>
      </div>

      <div className="mb-3">
        <a
          href={resource.url}
          target="_blank"
          rel="noopener noreferrer"
          className="font-medium hover:text-primary"
        >
          {resource.title}
        </a>
        <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
          {resource.description}
        </p>
      </div>

      <div className="flex items-center justify-between text-sm mb-3">
        <div className="flex items-center gap-4">
          <div className="text-muted-foreground">
            {resource.provider}
          </div>
          
          {resource.duration && (
            <div className="flex items-center gap-1 text-muted-foreground">
              <Clock className="h-3 w-3" />
              <span>{formatDuration(resource.duration)}</span>
            </div>
          )}
          
          {resource.rating && (
            <div className="flex items-center gap-1 text-muted-foreground">
              <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
              <span>{formatRating(resource.rating)}</span>
            </div>
          )}
          
          {resource.enrollmentCount && (
            <div className="flex items-center gap-1 text-muted-foreground">
              <Users className="h-3 w-3" />
              <span>{resource.enrollmentCount.toLocaleString()}</span>
            </div>
          )}
        </div>
      </div>

      {/* Progress Bar */}
      {resource.progress !== undefined && (
        <div className="mb-3">
          <div className="flex items-center justify-between text-xs mb-1">
            <span className="text-muted-foreground">Progress</span>
            <span className="text-muted-foreground">{resource.progress}%</span>
          </div>
          <Progress value={resource.progress} className="h-2" />
        </div>
      )}

      {/* Tags */}
      {resource.tags && resource.tags.length > 0 && (
        <div className="flex flex-wrap gap-1">
          {resource.tags.slice(0, 4).map((tag) => (
            <Badge key={tag} variant="secondary" className="text-xs">
              {tag}
            </Badge>
          ))}
          {resource.tags.length > 4 && (
            <Badge variant="secondary" className="text-xs">
              +{resource.tags.length - 4}
            </Badge>
          )}
        </div>
      )}
    </motion.div>
  );
};

// Learning Path Card Component
const LearningPathCard: React.FC<{ 
  path: LearningPath; 
  onEnroll: (id: string) => void;
  onUnenroll: (id: string) => void;
}> = ({ path, onEnroll, onUnenroll }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="p-4 rounded-lg border bg-card hover:bg-accent/50 transition-colors"
    >
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center gap-2">
          <BookOpen className="h-4 w-4" />
          <Badge variant="outline" className={cn("text-xs", getDifficultyColor(path.difficulty))}>
            {path.difficulty}
          </Badge>
          <div className="flex items-center gap-1 text-xs text-muted-foreground">
            <Clock className="h-3 w-3" />
            <span>{path.estimatedHours}h</span>
          </div>
        </div>
        
        <Button
          size="sm"
          variant={path.isEnrolled ? "outline" : "default"}
          onClick={() => path.isEnrolled ? onUnenroll(path.id) : onEnroll(path.id)}
          className="h-7"
        >
          {path.isEnrolled ? "Enrolled" : "Enroll"}
        </Button>
      </div>

      <div className="mb-3">
        <h3 className="font-medium">{path.title}</h3>
        <p className="text-sm text-muted-foreground mt-1">
          {path.description}
        </p>
      </div>

      {/* Progress for enrolled paths */}
      {path.isEnrolled && path.progress !== undefined && (
        <div className="mb-3">
          <div className="flex items-center justify-between text-xs mb-1">
            <span className="text-muted-foreground">Progress</span>
            <span className="text-muted-foreground">{path.progress}%</span>
          </div>
          <Progress value={path.progress} className="h-2" />
        </div>
      )}

      <div className="flex items-center justify-between text-sm">
        <div className="text-muted-foreground">
          {path.resources.length} resources
        </div>
        
        {/* Tags */}
        <div className="flex flex-wrap gap-1">
          {path.tags.slice(0, 2).map((tag) => (
            <Badge key={tag} variant="secondary" className="text-xs">
              {tag}
            </Badge>
          ))}
          {path.tags.length > 2 && (
            <Badge variant="secondary" className="text-xs">
              +{path.tags.length - 2}
            </Badge>
          )}
        </div>
      </div>
    </motion.div>
  );
};

// Learning Goal Card Component
const LearningGoalCard: React.FC<{ 
  goal: LearningGoal;
  onDelete: (id: string) => void;
}> = ({ goal, onDelete }) => {
  const completedMilestones = goal.milestones.filter(m => m.completed).length;
  const totalMilestones = goal.milestones.length;
  
  const getDaysRemaining = () => {
    const today = new Date();
    const target = new Date(goal.targetDate);
    const diffTime = target.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const daysRemaining = getDaysRemaining();

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="p-4 rounded-lg border bg-card hover:bg-accent/50 transition-colors"
    >
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center gap-2">
          <Target className="h-4 w-4" />
          <Badge variant={daysRemaining > 7 ? "default" : daysRemaining > 0 ? "secondary" : "destructive"} className="text-xs">
            {daysRemaining > 0 ? `${daysRemaining} days left` : 'Overdue'}
          </Badge>
        </div>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={() => onDelete(goal.id)}
          className="h-6 w-6 p-0 text-muted-foreground hover:text-destructive"
        >
          ×
        </Button>
      </div>

      <div className="mb-3">
        <h3 className="font-medium">{goal.title}</h3>
        <p className="text-sm text-muted-foreground mt-1">
          {goal.description}
        </p>
      </div>

      {/* Progress */}
      <div className="mb-3">
        <div className="flex items-center justify-between text-xs mb-1">
          <span className="text-muted-foreground">Progress</span>
          <span className="text-muted-foreground">{goal.progress}%</span>
        </div>
        <Progress value={goal.progress} className="h-2" />
      </div>

      {/* Milestones */}
      <div className="space-y-1">
        <div className="text-xs text-muted-foreground">
          Milestones ({completedMilestones}/{totalMilestones})
        </div>
        {goal.milestones.slice(0, 2).map((milestone) => (
          <div key={milestone.id} className="flex items-center gap-2 text-xs">
            {milestone.completed ? (
              <CheckCircle className="h-3 w-3 text-green-500" />
            ) : (
              <div className="h-3 w-3 rounded-full border border-muted-foreground" />
            )}
            <span className={milestone.completed ? "line-through text-muted-foreground" : ""}>
              {milestone.title}
            </span>
          </div>
        ))}
        {goal.milestones.length > 2 && (
          <div className="text-xs text-muted-foreground">
            +{goal.milestones.length - 2} more milestones
          </div>
        )}
      </div>
    </motion.div>
  );
};

// Stats Overview Component
const StatsOverview: React.FC<{
  totalResources: number;
  completedResources: number;
  totalLearningHours: number;
  currentStreak: number;
}> = ({ totalResources, completedResources, totalLearningHours, currentStreak }) => {
  const completionRate = totalResources > 0 ? (completedResources / totalResources) * 100 : 0;

  return (
    <div className="grid grid-cols-2 gap-3 mb-4">
      <Card className="p-3">
        <div className="flex items-center gap-2">
          <BookOpen className="h-4 w-4 text-blue-500" />
          <div>
            <div className="text-lg font-semibold">{totalResources}</div>
            <div className="text-xs text-muted-foreground">Resources</div>
          </div>
        </div>
      </Card>
      
      <Card className="p-3">
        <div className="flex items-center gap-2">
          <CheckCircle className="h-4 w-4 text-green-500" />
          <div>
            <div className="text-lg font-semibold">{completionRate.toFixed(0)}%</div>
            <div className="text-xs text-muted-foreground">Completed</div>
          </div>
        </div>
      </Card>
      
      <Card className="p-3">
        <div className="flex items-center gap-2">
          <Clock className="h-4 w-4 text-orange-500" />
          <div>
            <div className="text-lg font-semibold">{totalLearningHours}</div>
            <div className="text-xs text-muted-foreground">Hours</div>
          </div>
        </div>
      </Card>
      
      <Card className="p-3">
        <div className="flex items-center gap-2">
          <TrendingUp className="h-4 w-4 text-purple-500" />
          <div>
            <div className="text-lg font-semibold">{currentStreak}</div>
            <div className="text-xs text-muted-foreground">Day Streak</div>
          </div>
        </div>
      </Card>
    </div>
  );
};

// Main Widget Component
export const LearningWidget: React.FC<LearningWidgetProps> = ({
  widget,
  onRefresh
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState('discovery');
  const [viewMode, setViewMode] = useState<'compact' | 'detailed'>('compact');
  
  const widgetSettings = widget.settings as {
    resourceTypes?: string[];
    difficulty?: string[];
    providers?: string[];
    topics?: string[];
    showProgress?: boolean;
    sortBy?: string;
  };

  const { 
    resources,
    learningPaths,
    goals,
    recommendations,
    bookmarkedResources,
    loading,
    error,
    filters,
    totalResources,
    completedResources,
    totalLearningHours,
    currentStreak,
    lastUpdated,
    setFilters,
    search,
    refresh,
    clearError,
    bookmarkResource,
    unbookmarkResource,
    deleteGoal,
    enrollInPath,
    unenrollFromPath
  } = useLearningWidget(widgetSettings);

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      await search(searchQuery.trim());
    }
  };

  const handleRefresh = async () => {
    await refresh();
    onRefresh();
  };

  const handleBookmark = async (resourceId: string) => {
    const resource = resources.find(r => r.id === resourceId);
    if (resource?.isBookmarked) {
      await unbookmarkResource(resourceId);
    } else {
      await bookmarkResource(resourceId);
    }
  };

  // Memoized filtered data
  const filteredResources = useMemo(() => {
    return resources.slice(0, 10); // Limit for widget display
  }, [resources]);

  if (error) {
    return (
      <div className="p-4 text-center">
        <div className="text-red-500 mb-2">Failed to load learning content</div>
        <div className="text-sm text-muted-foreground mb-3">{error}</div>
        <Button variant="outline" size="sm" onClick={clearError}>
          Try Again
        </Button>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header Controls */}
      <div className="space-y-3 mb-4">
        {/* Search */}
        <form onSubmit={handleSearch} className="flex gap-2">
          <Input
            placeholder="Search learning resources..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="text-sm"
            disabled={loading}
          />
          <Button 
            type="submit" 
            size="sm" 
            variant="outline"
            disabled={loading}
          >
            <Search className="h-3 w-3" />
          </Button>
        </form>

        {/* Quick Filters */}
        <div className="flex gap-2">
          <Select
            value={filters.sortBy}
            onValueChange={(value) => setFilters({ sortBy: value as any })}
          >
            <SelectTrigger className="text-xs h-8">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="relevance">Relevance</SelectItem>
              <SelectItem value="rating">Rating</SelectItem>
              <SelectItem value="recent">Recent</SelectItem>
              <SelectItem value="popular">Popular</SelectItem>
              <SelectItem value="duration">Duration</SelectItem>
            </SelectContent>
          </Select>

          <Select
            value={filters.priceFilter}
            onValueChange={(value) => setFilters({ priceFilter: value as any })}
          >
            <SelectTrigger className="text-xs h-8">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All</SelectItem>
              <SelectItem value="free">Free</SelectItem>
              <SelectItem value="paid">Paid</SelectItem>
            </SelectContent>
          </Select>

          <Button
            variant="outline"
            size="sm"
            onClick={() => setViewMode(viewMode === 'compact' ? 'detailed' : 'compact')}
            className="h-8"
          >
            <Filter className="h-3 w-3" />
          </Button>
        </div>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
        <TabsList className="grid w-full grid-cols-5 mb-3">
          <TabsTrigger value="discovery" className="text-xs">
            <Lightbulb className="h-3 w-3 mr-1" />
            Discover
          </TabsTrigger>
          <TabsTrigger value="my-learning" className="text-xs">
            <BookOpen className="h-3 w-3 mr-1" />
            My Learning
          </TabsTrigger>
          <TabsTrigger value="paths" className="text-xs">
            <Target className="h-3 w-3 mr-1" />
            Paths
          </TabsTrigger>
          <TabsTrigger value="goals" className="text-xs">
            <Trophy className="h-3 w-3 mr-1" />
            Goals
          </TabsTrigger>
          <TabsTrigger value="stats" className="text-xs">
            <BarChart3 className="h-3 w-3 mr-1" />
            Stats
          </TabsTrigger>
        </TabsList>

        <div className="flex-1 overflow-auto">
            {/* Discovery Tab */}
            <TabsContent value="discovery" className="space-y-3 mt-0">
              {/* Recommendations Section */}
              {recommendations.length > 0 && (
                <div className="mb-4">
                  <h3 className="text-sm font-medium mb-2 flex items-center gap-2">
                    <Star className="h-4 w-4 text-yellow-500" />
                    Recommended for you
                  </h3>
                  <div className="space-y-2">
                    {recommendations.slice(0, 2).map((resource) => (
                      <LearningResourceCard
                        key={resource.id}
                        resource={resource}
                        compact={true}
                        onBookmark={handleBookmark}
                      />
                    ))}
                  </div>
                  <Separator className="mt-3" />
                </div>
              )}

              {/* All Resources */}
              {loading ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary mx-auto mb-2" />
                  <div className="text-sm text-muted-foreground">Loading resources...</div>
                </div>
              ) : filteredResources.length > 0 ? (
                <div className="space-y-2">
                  {filteredResources.map((resource) => (
                    <LearningResourceCard
                      key={resource.id}
                      resource={resource}
                      compact={viewMode === 'compact'}
                      onBookmark={handleBookmark}
                    />
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  No resources found
                </div>
              )}
            </TabsContent>

            {/* My Learning Tab */}
            <TabsContent value="my-learning" className="space-y-3 mt-0">
              {/* Bookmarked Resources */}
              {bookmarkedResources.length > 0 && (
                <div className="mb-4">
                  <h3 className="text-sm font-medium mb-2 flex items-center gap-2">
                    <BookmarkCheck className="h-4 w-4 text-blue-500" />
                    Bookmarked ({bookmarkedResources.length})
                  </h3>
                  <div className="space-y-2">
                    {bookmarkedResources.slice(0, 5).map((resource) => (
                      <LearningResourceCard
                        key={resource.id}
                        resource={resource}
                        compact={true}
                        onBookmark={handleBookmark}
                      />
                    ))}
                  </div>
                  <Separator className="mt-3" />
                </div>
              )}

              {/* In Progress Resources */}
              {resources.filter(r => (r.progress || 0) > 0 && (r.progress || 0) < 100).length > 0 && (
                <div>
                  <h3 className="text-sm font-medium mb-2 flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-orange-500" />
                    In Progress
                  </h3>
                  <div className="space-y-2">
                    {resources
                      .filter(r => (r.progress || 0) > 0 && (r.progress || 0) < 100)
                      .slice(0, 5)
                      .map((resource) => (
                        <LearningResourceCard
                          key={resource.id}
                          resource={resource}
                          compact={true}
                          onBookmark={handleBookmark}
                        />
                      ))
                    }
                  </div>
                </div>
              )}

              {bookmarkedResources.length === 0 && (
                <div className="text-center py-8 text-muted-foreground">
                  <BookmarkCheck className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <div>No bookmarked resources yet</div>
                  <div className="text-xs">Bookmark resources to see them here</div>
                </div>
              )}
            </TabsContent>

            {/* Learning Paths Tab */}
            <TabsContent value="paths" className="space-y-2 mt-0">
              {loading ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary mx-auto mb-2" />
                  <div className="text-sm text-muted-foreground">Loading paths...</div>
                </div>
              ) : learningPaths.length > 0 ? (
                learningPaths.map((path) => (
                  <LearningPathCard
                    key={path.id}
                    path={path}
                    onEnroll={enrollInPath}
                    onUnenroll={unenrollFromPath}
                  />
                ))
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  No learning paths found
                </div>
              )}
            </TabsContent>

            {/* Goals Tab */}
            <TabsContent value="goals" className="space-y-2 mt-0">
              {goals.length > 0 ? (
                goals.map((goal) => (
                  <LearningGoalCard
                    key={goal.id}
                    goal={goal}
                    onDelete={deleteGoal}
                  />
                ))
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <Target className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <div>No learning goals set</div>
                  <div className="text-xs">Create goals to track your progress</div>
                </div>
              )}
            </TabsContent>

            {/* Stats Tab */}
            <TabsContent value="stats" className="mt-0">
              <StatsOverview
                totalResources={totalResources}
                completedResources={completedResources}
                totalLearningHours={totalLearningHours}
                currentStreak={currentStreak}
              />
              
              {/* Recent Activity would go here */}
              <div className="text-center py-4 text-sm text-muted-foreground">
                More detailed analytics coming soon
              </div>
            </TabsContent>
        </div>
      </Tabs>

      {/* Footer */}
      {lastUpdated && (
        <div className="mt-3 pt-3 border-t">
          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <div className="flex items-center gap-1">
              <Clock className="h-3 w-3" />
              Updated {lastUpdated.toLocaleTimeString()}
            </div>
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={handleRefresh}
              disabled={loading}
              className="h-6 px-2"
            >
              {loading ? (
                <div className="animate-spin rounded-full h-3 w-3 border-b border-current" />
              ) : (
                <BookOpen className="h-3 w-3" />
              )}
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default LearningWidget;
