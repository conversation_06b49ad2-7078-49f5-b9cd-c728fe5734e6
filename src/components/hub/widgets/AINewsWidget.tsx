import React, { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  ExternalLink, 
  Brain, 
  Clock, 
  TrendingUp, 
  Bookmark, 
  BookmarkX,
  AlertCircle,
  RefreshCw,
  Settings,
  Search,
  Tag
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Input } from '@/components/ui/input';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { cn } from '@/lib/utils';
import { useAINews } from '@/hooks/useAINews';
import type { HubWidget, AINewsItem, AINewsWidgetSettings } from '@/types/hub';

interface AINewsWidgetProps {
  widget: HubWidget;
  isEditing: boolean;
  onUpdate: (updates: Partial<HubWidget>) => void;
  onRefresh: () => void;
}

interface AINewsCardProps {
  article: AINewsItem;
  isCompact?: boolean;
  onBookmark: (articleId: string) => void;
  onRemoveBookmark: (articleId: string) => void;
}

const AINewsCard: React.FC<AINewsCardProps> = ({ 
  article, 
  isCompact = false, 
  onBookmark, 
  onRemoveBookmark 
}) => {
  // Format time ago
  const formatTimeAgo = (date: Date): string => {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffHours / 24);
    
    if (diffHours < 1) {
      const diffMins = Math.floor(diffMs / (1000 * 60));
      return `${diffMins}m ago`;
    } else if (diffHours < 24) {
      return `${diffHours}h ago`;
    } else if (diffDays < 7) {
      return `${diffDays}d ago`;
    } else {
      return `${Math.floor(diffDays / 7)}w ago`;
    }
  };

  // Get category color
  const getCategoryColor = (category: AINewsItem['category']): string => {
    switch (category) {
      case 'ai': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'ml': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'nlp': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
      case 'cv': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
      case 'robotics': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  // Get source icon
  const getSourceIcon = (source: string): React.ReactNode => {
    switch (source.toLowerCase()) {
      case 'hacker news':
        return <div className="h-3 w-3 bg-orange-500 rounded-sm" />;
      case 'arxiv':
        return <div className="h-3 w-3 bg-blue-500 rounded-sm" />;
      case 'openai blog':
        return <div className="h-3 w-3 bg-black dark:bg-white rounded-sm" />;
      case 'anthropic news':
        return <div className="h-3 w-3 bg-orange-400 rounded-sm" />;
      default:
        return <Brain className="h-3 w-3" />;
    }
  };

  const handleBookmarkToggle = () => {
    if (article.isBookmarked) {
      onRemoveBookmark(article.id);
    } else {
      onBookmark(article.id);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className="group relative"
    >
      <Card className="p-3 hover:shadow-md transition-all duration-200 border-l-2 border-l-transparent hover:border-l-blue-500">
        <div className="space-y-2">
          {/* Header */}
          <div className="flex items-start justify-between gap-2">
            <div className="flex-1 min-w-0">
              <h3 className="font-medium text-sm leading-tight line-clamp-2 group-hover:text-blue-600 transition-colors cursor-pointer"
                  onClick={() => window.open(article.url, '_blank')}>
                {article.title}
              </h3>
              
              <div className="flex items-center gap-2 mt-1 text-xs text-muted-foreground">
                <div className="flex items-center gap-1">
                  {getSourceIcon(article.source)}
                  <span>{article.source}</span>
                </div>
                <span>•</span>
                <div className="flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  <span>{formatTimeAgo(article.publishedAt)}</span>
                </div>
                {article.score && article.score > 0 && (
                  <>
                    <span>•</span>
                    <div className="flex items-center gap-1 text-orange-600">
                      <TrendingUp className="h-3 w-3" />
                      <span>{article.score}</span>
                    </div>
                  </>
                )}
              </div>
            </div>

            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
              onClick={handleBookmarkToggle}
              title={article.isBookmarked ? 'Remove bookmark' : 'Bookmark article'}
            >
              {article.isBookmarked ? (
                <BookmarkX className="h-3 w-3 text-blue-600" />
              ) : (
                <Bookmark className="h-3 w-3" />
              )}
            </Button>
          </div>

          {/* Description */}
          {!isCompact && article.description && (
            <p className="text-xs text-muted-foreground line-clamp-2">
              {article.description.length > 200 
                ? `${article.description.slice(0, 200)}...` 
                : article.description}
            </p>
          )}

          {/* Footer */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {/* Category */}
              <Badge 
                variant="secondary" 
                className={cn("text-xs py-0 px-2", getCategoryColor(article.category))}
              >
                {article.category.toUpperCase()}
              </Badge>

              {/* Tags */}
              {article.tags.slice(0, 2).map((tag, index) => (
                <Badge key={index} variant="outline" className="text-xs py-0 px-1">
                  {tag}
                </Badge>
              ))}
            </div>

            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
              onClick={() => window.open(article.url, '_blank')}
              title="Open article"
            >
              <ExternalLink className="h-3 w-3" />
            </Button>
          </div>
        </div>
      </Card>
    </motion.div>
  );
};

export const AINewsWidget: React.FC<AINewsWidgetProps> = ({
  widget,
  isEditing,
  onUpdate,
  onRefresh
}) => {
  const settings = widget.settings as AINewsWidgetSettings;
  const [showSettings, setShowSettings] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedView, setSelectedView] = useState<'all' | 'bookmarked'>('all');
  
  const { 
    articles, 
    isLoading, 
    error, 
    lastUpdated, 
    refresh,
    bookmarkArticle,
    removeBookmark,
    searchArticles
  } = useAINews({
    settings,
    enabled: widget.isEnabled,
    cacheKey: `widget-${widget.id}`
  });

  // Filter articles based on search and view
  const filteredArticles = useMemo(() => {
    let filtered = searchQuery ? searchArticles(searchQuery) : articles;
    
    if (selectedView === 'bookmarked') {
      filtered = filtered.filter(article => article.isBookmarked);
    }
    
    return filtered;
  }, [articles, searchQuery, selectedView, searchArticles]);

  // Get category stats
  const categoryStats = useMemo(() => {
    const stats: Record<string, number> = {};
    articles.forEach(article => {
      stats[article.category] = (stats[article.category] || 0) + 1;
    });
    return stats;
  }, [articles]);

  // Handle refresh
  const handleRefresh = async () => {
    await refresh();
    onRefresh();
  };

  // Update settings
  const updateSettings = (newSettings: Partial<AINewsWidgetSettings>) => {
    onUpdate({
      settings: { ...settings, ...newSettings }
    });
  };

  if (!widget.isEnabled) {
    return (
      <div className="flex flex-col items-center justify-center h-full text-center p-4">
        <Brain className="h-12 w-12 text-muted-foreground/50 mb-3" />
        <h3 className="font-medium text-muted-foreground mb-2">AI News Widget Disabled</h3>
        <p className="text-sm text-muted-foreground mb-4">
          Enable this widget to stay updated with the latest AI/ML news and research
        </p>
        {isEditing && (
          <Button
            variant="outline"
            size="sm"
            onClick={() => onUpdate({ isEnabled: true })}
          >
            Enable Widget
          </Button>
        )}
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-full text-center p-4">
        <AlertCircle className="h-12 w-12 text-destructive/50 mb-3" />
        <h3 className="font-medium text-destructive mb-2">Failed to Load</h3>
        <p className="text-sm text-muted-foreground mb-4 line-clamp-3">
          {error}
        </p>
        <Button
          variant="outline"
          size="sm"
          onClick={handleRefresh}
          disabled={isLoading}
        >
          <RefreshCw className={cn("h-4 w-4 mr-2", isLoading && "animate-spin")} />
          Try Again
        </Button>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          <div className="flex items-center gap-1 text-xs text-muted-foreground">
            <span>{settings.sources.length} source{settings.sources.length !== 1 ? 's' : ''}</span>
            <span>•</span>
            <span>{articles.length} articles</span>
            {Object.keys(categoryStats).length > 0 && (
              <>
                <span>•</span>
                <div className="flex items-center gap-1">
                  <Tag className="h-3 w-3" />
                  <span>{Object.keys(categoryStats).length} categories</span>
                </div>
              </>
            )}
          </div>
        </div>
        
        <div className="flex items-center gap-1">
          {isEditing && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowSettings(!showSettings)}
              className="h-6 w-6 p-0"
              title="Widget settings"
            >
              <Settings className="h-3 w-3" />
            </Button>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={handleRefresh}
            disabled={isLoading}
            className="h-6 w-6 p-0"
            title="Refresh news"
          >
            <RefreshCw className={cn("h-3 w-3", isLoading && "animate-spin")} />
          </Button>
        </div>
      </div>

      {/* Settings Panel */}
      <AnimatePresence>
        {showSettings && isEditing && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="mb-3"
          >
            <Card className="p-3 bg-muted/50">
              <h4 className="font-medium text-sm mb-3">AI News Settings</h4>
              
              {/* Sources */}
              <div className="mb-3">
                <label className="text-xs font-medium text-muted-foreground mb-1 block">
                  News Sources
                </label>
                <div className="flex flex-wrap gap-1">
                  {['hackernews', 'arxiv', 'aiweekly', 'openai-blog'].map(source => (
                    <Button
                      key={source}
                      variant={settings.sources.includes(source as any) ? "default" : "outline"}
                      size="sm"
                      className="h-6 text-xs"
                      onClick={() => {
                        const newSources = settings.sources.includes(source as any)
                          ? settings.sources.filter(s => s !== source)
                          : [...settings.sources, source as any];
                        updateSettings({ sources: newSources });
                      }}
                    >
                      {source.replace('-', ' ')}
                    </Button>
                  ))}
                </div>
              </div>

              {/* Categories */}
              <div className="mb-3">
                <label className="text-xs font-medium text-muted-foreground mb-1 block">
                  Categories
                </label>
                <div className="flex flex-wrap gap-1">
                  {['ai', 'ml', 'nlp', 'cv', 'robotics'].map(category => (
                    <Button
                      key={category}
                      variant={settings.categories.includes(category as any) ? "default" : "outline"}
                      size="sm"
                      className="h-6 text-xs"
                      onClick={() => {
                        const newCategories = settings.categories.includes(category as any)
                          ? settings.categories.filter(c => c !== category)
                          : [...settings.categories, category as any];
                        updateSettings({ categories: newCategories });
                      }}
                    >
                      {category.toUpperCase()}
                    </Button>
                  ))}
                </div>
              </div>

              {/* Time Range and Limit */}
              <div className="grid grid-cols-2 gap-2 mb-3">
                <div>
                  <label className="text-xs font-medium text-muted-foreground mb-1 block">
                    Time Range
                  </label>
                  <select
                    value={settings.timeRange}
                    onChange={(e) => updateSettings({ timeRange: e.target.value as any })}
                    className="w-full px-2 py-1 text-xs border rounded bg-background"
                  >
                    <option value="day">Past Day</option>
                    <option value="week">Past Week</option>
                    <option value="month">Past Month</option>
                  </select>
                </div>
                
                <div>
                  <label className="text-xs font-medium text-muted-foreground mb-1 block">
                    Article Limit
                  </label>
                  <input
                    type="number"
                    min="5"
                    max="50"
                    value={settings.itemLimit}
                    onChange={(e) => updateSettings({ itemLimit: parseInt(e.target.value) || 10 })}
                    className="w-full px-2 py-1 text-xs border rounded bg-background"
                  />
                </div>
              </div>

              {/* Keywords */}
              <div className="mb-3">
                <label className="text-xs font-medium text-muted-foreground mb-1 block">
                  Keywords (comma-separated)
                </label>
                <input
                  type="text"
                  value={settings.keywords.join(', ')}
                  onChange={(e) => {
                    const keywords = e.target.value
                      .split(',')
                      .map(k => k.trim())
                      .filter(k => k.length > 0);
                    updateSettings({ keywords });
                  }}
                  className="w-full px-2 py-1 text-xs border rounded bg-background"
                  placeholder="ai, machine learning, gpt"
                />
              </div>

              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowSettings(false)}
                className="w-full"
              >
                Done
              </Button>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Search and Filters */}
      <div className="flex items-center gap-2 mb-3">
        <div className="relative flex-1">
          <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-3 w-3 text-muted-foreground" />
          <Input
            type="text"
            placeholder="Search articles..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-7 h-7 text-xs"
          />
        </div>
        
        <Tabs value={selectedView} onValueChange={(value) => setSelectedView(value as any)} className="w-auto">
          <TabsList className="h-7">
            <TabsTrigger value="all" className="h-5 px-2 text-xs">All</TabsTrigger>
            <TabsTrigger value="bookmarked" className="h-5 px-2 text-xs">
              <Bookmark className="h-3 w-3 mr-1" />
              Saved
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-hidden">
        {isLoading && articles.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <RefreshCw className="h-8 w-8 animate-spin text-muted-foreground mx-auto mb-2" />
              <p className="text-sm text-muted-foreground">Loading AI news...</p>
            </div>
          </div>
        ) : filteredArticles.length === 0 ? (
          <div className="flex items-center justify-center h-full text-center p-4">
            <div>
              {selectedView === 'bookmarked' ? (
                <>
                  <Bookmark className="h-12 w-12 text-muted-foreground/50 mx-auto mb-3" />
                  <h3 className="font-medium text-muted-foreground mb-2">No Bookmarked Articles</h3>
                  <p className="text-sm text-muted-foreground">
                    Bookmark interesting articles to read them later
                  </p>
                </>
              ) : searchQuery ? (
                <>
                  <Search className="h-12 w-12 text-muted-foreground/50 mx-auto mb-3" />
                  <h3 className="font-medium text-muted-foreground mb-2">No Results Found</h3>
                  <p className="text-sm text-muted-foreground">
                    Try different keywords or clear the search
                  </p>
                </>
              ) : (
                <>
                  <Brain className="h-12 w-12 text-muted-foreground/50 mx-auto mb-3" />
                  <h3 className="font-medium text-muted-foreground mb-2">No Articles Found</h3>
                  <p className="text-sm text-muted-foreground">
                    Check your sources and settings or try refreshing
                  </p>
                </>
              )}
            </div>
          </div>
        ) : (
          <ScrollArea className="h-full">
            <div className="space-y-2 pr-2">
              {filteredArticles.map((article, index) => (
                <React.Fragment key={article.id}>
                  <AINewsCard 
                    article={article} 
                    isCompact={filteredArticles.length > 10}
                    onBookmark={bookmarkArticle}
                    onRemoveBookmark={removeBookmark}
                  />
                  {index < filteredArticles.length - 1 && <Separator className="my-2" />}
                </React.Fragment>
              ))}
              
              {isLoading && (
                <div className="flex items-center justify-center py-4">
                  <RefreshCw className="h-4 w-4 animate-spin text-muted-foreground mr-2" />
                  <span className="text-xs text-muted-foreground">Refreshing...</span>
                </div>
              )}
            </div>
          </ScrollArea>
        )}
      </div>

      {/* Footer */}
      {filteredArticles.length > 0 && (
        <div className="mt-3 pt-2 border-t">
          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <div className="flex items-center gap-2">
              <span>{filteredArticles.length} articles</span>
              {selectedView === 'bookmarked' && (
                <Badge variant="secondary" className="text-xs py-0 px-1">
                  <Bookmark className="h-3 w-3 mr-1" />
                  Bookmarked
                </Badge>
              )}
            </div>
            {lastUpdated && (
              <span>Updated {lastUpdated.toLocaleTimeString()}</span>
            )}
          </div>
        </div>
      )}
    </div>
  );
};