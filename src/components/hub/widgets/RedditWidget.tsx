import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  ExternalLink, 
  MessageCircle, 
  TrendingUp, 
  Clock, 
  User, 
  Image as ImageIcon,
  AlertCircle,
  RefreshCw,
  Settings
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';
import { useRedditData } from '@/hooks/useRedditData';
import type { HubWidget, RedditPost, RedditWidgetSettings } from '@/types/hub';

interface RedditWidgetProps {
  widget: HubWidget;
  isEditing: boolean;
  onUpdate: (updates: Partial<HubWidget>) => void;
  onRemove: () => void;
  onRefresh: () => void;
}

interface RedditPostCardProps {
  post: RedditPost;
  isCompact?: boolean;
}

const RedditPostCard: React.FC<RedditPostCardProps> = ({ post, isCompact = false }) => {
  const [imageError, setImageError] = useState(false);
  
  // Format score with K/M suffixes
  const formatScore = (score: number): string => {
    if (score >= 1000000) {
      return `${(score / 1000000).toFixed(1)}M`;
    } else if (score >= 1000) {
      return `${(score / 1000).toFixed(1)}K`;
    }
    return score.toString();
  };

  // Format time ago
  const formatTimeAgo = (utc: number): string => {
    const now = Date.now() / 1000;
    const diff = now - utc;
    
    if (diff < 3600) {
      return `${Math.floor(diff / 60)}m ago`;
    } else if (diff < 86400) {
      return `${Math.floor(diff / 3600)}h ago`;
    } else if (diff < 604800) {
      return `${Math.floor(diff / 86400)}d ago`;
    } else {
      return `${Math.floor(diff / 604800)}w ago`;
    }
  };

  // Get thumbnail URL if available
  const getThumbnailUrl = (): string | null => {
    if (!post.preview?.images?.[0]?.source?.url || imageError) {
      return null;
    }
    return post.preview.images[0].source.url;
  };

  const thumbnailUrl = getThumbnailUrl();
  const hasImage = thumbnailUrl && !post.is_self;

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className="group relative"
    >
      <Card className="p-3 hover:shadow-md transition-shadow duration-200 border-l-2 border-l-transparent hover:border-l-blue-500">
        <div className="flex gap-3">
          {/* Thumbnail */}
          {hasImage && !isCompact && (
            <div className="flex-shrink-0 w-16 h-16 rounded-md overflow-hidden bg-muted">
              <img
                src={thumbnailUrl}
                alt=""
                className="w-full h-full object-cover"
                onError={() => setImageError(true)}
                loading="lazy"
              />
            </div>
          )}

          <div className="flex-1 min-w-0">
            {/* Header */}
            <div className="flex items-start justify-between gap-2 mb-2">
              <div className="min-w-0 flex-1">
                <h3 className="font-medium text-sm leading-tight line-clamp-2 group-hover:text-blue-600 transition-colors">
                  {post.title}
                </h3>
                <div className="flex items-center gap-2 mt-1 text-xs text-muted-foreground">
                  <span>r/{post.subreddit}</span>
                  <span>•</span>
                  <div className="flex items-center gap-1">
                    <User className="h-3 w-3" />
                    <span>u/{post.author}</span>
                  </div>
                  <span>•</span>
                  <div className="flex items-center gap-1">
                    <Clock className="h-3 w-3" />
                    <span>{formatTimeAgo(post.created_utc)}</span>
                  </div>
                </div>
              </div>

              {/* Compact thumbnail */}
              {hasImage && isCompact && (
                <div className="flex-shrink-0 w-10 h-10 rounded overflow-hidden bg-muted">
                  <img
                    src={thumbnailUrl}
                    alt=""
                    className="w-full h-full object-cover"
                    onError={() => setImageError(true)}
                    loading="lazy"
                  />
                </div>
              )}
            </div>

            {/* Self text preview */}
            {post.is_self && post.selftext && !isCompact && (
              <p className="text-xs text-muted-foreground line-clamp-2 mb-2">
                {post.selftext.length > 150 ? `${post.selftext.slice(0, 150)}...` : post.selftext}
              </p>
            )}

            {/* Footer */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3 text-xs">
                <div className="flex items-center gap-1 text-orange-600">
                  <TrendingUp className="h-3 w-3" />
                  <span className="font-medium">{formatScore(post.score)}</span>
                </div>
                <div className="flex items-center gap-1 text-blue-600">
                  <MessageCircle className="h-3 w-3" />
                  <span>{post.num_comments}</span>
                </div>
                {post.is_self && (
                  <Badge variant="secondary" className="text-xs py-0 px-1">
                    Text
                  </Badge>
                )}
                {hasImage && (
                  <div className="flex items-center gap-1 text-green-600">
                    <ImageIcon className="h-3 w-3" />
                  </div>
                )}
              </div>

              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                onClick={() => window.open(post.permalink, '_blank')}
                title="Open in Reddit"
              >
                <ExternalLink className="h-3 w-3" />
              </Button>
            </div>
          </div>
        </div>
      </Card>
    </motion.div>
  );
};

export const RedditWidget: React.FC<RedditWidgetProps> = ({
  widget,
  isEditing,
  onUpdate,
  onRefresh
}) => {
  const settings = widget.settings as RedditWidgetSettings;
  const [showSettings, setShowSettings] = useState(false);
  
  const { posts, isLoading, error, lastUpdated, refresh } = useRedditData({
    settings,
    enabled: widget.isEnabled,
    cacheKey: `widget-${widget.id}`
  });

  // Handle refresh
  const handleRefresh = async () => {
    await refresh();
    onRefresh(); // Also call the parent refresh to update last updated time
  };

  // Handle settings toggle
  const handleSettingsClick = () => {
    setShowSettings(!showSettings);
  };

  // Update settings
  const updateSettings = (newSettings: Partial<RedditWidgetSettings>) => {
    onUpdate({
      settings: { ...settings, ...newSettings }
    });
  };

  if (!widget.isEnabled) {
    return (
      <div className="flex flex-col items-center justify-center h-full text-center p-4">
        <MessageCircle className="h-12 w-12 text-muted-foreground/50 mb-3" />
        <h3 className="font-medium text-muted-foreground mb-2">Reddit Widget Disabled</h3>
        <p className="text-sm text-muted-foreground mb-4">
          Enable this widget to see Reddit posts from your favorite subreddits
        </p>
        {isEditing && (
          <Button
            variant="outline"
            size="sm"
            onClick={() => onUpdate({ isEnabled: true })}
          >
            Enable Widget
          </Button>
        )}
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-full text-center p-4">
        <AlertCircle className="h-12 w-12 text-destructive/50 mb-3" />
        <h3 className="font-medium text-destructive mb-2">Failed to Load</h3>
        <p className="text-sm text-muted-foreground mb-4 line-clamp-3">
          {error}
        </p>
        <Button
          variant="outline"
          size="sm"
          onClick={handleRefresh}
          disabled={isLoading}
        >
          <RefreshCw className={cn("h-4 w-4 mr-2", isLoading && "animate-spin")} />
          Try Again
        </Button>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          <div className="flex items-center gap-1 text-xs text-muted-foreground">
            <span>{settings.subreddits.length} subreddit{settings.subreddits.length !== 1 ? 's' : ''}</span>
            <span>•</span>
            <span className="capitalize">{settings.sortBy}</span>
          </div>
        </div>
        
        <div className="flex items-center gap-1">
          {isEditing && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleSettingsClick}
              className="h-6 w-6 p-0"
              title="Widget settings"
            >
              <Settings className="h-3 w-3" />
            </Button>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={handleRefresh}
            disabled={isLoading}
            className="h-6 w-6 p-0"
            title="Refresh posts"
          >
            <RefreshCw className={cn("h-3 w-3", isLoading && "animate-spin")} />
          </Button>
        </div>
      </div>

      {/* Settings Panel */}
      {showSettings && isEditing && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          className="mb-3"
        >
          <Card className="p-3 bg-muted/50">
            <h4 className="font-medium text-sm mb-3">Reddit Settings</h4>
            
            {/* Subreddits */}
            <div className="mb-3">
              <label className="text-xs font-medium text-muted-foreground mb-1 block">
                Subreddits (comma-separated)
              </label>
              <input
                type="text"
                value={settings.subreddits.join(', ')}
                onChange={(e) => {
                  const subreddits = e.target.value
                    .split(',')
                    .map(s => s.trim())
                    .filter(s => s.length > 0);
                  updateSettings({ subreddits });
                }}
                className="w-full px-2 py-1 text-xs border rounded bg-background"
                placeholder="programming, MachineLearning, artificial"
              />
            </div>

            {/* Sort and Limit */}
            <div className="grid grid-cols-2 gap-2 mb-3">
              <div>
                <label className="text-xs font-medium text-muted-foreground mb-1 block">
                  Sort By
                </label>
                <select
                  value={settings.sortBy}
                  onChange={(e) => updateSettings({ sortBy: e.target.value as any })}
                  className="w-full px-2 py-1 text-xs border rounded bg-background"
                >
                  <option value="hot">Hot</option>
                  <option value="new">New</option>
                  <option value="top">Top</option>
                  <option value="rising">Rising</option>
                </select>
              </div>
              
              <div>
                <label className="text-xs font-medium text-muted-foreground mb-1 block">
                  Post Limit
                </label>
                <input
                  type="number"
                  min="5"
                  max="50"
                  value={settings.postLimit}
                  onChange={(e) => updateSettings({ postLimit: parseInt(e.target.value) || 10 })}
                  className="w-full px-2 py-1 text-xs border rounded bg-background"
                />
              </div>
            </div>

            {/* Time Range (for top sort) */}
            {settings.sortBy === 'top' && (
              <div className="mb-3">
                <label className="text-xs font-medium text-muted-foreground mb-1 block">
                  Time Range
                </label>
                <select
                  value={settings.timeRange || 'day'}
                  onChange={(e) => updateSettings({ timeRange: e.target.value as any })}
                  className="w-full px-2 py-1 text-xs border rounded bg-background"
                >
                  <option value="hour">Past Hour</option>
                  <option value="day">Past Day</option>
                  <option value="week">Past Week</option>
                  <option value="month">Past Month</option>
                  <option value="year">Past Year</option>
                  <option value="all">All Time</option>
                </select>
              </div>
            )}

            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowSettings(false)}
              className="w-full"
            >
              Done
            </Button>
          </Card>
        </motion.div>
      )}

      {/* Content */}
      <div className="flex-1 overflow-hidden">
        {isLoading && posts.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <RefreshCw className="h-8 w-8 animate-spin text-muted-foreground mx-auto mb-2" />
              <p className="text-sm text-muted-foreground">Loading Reddit posts...</p>
            </div>
          </div>
        ) : posts.length === 0 ? (
          <div className="flex items-center justify-center h-full text-center p-4">
            <div>
              <MessageCircle className="h-12 w-12 text-muted-foreground/50 mx-auto mb-3" />
              <h3 className="font-medium text-muted-foreground mb-2">No Posts Found</h3>
              <p className="text-sm text-muted-foreground">
                Try different subreddits or check your internet connection
              </p>
            </div>
          </div>
        ) : (
          <ScrollArea className="h-full">
            <div className="space-y-2 pr-2">
              {posts.map((post, index) => (
                <React.Fragment key={post.id}>
                  <RedditPostCard post={post} isCompact={posts.length > 10} />
                  {index < posts.length - 1 && <Separator className="my-2" />}
                </React.Fragment>
              ))}
              
              {isLoading && (
                <div className="flex items-center justify-center py-4">
                  <RefreshCw className="h-4 w-4 animate-spin text-muted-foreground mr-2" />
                  <span className="text-xs text-muted-foreground">Refreshing...</span>
                </div>
              )}
            </div>
          </ScrollArea>
        )}
      </div>

      {/* Footer */}
      {posts.length > 0 && (
        <div className="mt-3 pt-2 border-t">
          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <span>{posts.length} posts</span>
            {lastUpdated && (
              <span>Updated {lastUpdated.toLocaleTimeString()}</span>
            )}
          </div>
        </div>
      )}
    </div>
  );
};
