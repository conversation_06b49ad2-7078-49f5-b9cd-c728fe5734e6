import React from 'react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Eye, Keyboard } from 'lucide-react';
import type { HubSettings, AccessibilitySettings as AccessibilitySettingsType } from '@/types/hub';

interface AccessibilitySettingsProps {
  settings: HubSettings;
  onUpdate: (updates: Partial<HubSettings>) => void;
}

export const AccessibilitySettings: React.FC<AccessibilitySettingsProps> = ({
  settings,
  onUpdate,
}) => {
  const handleAccessibilityUpdate = (accessibilityUpdates: Partial<AccessibilitySettingsType>) => {
    onUpdate({
      accessibility: { ...settings.accessibility, ...accessibilityUpdates }
    });
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold">Accessibility Settings</h3>
        <p className="text-sm text-muted-foreground">
          Configure accessibility features to improve usability for all users.
        </p>
      </div>

      {/* Visual Accessibility */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <Eye className="h-4 w-4" />
            Visual Accessibility
          </CardTitle>
          <CardDescription>
            Options for users with visual impairments
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-sm font-medium">High Contrast Mode</Label>
              <p className="text-xs text-muted-foreground">
                Increase contrast between text and background
              </p>
            </div>
            <Switch
              checked={settings.accessibility.highContrast}
              onCheckedChange={(enabled) => handleAccessibilityUpdate({ highContrast: enabled })}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-sm font-medium">Large Text</Label>
              <p className="text-xs text-muted-foreground">
                Increase font size throughout the interface
              </p>
            </div>
            <Switch
              checked={settings.accessibility.largeText}
              onCheckedChange={(enabled) => handleAccessibilityUpdate({ largeText: enabled })}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-sm font-medium">Reduced Motion</Label>
              <p className="text-xs text-muted-foreground">
                Minimize animations and transitions
              </p>
            </div>
            <Switch
              checked={settings.accessibility.reducedMotion}
              onCheckedChange={(enabled) => handleAccessibilityUpdate({ reducedMotion: enabled })}
            />
          </div>

          <Separator />

          <div className="space-y-2">
            <Label className="text-sm font-medium">Color Blind Support</Label>
            <Select 
              value={settings.accessibility.colorBlindSupport}
              onValueChange={(value: any) => handleAccessibilityUpdate({ colorBlindSupport: value })}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">None - Standard colors</SelectItem>
                <SelectItem value="protanopia">Protanopia - Red-blind</SelectItem>
                <SelectItem value="deuteranopia">Deuteranopia - Green-blind</SelectItem>
                <SelectItem value="tritanopia">Tritanopia - Blue-blind</SelectItem>
              </SelectContent>
            </Select>
            <p className="text-xs text-muted-foreground">
              Adjust colors for different types of color blindness
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Screen Reader Support */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Screen Reader Support</CardTitle>
          <CardDescription>
            Features for users who rely on screen readers
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-sm font-medium">Screen Reader Support</Label>
              <p className="text-xs text-muted-foreground">
                Optimize interface for screen readers
              </p>
            </div>
            <Switch
              checked={settings.accessibility.screenReaderSupport}
              onCheckedChange={(enabled) => handleAccessibilityUpdate({ screenReaderSupport: enabled })}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-sm font-medium">Alt Text for Images</Label>
              <p className="text-xs text-muted-foreground">
                Provide descriptive text for all images
              </p>
            </div>
            <Switch
              checked={settings.accessibility.altTextImages}
              onCheckedChange={(enabled) => handleAccessibilityUpdate({ altTextImages: enabled })}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-sm font-medium">Voice Announcements</Label>
              <p className="text-xs text-muted-foreground">
                Announce important updates and changes
              </p>
            </div>
            <Switch
              checked={settings.accessibility.voiceAnnouncements}
              onCheckedChange={(enabled) => handleAccessibilityUpdate({ voiceAnnouncements: enabled })}
            />
          </div>
        </CardContent>
      </Card>

      {/* Navigation Assistance */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <Keyboard className="h-4 w-4" />
            Navigation Assistance
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-sm font-medium">Keyboard Navigation</Label>
              <p className="text-xs text-muted-foreground">
                Enable full keyboard navigation support
              </p>
            </div>
            <Switch
              checked={settings.accessibility.keyboardNavigation}
              onCheckedChange={(enabled) => handleAccessibilityUpdate({ keyboardNavigation: enabled })}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-sm font-medium">Focus Indicators</Label>
              <p className="text-xs text-muted-foreground">
                Show clear visual indicators for focused elements
              </p>
            </div>
            <Switch
              checked={settings.accessibility.focusIndicators}
              onCheckedChange={(enabled) => handleAccessibilityUpdate({ focusIndicators: enabled })}
            />
          </div>
        </CardContent>
      </Card>

      {/* Additional Options */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Additional Options</CardTitle>
          <CardDescription>
            More accessibility features and preferences
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="p-4 rounded-lg border bg-muted/50">
            <h4 className="font-medium mb-2">System Preferences</h4>
            <p className="text-sm text-muted-foreground mb-3">
              Many accessibility features can be configured through your operating system's 
              accessibility settings. These will be automatically respected by the dashboard.
            </p>
            <ul className="text-xs text-muted-foreground space-y-1">
              <li>• System dark/light mode preference</li>
              <li>• Reduced motion preference</li>
              <li>• High contrast mode</li>
              <li>• Text size scaling</li>
            </ul>
          </div>

          <div className="p-4 rounded-lg border bg-blue-50 dark:bg-blue-950/30">
            <h4 className="font-medium mb-2 text-blue-900 dark:text-blue-100">
              Feedback Welcome
            </h4>
            <p className="text-sm text-blue-800 dark:text-blue-200">
              We're committed to making our dashboard accessible to everyone. 
              If you have suggestions for improving accessibility, please let us know.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};