import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Database, Share, Download, Trash } from 'lucide-react';
import type { HubSettings, PrivacySettings as PrivacySettingsType } from '@/types/hub';

interface PrivacySettingsProps {
  settings: HubSettings;
  onUpdate: (updates: Partial<HubSettings>) => void;
}

export const PrivacySettings: React.FC<PrivacySettingsProps> = ({
  settings,
  onUpdate,
}) => {
  const handlePrivacyUpdate = (privacyUpdates: Partial<PrivacySettingsType>) => {
    onUpdate({
      privacy: { ...settings.privacy, ...privacyUpdates }
    });
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold">Privacy Settings</h3>
        <p className="text-sm text-muted-foreground">
          Control how your data is collected, used, and shared.
        </p>
      </div>

      {/* Data Collection */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <Database className="h-4 w-4" />
            Data Collection
          </CardTitle>
          <CardDescription>
            Control what data is collected and how it's used
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-sm font-medium">Analytics</Label>
              <p className="text-xs text-muted-foreground">
                Help improve the app by sharing anonymous usage data
              </p>
            </div>
            <Switch
              checked={settings.privacy.analytics}
              onCheckedChange={(enabled) => handlePrivacyUpdate({ analytics: enabled })}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-sm font-medium">Crash Reporting</Label>
              <p className="text-xs text-muted-foreground">
                Send crash reports to help fix bugs
              </p>
            </div>
            <Switch
              checked={settings.privacy.crashReporting}
              onCheckedChange={(enabled) => handlePrivacyUpdate({ crashReporting: enabled })}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-sm font-medium">Usage Statistics</Label>
              <p className="text-xs text-muted-foreground">
                Collect data about how you use the dashboard
              </p>
            </div>
            <Switch
              checked={settings.privacy.usageStatistics}
              onCheckedChange={(enabled) => handlePrivacyUpdate({ usageStatistics: enabled })}
            />
          </div>
        </CardContent>
      </Card>

      {/* Data Sharing */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <Share className="h-4 w-4" />
            Data Sharing & Personalization
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-sm font-medium">Data Sharing</Label>
              <p className="text-xs text-muted-foreground">
                Share anonymized data to improve recommendations
              </p>
            </div>
            <Switch
              checked={settings.privacy.dataSharing}
              onCheckedChange={(enabled) => handlePrivacyUpdate({ dataSharing: enabled })}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-sm font-medium">Personalized Content</Label>
              <p className="text-xs text-muted-foreground">
                Use your data to personalize content recommendations
              </p>
            </div>
            <Switch
              checked={settings.privacy.personalizedContent}
              onCheckedChange={(enabled) => handlePrivacyUpdate({ personalizedContent: enabled })}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-sm font-medium">Third-Party Integrations</Label>
              <p className="text-xs text-muted-foreground">
                Allow connections to external services like GitHub
              </p>
            </div>
            <Switch
              checked={settings.privacy.thirdPartyIntegrations}
              onCheckedChange={(enabled) => handlePrivacyUpdate({ thirdPartyIntegrations: enabled })}
            />
          </div>
        </CardContent>
      </Card>

      {/* Data Retention */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Data Retention</CardTitle>
          <CardDescription>
            How long your data is stored
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label className="text-sm font-medium">Data Retention Period</Label>
            <Select 
              value={settings.privacy.dataRetention}
              onValueChange={(value: any) => handlePrivacyUpdate({ dataRetention: value })}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="30days">30 days - Minimal storage</SelectItem>
                <SelectItem value="6months">6 months - Balanced approach</SelectItem>
                <SelectItem value="1year">1 year - Extended history</SelectItem>
                <SelectItem value="forever">Forever - Never delete</SelectItem>
              </SelectContent>
            </Select>
            <p className="text-xs text-muted-foreground">
              How long to keep your activity data and preferences
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Data Management */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Data Management</CardTitle>
          <CardDescription>
            Export or delete your data
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-sm font-medium">Export Data</Label>
              <p className="text-xs text-muted-foreground">
                Download all your data in JSON format
              </p>
            </div>
            <Button variant="outline" size="sm" className="gap-2">
              <Download className="h-4 w-4" />
              Export
            </Button>
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-sm font-medium text-destructive">Delete All Data</Label>
              <p className="text-xs text-muted-foreground">
                Permanently delete all your data and settings
              </p>
            </div>
            <Button variant="destructive" size="sm" className="gap-2">
              <Trash className="h-4 w-4" />
              Delete
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};