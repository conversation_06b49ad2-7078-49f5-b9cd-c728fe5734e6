import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { 
  Code, 
  Zap, 
  Database, 
  AlertTriangle,
  Download,
  ExternalLink
} from 'lucide-react';
import type { HubSettings, AdvancedSettings as AdvancedSettingsType } from '@/types/hub';

interface AdvancedSettingsProps {
  settings: HubSettings;
  onUpdate: (updates: Partial<HubSettings>) => void;
}

const EXPERIMENTAL_FEATURES = [
  {
    id: 'ai-recommendations',
    name: 'AI-Powered Recommendations',
    description: 'Use AI to suggest relevant content based on your activity',
    status: 'beta',
  },
  {
    id: 'advanced-filtering',
    name: 'Advanced Content Filtering',
    description: 'More sophisticated content filtering and categorization',
    status: 'experimental',
  },
  {
    id: 'real-time-collaboration',
    name: 'Real-time Collaboration',
    description: 'Share and collaborate on dashboard configurations',
    status: 'alpha',
  },
  {
    id: 'custom-integrations',
    name: 'Custom API Integrations',
    description: 'Connect to custom APIs and data sources',
    status: 'experimental',
  },
];

export const AdvancedSettings: React.FC<AdvancedSettingsProps> = ({
  settings,
  onUpdate,
}) => {
  const [customCSS, setCustomCSS] = useState(settings.advanced.customCSS || '');
  const [newEndpointName, setNewEndpointName] = useState('');
  const [newEndpointUrl, setNewEndpointUrl] = useState('');

  const handleAdvancedUpdate = (advancedUpdates: Partial<AdvancedSettingsType>) => {
    onUpdate({
      advanced: { ...settings.advanced, ...advancedUpdates }
    });
  };

  const toggleExperimentalFeature = (featureId: string) => {
    const currentFeatures = settings.advanced.experimentalFeatures || [];
    const updatedFeatures = currentFeatures.includes(featureId)
      ? currentFeatures.filter(f => f !== featureId)
      : [...currentFeatures, featureId];
    
    handleAdvancedUpdate({ experimentalFeatures: updatedFeatures });
  };

  const addApiEndpoint = () => {
    if (!newEndpointName.trim() || !newEndpointUrl.trim()) return;

    const updatedEndpoints = {
      ...settings.advanced.apiEndpoints,
      [newEndpointName]: newEndpointUrl
    };
    
    handleAdvancedUpdate({ apiEndpoints: updatedEndpoints });
    setNewEndpointName('');
    setNewEndpointUrl('');
  };

  const removeApiEndpoint = (name: string) => {
    const updatedEndpoints = { ...settings.advanced.apiEndpoints };
    delete updatedEndpoints[name];
    handleAdvancedUpdate({ apiEndpoints: updatedEndpoints });
  };

  const handleCustomCSSUpdate = () => {
    handleAdvancedUpdate({ customCSS });
  };

  const exportDebugInfo = () => {
    const debugInfo = {
      timestamp: new Date().toISOString(),
      settings: settings,
      userAgent: navigator.userAgent,
      platform: navigator.platform,
      language: navigator.language,
      screenResolution: `${screen.width}x${screen.height}`,
      viewportSize: `${window.innerWidth}x${window.innerHeight}`,
    };

    const blob = new Blob([JSON.stringify(debugInfo, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `claudia-debug-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold">Advanced Settings</h3>
        <p className="text-sm text-muted-foreground">
          Developer options, experimental features, and advanced configurations.
        </p>
      </div>

      {/* Developer Options */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <Code className="h-4 w-4" />
            Developer Options
          </CardTitle>
          <CardDescription>
            Settings for developers and power users
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-sm font-medium">Developer Mode</Label>
              <p className="text-xs text-muted-foreground">
                Enable additional debugging and development features
              </p>
            </div>
            <Switch
              checked={settings.advanced.developerMode}
              onCheckedChange={(enabled) => handleAdvancedUpdate({ developerMode: enabled })}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-sm font-medium">Debug Mode</Label>
              <p className="text-xs text-muted-foreground">
                Show detailed logging and error information
              </p>
            </div>
            <Switch
              checked={settings.advanced.debugMode}
              onCheckedChange={(enabled) => handleAdvancedUpdate({ debugMode: enabled })}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-sm font-medium">Beta Features</Label>
              <p className="text-xs text-muted-foreground">
                Access to beta features and early previews
              </p>
            </div>
            <Switch
              checked={settings.advanced.enableBetaFeatures}
              onCheckedChange={(enabled) => handleAdvancedUpdate({ enableBetaFeatures: enabled })}
            />
          </div>

          {settings.advanced.developerMode && (
            <>
              <Separator />
              <div className="flex justify-end">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={exportDebugInfo}
                  className="gap-2"
                >
                  <Download className="h-4 w-4" />
                  Export Debug Info
                </Button>
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Experimental Features */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <Zap className="h-4 w-4" />
            Experimental Features
          </CardTitle>
          <CardDescription>
            Try out cutting-edge features that are still in development
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center gap-2 p-3 rounded border bg-yellow-50 dark:bg-yellow-950/30">
            <AlertTriangle className="h-4 w-4 text-yellow-600" />
            <p className="text-sm text-yellow-800 dark:text-yellow-200">
              Experimental features may be unstable and could change without notice.
            </p>
          </div>

          <div className="space-y-3">
            {EXPERIMENTAL_FEATURES.map((feature) => (
              <div key={feature.id} className="flex items-center justify-between p-3 rounded border">
                <div className="space-y-1">
                  <div className="flex items-center gap-2">
                    <span className="font-medium">{feature.name}</span>
                    <Badge 
                      variant={feature.status === 'beta' ? 'default' : 'secondary'}
                      className="text-xs"
                    >
                      {feature.status}
                    </Badge>
                  </div>
                  <p className="text-xs text-muted-foreground">{feature.description}</p>
                </div>
                <Switch
                  checked={(settings.advanced.experimentalFeatures || []).includes(feature.id)}
                  onCheckedChange={() => toggleExperimentalFeature(feature.id)}
                  disabled={!settings.advanced.enableBetaFeatures}
                />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Custom API Endpoints */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <Database className="h-4 w-4" />
            Custom API Endpoints
          </CardTitle>
          <CardDescription>
            Configure custom API endpoints for data sources
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Add New Endpoint */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Add Custom Endpoint</Label>
            <div className="grid grid-cols-2 gap-2">
              <Input
                placeholder="Service name..."
                value={newEndpointName}
                onChange={(e) => setNewEndpointName(e.target.value)}
              />
              <Input
                placeholder="https://api.example.com"
                value={newEndpointUrl}
                onChange={(e) => setNewEndpointUrl(e.target.value)}
              />
            </div>
            <Button
              onClick={addApiEndpoint}
              disabled={!newEndpointName.trim() || !newEndpointUrl.trim()}
              className="w-full"
            >
              Add Endpoint
            </Button>
          </div>

          <Separator />

          {/* Existing Endpoints */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Configured Endpoints</Label>
            {Object.keys(settings.advanced.apiEndpoints).length === 0 ? (
              <p className="text-sm text-muted-foreground py-4 text-center">
                No custom endpoints configured
              </p>
            ) : (
              <div className="space-y-2">
                {Object.entries(settings.advanced.apiEndpoints).map(([name, url]) => (
                  <div key={name} className="flex items-center justify-between p-2 rounded border">
                    <div className="space-y-0.5">
                      <span className="font-medium">{name}</span>
                      <p className="text-xs text-muted-foreground font-mono">{url}</p>
                    </div>
                    <div className="flex gap-1">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => window.open(url, '_blank')}
                      >
                        <ExternalLink className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => removeApiEndpoint(name)}
                        className="text-destructive hover:text-destructive"
                      >
                        ×
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Custom CSS */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Custom CSS</CardTitle>
          <CardDescription>
            Add custom CSS to personalize the dashboard appearance
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label className="text-sm font-medium">Custom Stylesheet</Label>
            <Textarea
              placeholder="/* Add your custom CSS here */
.custom-widget {
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Example: Change widget background */
.widget-container {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}"
              value={customCSS}
              onChange={(e) => setCustomCSS(e.target.value)}
              className="min-h-32 font-mono text-sm"
            />
          </div>
          
          <div className="flex justify-between">
            <p className="text-xs text-muted-foreground">
              Custom CSS will be applied to the dashboard interface
            </p>
            <Button
              onClick={handleCustomCSSUpdate}
              disabled={customCSS === settings.advanced.customCSS}
              size="sm"
            >
              Apply CSS
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Backup Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Backup Settings</CardTitle>
          <CardDescription>
            Configure automatic backups of your settings and data
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-sm font-medium">Auto Backup</Label>
              <p className="text-xs text-muted-foreground">
                Automatically backup settings and preferences
              </p>
            </div>
            <Switch
              checked={settings.advanced.backupSettings.autoBackup}
              onCheckedChange={(enabled) => handleAdvancedUpdate({
                backupSettings: { ...settings.advanced.backupSettings, autoBackup: enabled }
              })}
            />
          </div>

          {settings.advanced.backupSettings.autoBackup && (
            <>
              <Separator />
              
              <div className="space-y-2">
                <Label className="text-sm font-medium">Backup Interval</Label>
                <Select 
                  value={settings.advanced.backupSettings.backupInterval}
                  onValueChange={(value: any) => handleAdvancedUpdate({
                    backupSettings: { ...settings.advanced.backupSettings, backupInterval: value }
                  })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="daily">Daily</SelectItem>
                    <SelectItem value="weekly">Weekly</SelectItem>
                    <SelectItem value="monthly">Monthly</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium">Max Backups to Keep</Label>
                <Input
                  type="number"
                  min="1"
                  max="20"
                  value={settings.advanced.backupSettings.maxBackups}
                  onChange={(e) => handleAdvancedUpdate({
                    backupSettings: { 
                      ...settings.advanced.backupSettings, 
                      maxBackups: parseInt(e.target.value) || 5 
                    }
                  })}
                  className="w-20"
                />
                <p className="text-xs text-muted-foreground">
                  Older backups will be automatically deleted
                </p>
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
};