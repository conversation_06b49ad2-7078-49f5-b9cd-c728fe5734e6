import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';

import { 
  Palette, 
  Sun, 
  Moon, 
  Monitor,
  Paintbrush,
  Eye,
  Type,
  Sparkles
} from 'lucide-react';
import { cn } from '@/lib/utils';
import type { HubSettings, CustomColorScheme } from '@/types/hub';

interface ThemeSettingsProps {
  settings: HubSettings;
  onUpdate: (updates: Partial<HubSettings>) => void;
}

interface ColorPreset {
  id: string;
  name: string;
  description: string;
  colors: CustomColorScheme;
}

const COLOR_PRESETS: ColorPreset[] = [
  {
    id: 'default',
    name: 'Default',
    description: 'Classic Claudia theme',
    colors: {
      primary: 'hsl(222, 84%, 55%)',
      secondary: 'hsl(210, 40%, 95%)',
      accent: 'hsl(280, 80%, 60%)',
      background: 'hsl(0, 0%, 100%)',
      surface: 'hsl(0, 0%, 98%)',
      text: 'hsl(222, 84%, 5%)',
      textSecondary: 'hsl(215, 16%, 45%)',
      border: 'hsl(214, 32%, 91%)',
      success: 'hsl(142, 76%, 36%)',
      warning: 'hsl(38, 92%, 50%)',
      error: 'hsl(0, 84%, 60%)',
    },
  },
  {
    id: 'ocean',
    name: 'Ocean Blue',
    description: 'Calm blue tones',
    colors: {
      primary: 'hsl(200, 95%, 55%)',
      secondary: 'hsl(200, 30%, 95%)',
      accent: 'hsl(185, 80%, 60%)',
      background: 'hsl(0, 0%, 100%)',
      surface: 'hsl(200, 20%, 98%)',
      text: 'hsl(200, 95%, 5%)',
      textSecondary: 'hsl(200, 16%, 45%)',
      border: 'hsl(200, 32%, 91%)',
      success: 'hsl(142, 76%, 36%)',
      warning: 'hsl(38, 92%, 50%)',
      error: 'hsl(0, 84%, 60%)',
    },
  },
  {
    id: 'forest',
    name: 'Forest Green',
    description: 'Natural green theme',
    colors: {
      primary: 'hsl(142, 76%, 36%)',
      secondary: 'hsl(142, 30%, 95%)',
      accent: 'hsl(120, 80%, 45%)',
      background: 'hsl(0, 0%, 100%)',
      surface: 'hsl(142, 20%, 98%)',
      text: 'hsl(142, 76%, 5%)',
      textSecondary: 'hsl(142, 16%, 45%)',
      border: 'hsl(142, 32%, 91%)',
      success: 'hsl(142, 76%, 36%)',
      warning: 'hsl(38, 92%, 50%)',
      error: 'hsl(0, 84%, 60%)',
    },
  },
  {
    id: 'sunset',
    name: 'Sunset Orange',
    description: 'Warm orange gradient',
    colors: {
      primary: 'hsl(25, 95%, 55%)',
      secondary: 'hsl(25, 30%, 95%)',
      accent: 'hsl(45, 80%, 60%)',
      background: 'hsl(0, 0%, 100%)',
      surface: 'hsl(25, 20%, 98%)',
      text: 'hsl(25, 95%, 5%)',
      textSecondary: 'hsl(25, 16%, 45%)',
      border: 'hsl(25, 32%, 91%)',
      success: 'hsl(142, 76%, 36%)',
      warning: 'hsl(38, 92%, 50%)',
      error: 'hsl(0, 84%, 60%)',
    },
  },
];

export const ThemeSettings: React.FC<ThemeSettingsProps> = ({
  settings,
  onUpdate,
}) => {
  const [showCustomColorEditor, setShowCustomColorEditor] = useState(false);
  const [selectedPreset, setSelectedPreset] = useState('default');

  const handleThemeUpdate = (themeUpdates: Partial<typeof settings.theme>) => {
    onUpdate({
      theme: { ...settings.theme, ...themeUpdates }
    });
  };

  const handleApplyColorPreset = (preset: ColorPreset) => {
    handleThemeUpdate({ customColors: preset.colors });
    setSelectedPreset(preset.id);
  };

  const handleCustomColorChange = (colorKey: keyof CustomColorScheme, value: string) => {
    const currentColors = settings.theme.customColors || COLOR_PRESETS[0].colors;
    handleThemeUpdate({
      customColors: { ...currentColors, [colorKey]: value }
    });
  };

  const resetToDefault = () => {
    const defaultPreset = COLOR_PRESETS.find(p => p.id === 'default');
    if (defaultPreset) {
      handleApplyColorPreset(defaultPreset);
    }
  };

  const currentColors = settings.theme.customColors || COLOR_PRESETS[0].colors;

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold">Theme Settings</h3>
        <p className="text-sm text-muted-foreground">
          Customize the appearance and visual style of your dashboard.
        </p>
      </div>

      {/* Theme Mode */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <Palette className="h-4 w-4" />
            Theme Mode
          </CardTitle>
          <CardDescription>
            Choose between light, dark, or automatic theme switching
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-3 gap-3">
            {[
              { 
                value: 'light', 
                label: 'Light', 
                icon: Sun, 
                description: 'Clean and bright interface' 
              },
              { 
                value: 'dark', 
                label: 'Dark', 
                icon: Moon, 
                description: 'Easy on the eyes in low light' 
              },
              { 
                value: 'auto', 
                label: 'System', 
                icon: Monitor, 
                description: 'Follows your system preference' 
              },
            ].map((mode) => {
              const Icon = mode.icon;
              return (
                <button
                  key={mode.value}
                  onClick={() => handleThemeUpdate({ mode: mode.value as any })}
                  className={cn(
                    "p-4 rounded-lg border-2 transition-all text-left",
                    settings.theme.mode === mode.value
                      ? "border-primary bg-primary/5"
                      : "border-border hover:border-primary/50"
                  )}
                >
                  <div className="flex items-center gap-2 mb-2">
                    <Icon className="h-4 w-4" />
                    <span className="font-medium">{mode.label}</span>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {mode.description}
                  </p>
                </button>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Color Schemes */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <Paintbrush className="h-4 w-4" />
            Color Scheme
          </CardTitle>
          <CardDescription>
            Choose from preset color schemes or create your own
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Color Presets */}
          <div className="grid grid-cols-2 gap-3">
            {COLOR_PRESETS.map((preset) => (
              <button
                key={preset.id}
                onClick={() => handleApplyColorPreset(preset)}
                className={cn(
                  "p-3 rounded-lg border text-left transition-all",
                  selectedPreset === preset.id
                    ? "border-primary bg-primary/5"
                    : "border-border hover:border-primary/50"
                )}
              >
                <div className="flex items-center justify-between mb-2">
                  <span className="font-medium">{preset.name}</span>
                  <div className="flex gap-1">
                    <div 
                      className="w-3 h-3 rounded-full border"
                      style={{ backgroundColor: preset.colors.primary }}
                    />
                    <div 
                      className="w-3 h-3 rounded-full border"
                      style={{ backgroundColor: preset.colors.accent }}
                    />
                  </div>
                </div>
                <p className="text-xs text-muted-foreground">
                  {preset.description}
                </p>
              </button>
            ))}
          </div>

          <Separator />

          {/* Custom Color Editor Toggle */}
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-sm font-medium">Custom Colors</Label>
              <p className="text-xs text-muted-foreground">
                Fine-tune individual color values
              </p>
            </div>
            <Switch
              checked={showCustomColorEditor}
              onCheckedChange={setShowCustomColorEditor}
            />
          </div>

          {/* Custom Color Editor */}
          {showCustomColorEditor && (
            <div className="space-y-3 pt-2">
              <div className="grid grid-cols-2 gap-3">
                {Object.entries(currentColors).map(([key, value]) => (
                  <div key={key} className="space-y-1">
                    <Label className="text-xs capitalize">
                      {key.replace(/([A-Z])/g, ' $1').trim()}
                    </Label>
                    <div className="flex gap-2">
                      <Input
                        type="color"
                        value={value}
                        onChange={(e) => handleCustomColorChange(key as keyof CustomColorScheme, e.target.value)}
                        className="w-12 h-8 p-1 border rounded"
                      />
                      <Input
                        type="text"
                        value={value}
                        onChange={(e) => handleCustomColorChange(key as keyof CustomColorScheme, e.target.value)}
                        className="flex-1 text-xs font-mono"
                        placeholder="hsl(222, 84%, 55%)"
                      />
                    </div>
                  </div>
                ))}
              </div>
              
              <div className="flex justify-end">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={resetToDefault}
                  className="gap-2"
                >
                  <Sparkles className="h-4 w-4" />
                  Reset to Default
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Typography and Spacing */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <Type className="h-4 w-4" />
            Typography & Spacing
          </CardTitle>
          <CardDescription>
            Control text size and component spacing
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label className="text-sm font-medium">Font Scale</Label>
            <Select 
              value={settings.theme.fontScale} 
              onValueChange={(value: any) => handleThemeUpdate({ fontScale: value })}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="small">Small - Compact text for power users</SelectItem>
                <SelectItem value="normal">Normal - Balanced readability</SelectItem>
                <SelectItem value="large">Large - Enhanced accessibility</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Separator />

          <div className="space-y-2">
            <Label className="text-sm font-medium">Widget Spacing</Label>
            <Select 
              value={settings.theme.widgetSpacing} 
              onValueChange={(value: any) => handleThemeUpdate({ widgetSpacing: value })}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="tight">Tight - Minimal gaps between widgets</SelectItem>
                <SelectItem value="normal">Normal - Balanced spacing</SelectItem>
                <SelectItem value="relaxed">Relaxed - Generous whitespace</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Separator />

          <div className="space-y-2">
            <Label className="text-sm font-medium">Border Radius</Label>
            <div className="grid grid-cols-4 gap-2">
              {[
                { value: 'none', label: 'None', radius: '0px' },
                { value: 'small', label: 'Small', radius: '4px' },
                { value: 'medium', label: 'Medium', radius: '8px' },
                { value: 'large', label: 'Large', radius: '12px' },
              ].map((option) => (
                <button
                  key={option.value}
                  onClick={() => handleThemeUpdate({ borderRadius: option.value as any })}
                  className={cn(
                    "p-3 border rounded text-center transition-all",
                    settings.theme.borderRadius === option.value
                      ? "border-primary bg-primary/5"
                      : "border-border hover:border-primary/50"
                  )}
                  style={{ borderRadius: option.radius }}
                >
                  <div className="text-xs font-medium">{option.label}</div>
                  <div className="text-xs text-muted-foreground mt-1">
                    {option.radius}
                  </div>
                </button>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Visual Effects */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <Eye className="h-4 w-4" />
            Visual Effects
          </CardTitle>
          <CardDescription>
            Control animations and visual enhancements
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-sm font-medium">Enable Animations</Label>
              <p className="text-xs text-muted-foreground">
                Smooth transitions and hover effects
              </p>
            </div>
            <Switch
              checked={settings.theme.animationsEnabled}
              onCheckedChange={(enabled) => handleThemeUpdate({ animationsEnabled: enabled })}
            />
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-sm font-medium">Compact Mode</Label>
              <p className="text-xs text-muted-foreground">
                Reduces padding and spacing throughout the interface
              </p>
            </div>
            <Switch
              checked={settings.theme.compactMode}
              onCheckedChange={(enabled) => handleThemeUpdate({ compactMode: enabled })}
            />
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-sm font-medium">Show Widget Titles</Label>
              <p className="text-xs text-muted-foreground">
                Display titles at the top of each widget
              </p>
            </div>
            <Switch
              checked={settings.theme.showWidgetTitles}
              onCheckedChange={(enabled) => handleThemeUpdate({ showWidgetTitles: enabled })}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
};