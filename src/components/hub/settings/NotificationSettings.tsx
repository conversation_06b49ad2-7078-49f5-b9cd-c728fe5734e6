import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import { Bell, Volume2, Mail, Smartphone, Clock } from 'lucide-react';
import type { HubSettings, NotificationSettings as NotificationSettingsType } from '@/types/hub';

interface NotificationSettingsProps {
  settings: HubSettings;
  onUpdate: (updates: Partial<HubSettings>) => void;
}

export const NotificationSettings: React.FC<NotificationSettingsProps> = ({
  settings,
  onUpdate,
}) => {
  const handleNotificationUpdate = (notificationUpdates: Partial<NotificationSettingsType>) => {
    onUpdate({
      notifications: { ...settings.notifications, ...notificationUpdates }
    });
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold">Notification Settings</h3>
        <p className="text-sm text-muted-foreground">
          Control when and how you receive notifications from the dashboard.
        </p>
      </div>

      {/* General Notifications */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <Bell className="h-4 w-4" />
            General Notifications
          </CardTitle>
          <CardDescription>
            Basic notification preferences
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-sm font-medium">Enable Notifications</Label>
              <p className="text-xs text-muted-foreground">
                Turn all notifications on or off
              </p>
            </div>
            <Switch
              checked={settings.notifications.enabled}
              onCheckedChange={(enabled) => handleNotificationUpdate({ enabled })}
            />
          </div>

          {settings.notifications.enabled && (
            <>
              <Separator />

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label className="text-sm font-medium">Update Notifications</Label>
                  <p className="text-xs text-muted-foreground">
                    Get notified when widgets update with new content
                  </p>
                </div>
                <Switch
                  checked={settings.notifications.updateNotifications}
                  onCheckedChange={(enabled) => handleNotificationUpdate({ updateNotifications: enabled })}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label className="text-sm font-medium">Error Notifications</Label>
                  <p className="text-xs text-muted-foreground">
                    Get notified when there are errors loading data
                  </p>
                </div>
                <Switch
                  checked={settings.notifications.errorNotifications}
                  onCheckedChange={(enabled) => handleNotificationUpdate({ errorNotifications: enabled })}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label className="text-sm font-medium">Achievement Notifications</Label>
                  <p className="text-xs text-muted-foreground">
                    Celebrate coding milestones and achievements
                  </p>
                </div>
                <Switch
                  checked={settings.notifications.achievementNotifications}
                  onCheckedChange={(enabled) => handleNotificationUpdate({ achievementNotifications: enabled })}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label className="text-sm font-medium">Weekly Digest</Label>
                  <p className="text-xs text-muted-foreground">
                    Get a weekly summary of your activity and popular content
                  </p>
                </div>
                <Switch
                  checked={settings.notifications.weeklyDigest}
                  onCheckedChange={(enabled) => handleNotificationUpdate({ weeklyDigest: enabled })}
                />
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Delivery Methods */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Delivery Methods</CardTitle>
          <CardDescription>
            Choose how you want to receive notifications
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5 flex items-center gap-2">
              <Mail className="h-4 w-4 text-muted-foreground" />
              <div>
                <Label className="text-sm font-medium">Email Notifications</Label>
                <p className="text-xs text-muted-foreground">
                  Receive notifications via email
                </p>
              </div>
            </div>
            <Switch
              checked={settings.notifications.emailNotifications}
              onCheckedChange={(enabled) => handleNotificationUpdate({ emailNotifications: enabled })}
              disabled={!settings.notifications.enabled}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5 flex items-center gap-2">
              <Smartphone className="h-4 w-4 text-muted-foreground" />
              <div>
                <Label className="text-sm font-medium">Push Notifications</Label>
                <p className="text-xs text-muted-foreground">
                  Browser push notifications
                </p>
              </div>
            </div>
            <Switch
              checked={settings.notifications.pushNotifications}
              onCheckedChange={(enabled) => handleNotificationUpdate({ pushNotifications: enabled })}
              disabled={!settings.notifications.enabled}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5 flex items-center gap-2">
              <Volume2 className="h-4 w-4 text-muted-foreground" />
              <div>
                <Label className="text-sm font-medium">Notification Sound</Label>
                <p className="text-xs text-muted-foreground">
                  Play sound when notifications arrive
                </p>
              </div>
            </div>
            <Switch
              checked={settings.notifications.notificationSound}
              onCheckedChange={(enabled) => handleNotificationUpdate({ notificationSound: enabled })}
              disabled={!settings.notifications.enabled}
            />
          </div>
        </CardContent>
      </Card>

      {/* Quiet Hours */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <Clock className="h-4 w-4" />
            Quiet Hours
          </CardTitle>
          <CardDescription>
            Set times when you don't want to receive notifications
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-sm font-medium">Enable Quiet Hours</Label>
              <p className="text-xs text-muted-foreground">
                Automatically silence notifications during specified hours
              </p>
            </div>
            <Switch
              checked={settings.notifications.quietHours?.enabled || false}
              onCheckedChange={(enabled) => handleNotificationUpdate({
                quietHours: {
                  enabled,
                  start: settings.notifications.quietHours?.start || '22:00',
                  end: settings.notifications.quietHours?.end || '08:00'
                }
              })}
              disabled={!settings.notifications.enabled}
            />
          </div>

          {settings.notifications.quietHours?.enabled && (
            <>
              <Separator />
              
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Start Time</Label>
                  <Input
                    type="time"
                    value={settings.notifications.quietHours?.start || '22:00'}
                    onChange={(e) => handleNotificationUpdate({
                      quietHours: {
                        enabled: true,
                        start: e.target.value,
                        end: settings.notifications.quietHours?.end || '08:00'
                      }
                    })}
                  />
                </div>
                <div className="space-y-2">
                  <Label className="text-sm font-medium">End Time</Label>
                  <Input
                    type="time"
                    value={settings.notifications.quietHours?.end || '08:00'}
                    onChange={(e) => handleNotificationUpdate({
                      quietHours: {
                        enabled: true,
                        start: settings.notifications.quietHours?.start || '22:00',
                        end: e.target.value
                      }
                    })}
                  />
                </div>
              </div>
              
              <p className="text-xs text-muted-foreground">
                Notifications will be silenced between these hours in your local timezone
              </p>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
};