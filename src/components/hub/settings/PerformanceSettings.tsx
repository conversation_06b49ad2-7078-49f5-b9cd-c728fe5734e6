import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import { Database, Clock, Cpu } from 'lucide-react';
import type { HubSettings, PerformanceSettings as PerformanceSettingsType } from '@/types/hub';

interface PerformanceSettingsProps {
  settings: HubSettings;
  onUpdate: (updates: Partial<HubSettings>) => void;
}

export const PerformanceSettings: React.FC<PerformanceSettingsProps> = ({
  settings,
  onUpdate,
}) => {
  const handlePerformanceUpdate = (performanceUpdates: Partial<PerformanceSettingsType>) => {
    onUpdate({
      performance: { ...settings.performance, ...performanceUpdates }
    });
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold">Performance Settings</h3>
        <p className="text-sm text-muted-foreground">
          Optimize dashboard performance and resource usage.
        </p>
      </div>

      {/* Cache Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <Database className="h-4 w-4" />
            Cache Settings
          </CardTitle>
          <CardDescription>
            Control how data is cached to improve performance
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label className="text-sm font-medium">Cache Size Limit</Label>
            <div className="space-y-2">
              <Slider
                value={[settings.performance.cacheSize]}
                onValueChange={([value]) => handlePerformanceUpdate({ cacheSize: value })}
                min={10}
                max={200}
                step={10}
                className="w-full"
              />
              <div className="flex items-center justify-between text-xs text-muted-foreground">
                <span>10 MB</span>
                <span>Current: {settings.performance.cacheSize} MB</span>
                <span>200 MB</span>
              </div>
            </div>
            <p className="text-xs text-muted-foreground">
              Maximum amount of data to cache locally
            </p>
          </div>

          <Separator />

          <div className="space-y-2">
            <Label className="text-sm font-medium">Cache Expiry Time</Label>
            <div className="space-y-2">
              <Slider
                value={[settings.performance.maxCacheAge]}
                onValueChange={([value]) => handlePerformanceUpdate({ maxCacheAge: value })}
                min={5}
                max={120}
                step={5}
                className="w-full"
              />
              <div className="flex items-center justify-between text-xs text-muted-foreground">
                <span>5 min</span>
                <span>Current: {settings.performance.maxCacheAge} minutes</span>
                <span>2 hours</span>
              </div>
            </div>
            <p className="text-xs text-muted-foreground">
              How long cached data remains valid
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Loading Optimizations */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <Clock className="h-4 w-4" />
            Loading Optimizations
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-sm font-medium">Preload Widgets</Label>
              <p className="text-xs text-muted-foreground">
                Load widget data in the background for faster display
              </p>
            </div>
            <Switch
              checked={settings.performance.preloadWidgets}
              onCheckedChange={(enabled) => handlePerformanceUpdate({ preloadWidgets: enabled })}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-sm font-medium">Background Refresh</Label>
              <p className="text-xs text-muted-foreground">
                Continue refreshing data when app is in background
              </p>
            </div>
            <Switch
              checked={settings.performance.backgroundRefresh}
              onCheckedChange={(enabled) => handlePerformanceUpdate({ backgroundRefresh: enabled })}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-sm font-medium">Enable Virtualization</Label>
              <p className="text-xs text-muted-foreground">
                Use virtual scrolling for large lists (experimental)
              </p>
            </div>
            <Switch
              checked={settings.performance.enableVirtualization}
              onCheckedChange={(enabled) => handlePerformanceUpdate({ enableVirtualization: enabled })}
            />
          </div>
        </CardContent>
      </Card>

      {/* Network Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Network Settings</CardTitle>
          <CardDescription>
            Control network requests and timeouts
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label className="text-sm font-medium">Max Concurrent Requests</Label>
            <div className="flex items-center gap-2">
              <Input
                type="number"
                min="1"
                max="10"
                value={settings.performance.maxConcurrentRequests}
                onChange={(e) => handlePerformanceUpdate({ 
                  maxConcurrentRequests: parseInt(e.target.value) || 5 
                })}
                className="w-20"
              />
              <span className="text-sm text-muted-foreground">simultaneous requests</span>
            </div>
            <p className="text-xs text-muted-foreground">
              Limit parallel API requests to avoid overwhelming services
            </p>
          </div>

          <Separator />

          <div className="space-y-2">
            <Label className="text-sm font-medium">Request Timeout</Label>
            <div className="flex items-center gap-2">
              <Input
                type="number"
                min="5"
                max="60"
                value={settings.performance.requestTimeout}
                onChange={(e) => handlePerformanceUpdate({ 
                  requestTimeout: parseInt(e.target.value) || 10 
                })}
                className="w-20"
              />
              <span className="text-sm text-muted-foreground">seconds</span>
            </div>
            <p className="text-xs text-muted-foreground">
              How long to wait for API responses before timing out
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Visual Performance */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <Cpu className="h-4 w-4" />
            Visual Performance
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-sm font-medium">Lazy Load Images</Label>
              <p className="text-xs text-muted-foreground">
                Load images only when they come into view
              </p>
            </div>
            <Switch
              checked={settings.performance.lazyLoadImages}
              onCheckedChange={(enabled) => handlePerformanceUpdate({ lazyLoadImages: enabled })}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-sm font-medium">Reduce Animations</Label>
              <p className="text-xs text-muted-foreground">
                Minimize animations to improve performance on slower devices
              </p>
            </div>
            <Switch
              checked={settings.performance.reduceAnimations}
              onCheckedChange={(enabled) => handlePerformanceUpdate({ reduceAnimations: enabled })}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-sm font-medium">Compressed Mode</Label>
              <p className="text-xs text-muted-foreground">
                Use lower quality images and reduced visual effects
              </p>
            </div>
            <Switch
              checked={settings.performance.compressedMode}
              onCheckedChange={(enabled) => handlePerformanceUpdate({ compressedMode: enabled })}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
};