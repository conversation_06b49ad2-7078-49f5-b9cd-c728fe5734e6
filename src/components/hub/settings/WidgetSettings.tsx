import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { 
  Settings,
  Hash as RedditIcon,
  Github,
  TrendingUp,
  BookOpen,
  Brain,
  Plus,
  X,
  Clock,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useHubStore } from '@/stores/hubStore';
import type { HubSettings, HubWidget } from '@/types/hub';

interface WidgetSettingsProps {
  settings: HubSettings;
  onUpdate: (updates: Partial<HubSettings>) => void;
}

interface WidgetTypeConfig {
  type: HubWidget['type'];
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  description: string;
}

const WIDGET_TYPES: WidgetTypeConfig[] = [
  {
    type: 'reddit',
    label: 'Reddit Feed',
    icon: RedditIcon,
    description: 'Posts from selected subreddits',
  },
  {
    type: 'github-trending',
    label: 'GitHub Trending',
    icon: Github,
    description: 'Trending repositories and developers',
  },
  {
    type: 'coding-progress',
    label: 'Coding Progress',
    icon: TrendingUp,
    description: 'Development activity tracking',
  },
  {
    type: 'ai-news',
    label: 'AI News',
    icon: Brain,
    description: 'Latest AI and ML news',
  },
  {
    type: 'learning',
    label: 'Learning Resources',
    icon: BookOpen,
    description: 'Courses and educational content',
  },
];

export const WidgetSettings: React.FC<WidgetSettingsProps> = ({
  settings,
  onUpdate,
}) => {
  const [selectedWidgetType, setSelectedWidgetType] = useState<HubWidget['type']>('reddit');
  const [newSubreddit, setNewSubreddit] = useState('');
  const [newKeyword, setNewKeyword] = useState('');

  const { widgets } = useHubStore();

  const handleWidgetSettingUpdate = (
    widgetType: HubWidget['type'],
    settingUpdates: Record<string, any>
  ) => {
    const currentWidgetSettings = settings.widgetSettings[widgetType] || {};
    const updatedWidgetSettings = {
      ...settings.widgetSettings,
      [widgetType]: { ...currentWidgetSettings, ...settingUpdates }
    };
    onUpdate({ widgetSettings: updatedWidgetSettings });
  };

  const getWidgetSettings = (widgetType: HubWidget['type']) => {
    return settings.widgetSettings[widgetType] || {};
  };

  const renderRedditSettings = () => {
    const redditSettings = getWidgetSettings('reddit');
    const subreddits = redditSettings.subreddits || ['programming', 'MachineLearning'];
    
    return (
      <div className="space-y-4">
        {/* Subreddits */}
        <div className="space-y-2">
          <Label className="text-sm font-medium">Subreddits</Label>
          <div className="flex gap-2">
            <Input
              placeholder="Enter subreddit name..."
              value={newSubreddit}
              onChange={(e) => setNewSubreddit(e.target.value)}
              onKeyPress={(e) => {
                if (e.key === 'Enter' && newSubreddit.trim()) {
                  const updated = [...subreddits, newSubreddit.trim()];
                  handleWidgetSettingUpdate('reddit', { subreddits: updated });
                  setNewSubreddit('');
                }
              }}
            />
            <Button
              onClick={() => {
                if (newSubreddit.trim()) {
                  const updated = [...subreddits, newSubreddit.trim()];
                  handleWidgetSettingUpdate('reddit', { subreddits: updated });
                  setNewSubreddit('');
                }
              }}
              disabled={!newSubreddit.trim()}
            >
              <Plus className="h-4 w-4" />
            </Button>
          </div>
          <div className="flex flex-wrap gap-2">
            {subreddits.map((subreddit: string, index: number) => (
              <Badge key={index} variant="secondary" className="gap-1">
                r/{subreddit}
                <button
                  onClick={() => {
                    const updated = subreddits.filter((_: string, i: number) => i !== index);
                    handleWidgetSettingUpdate('reddit', { subreddits: updated });
                  }}
                  className="ml-1 hover:text-destructive"
                >
                  <X className="h-3 w-3" />
                </button>
              </Badge>
            ))}
          </div>
        </div>

        <Separator />

        {/* Post Limit */}
        <div className="space-y-2">
          <Label className="text-sm font-medium">Posts to Show</Label>
          <Select 
            value={(redditSettings.postLimit || 10).toString()}
            onValueChange={(value) => handleWidgetSettingUpdate('reddit', { postLimit: parseInt(value) })}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="5">5 posts</SelectItem>
              <SelectItem value="10">10 posts</SelectItem>
              <SelectItem value="15">15 posts</SelectItem>
              <SelectItem value="20">20 posts</SelectItem>
              <SelectItem value="25">25 posts</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <Separator />

        {/* Sort By */}
        <div className="space-y-2">
          <Label className="text-sm font-medium">Sort Posts By</Label>
          <Select 
            value={redditSettings.sortBy || 'hot'}
            onValueChange={(value) => handleWidgetSettingUpdate('reddit', { sortBy: value })}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="hot">Hot - Most popular right now</SelectItem>
              <SelectItem value="new">New - Recently posted</SelectItem>
              <SelectItem value="top">Top - Highest rated</SelectItem>
              <SelectItem value="rising">Rising - Gaining popularity</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {(redditSettings.sortBy === 'top') && (
          <div className="space-y-2">
            <Label className="text-sm font-medium">Time Range</Label>
            <Select 
              value={redditSettings.timeRange || 'day'}
              onValueChange={(value) => handleWidgetSettingUpdate('reddit', { timeRange: value })}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="hour">Last Hour</SelectItem>
                <SelectItem value="day">Last Day</SelectItem>
                <SelectItem value="week">Last Week</SelectItem>
                <SelectItem value="month">Last Month</SelectItem>
                <SelectItem value="year">Last Year</SelectItem>
                <SelectItem value="all">All Time</SelectItem>
              </SelectContent>
            </Select>
          </div>
        )}
      </div>
    );
  };

  const renderGitHubSettings = () => {
    const githubSettings = getWidgetSettings('github-trending');
    
    return (
      <div className="space-y-4">
        {/* Time Range */}
        <div className="space-y-2">
          <Label className="text-sm font-medium">Trending Time Range</Label>
          <Select 
            value={githubSettings.timeRange || 'weekly'}
            onValueChange={(value) => handleWidgetSettingUpdate('github-trending', { timeRange: value })}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="daily">Daily - Last 24 hours</SelectItem>
              <SelectItem value="weekly">Weekly - Last 7 days</SelectItem>
              <SelectItem value="monthly">Monthly - Last 30 days</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <Separator />

        {/* Language Filter */}
        <div className="space-y-2">
          <Label className="text-sm font-medium">Programming Language</Label>
          <Select 
            value={githubSettings.language || 'all'}
            onValueChange={(value) => handleWidgetSettingUpdate('github-trending', { language: value })}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Languages</SelectItem>
              <SelectItem value="javascript">JavaScript</SelectItem>
              <SelectItem value="typescript">TypeScript</SelectItem>
              <SelectItem value="python">Python</SelectItem>
              <SelectItem value="java">Java</SelectItem>
              <SelectItem value="go">Go</SelectItem>
              <SelectItem value="rust">Rust</SelectItem>
              <SelectItem value="cpp">C++</SelectItem>
              <SelectItem value="swift">Swift</SelectItem>
              <SelectItem value="kotlin">Kotlin</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <Separator />

        {/* Repository Limit */}
        <div className="space-y-2">
          <Label className="text-sm font-medium">Repositories to Show</Label>
          <Select 
            value={(githubSettings.repoLimit || 10).toString()}
            onValueChange={(value) => handleWidgetSettingUpdate('github-trending', { repoLimit: parseInt(value) })}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="5">5 repositories</SelectItem>
              <SelectItem value="10">10 repositories</SelectItem>
              <SelectItem value="15">15 repositories</SelectItem>
              <SelectItem value="20">20 repositories</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <Separator />

        {/* Display Options */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Display Options</Label>
          
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-sm">Show Stars Added Today</Label>
              <p className="text-xs text-muted-foreground">Display daily star count increase</p>
            </div>
            <Switch
              checked={githubSettings.includeStarsToday ?? true}
              onCheckedChange={(checked) => handleWidgetSettingUpdate('github-trending', { includeStarsToday: checked })}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-sm">Show Topics</Label>
              <p className="text-xs text-muted-foreground">Display repository topics/tags</p>
            </div>
            <Switch
              checked={githubSettings.showTopics ?? true}
              onCheckedChange={(checked) => handleWidgetSettingUpdate('github-trending', { showTopics: checked })}
            />
          </div>
        </div>
      </div>
    );
  };

  const renderAINewsSettings = () => {
    const aiNewsSettings = getWidgetSettings('ai-news');
    const keywords = aiNewsSettings.keywords || ['ai', 'machine learning', 'llm'];
    
    return (
      <div className="space-y-4">
        {/* News Sources */}
        <div className="space-y-2">
          <Label className="text-sm font-medium">News Sources</Label>
          <div className="space-y-2">
            {[
              { id: 'hackernews', label: 'Hacker News', description: 'Tech news and discussions' },
              { id: 'arxiv', label: 'arXiv', description: 'Academic papers and research' },
              { id: 'aiweekly', label: 'AI Weekly', description: 'Curated AI newsletters' },
              { id: 'mit-news', label: 'MIT News', description: 'Research from MIT' },
              { id: 'openai-blog', label: 'OpenAI Blog', description: 'Updates from OpenAI' },
            ].map((source) => (
              <div key={source.id} className="flex items-center justify-between p-2 rounded border">
                <div className="space-y-0.5">
                  <Label className="text-sm">{source.label}</Label>
                  <p className="text-xs text-muted-foreground">{source.description}</p>
                </div>
                <Switch
                  checked={(aiNewsSettings.sources || ['hackernews', 'arxiv']).includes(source.id)}
                  onCheckedChange={(checked) => {
                    const currentSources = aiNewsSettings.sources || ['hackernews', 'arxiv'];
                    const updatedSources = checked
                      ? [...currentSources, source.id]
                      : currentSources.filter((s: string) => s !== source.id);
                    handleWidgetSettingUpdate('ai-news', { sources: updatedSources });
                  }}
                />
              </div>
            ))}
          </div>
        </div>

        <Separator />

        {/* Keywords */}
        <div className="space-y-2">
          <Label className="text-sm font-medium">Keywords</Label>
          <div className="flex gap-2">
            <Input
              placeholder="Add keyword..."
              value={newKeyword}
              onChange={(e) => setNewKeyword(e.target.value)}
              onKeyPress={(e) => {
                if (e.key === 'Enter' && newKeyword.trim()) {
                  const updated = [...keywords, newKeyword.trim()];
                  handleWidgetSettingUpdate('ai-news', { keywords: updated });
                  setNewKeyword('');
                }
              }}
            />
            <Button
              onClick={() => {
                if (newKeyword.trim()) {
                  const updated = [...keywords, newKeyword.trim()];
                  handleWidgetSettingUpdate('ai-news', { keywords: updated });
                  setNewKeyword('');
                }
              }}
              disabled={!newKeyword.trim()}
            >
              <Plus className="h-4 w-4" />
            </Button>
          </div>
          <div className="flex flex-wrap gap-2">
            {keywords.map((keyword: string, index: number) => (
              <Badge key={index} variant="secondary" className="gap-1">
                {keyword}
                <button
                  onClick={() => {
                    const updated = keywords.filter((_: string, i: number) => i !== index);
                    handleWidgetSettingUpdate('ai-news', { keywords: updated });
                  }}
                  className="ml-1 hover:text-destructive"
                >
                  <X className="h-3 w-3" />
                </button>
              </Badge>
            ))}
          </div>
        </div>

        <Separator />

        {/* Article Limit */}
        <div className="space-y-2">
          <Label className="text-sm font-medium">Articles to Show</Label>
          <Select 
            value={(aiNewsSettings.itemLimit || 10).toString()}
            onValueChange={(value) => handleWidgetSettingUpdate('ai-news', { itemLimit: parseInt(value) })}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="5">5 articles</SelectItem>
              <SelectItem value="10">10 articles</SelectItem>
              <SelectItem value="15">15 articles</SelectItem>
              <SelectItem value="20">20 articles</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
    );
  };

  const renderWidgetSettings = () => {
    switch (selectedWidgetType) {
      case 'reddit':
        return renderRedditSettings();
      case 'github-trending':
        return renderGitHubSettings();
      case 'ai-news':
        return renderAINewsSettings();
      case 'coding-progress':
        return (
          <div className="space-y-4">
            <p className="text-sm text-muted-foreground">
              Coding progress settings are automatically configured based on your development activity.
              Additional configuration options will be available in future updates.
            </p>
          </div>
        );
      case 'learning':
        return (
          <div className="space-y-4">
            <p className="text-sm text-muted-foreground">
              Learning resource settings will be available soon. The widget currently shows
              curated content based on your profile preferences.
            </p>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold">Widget Settings</h3>
        <p className="text-sm text-muted-foreground">
          Configure individual widget behavior and display options.
        </p>
      </div>

      {/* Widget Type Selector */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Select Widget Type</CardTitle>
          <CardDescription>
            Choose a widget to configure its specific settings
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {WIDGET_TYPES.map((widgetType) => {
              const Icon = widgetType.icon;
              const hasActiveWidget = widgets.some(w => w.type === widgetType.type);
              
              return (
                <button
                  key={widgetType.type}
                  onClick={() => setSelectedWidgetType(widgetType.type)}
                  className={cn(
                    "p-3 rounded-lg border text-left transition-all",
                    selectedWidgetType === widgetType.type
                      ? "border-primary bg-primary/5"
                      : "border-border hover:border-primary/50"
                  )}
                >
                  <div className="flex items-center gap-3">
                    <div className={cn(
                      "p-2 rounded border",
                      hasActiveWidget ? "bg-green-100 border-green-300" : "bg-muted"
                    )}>
                      <Icon className="h-4 w-4" />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{widgetType.label}</span>
                        {hasActiveWidget && (
                          <Badge variant="outline" className="text-xs bg-green-50">
                            Active
                          </Badge>
                        )}
                      </div>
                      <p className="text-xs text-muted-foreground">
                        {widgetType.description}
                      </p>
                    </div>
                  </div>
                </button>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Widget-Specific Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <Settings className="h-4 w-4" />
            {WIDGET_TYPES.find(w => w.type === selectedWidgetType)?.label} Settings
          </CardTitle>
          <CardDescription>
            Configure how this widget behaves and what content it displays
          </CardDescription>
        </CardHeader>
        <CardContent>
          {renderWidgetSettings()}
        </CardContent>
      </Card>

      {/* Global Widget Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <Clock className="h-4 w-4" />
            Global Widget Settings
          </CardTitle>
          <CardDescription>
            Settings that apply to all widgets
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-sm font-medium">Auto Refresh</Label>
              <p className="text-xs text-muted-foreground">
                Automatically refresh widget data
              </p>
            </div>
            <Switch
              checked={settings.autoRefresh}
              onCheckedChange={(checked) => onUpdate({ autoRefresh: checked })}
            />
          </div>

          {settings.autoRefresh && (
            <>
              <Separator />
              <div className="space-y-2">
                <Label className="text-sm font-medium">Default Refresh Interval</Label>
                <Select 
                  value={settings.refreshInterval.toString()}
                  onValueChange={(value) => onUpdate({ refreshInterval: parseInt(value) })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="15">15 minutes</SelectItem>
                    <SelectItem value="30">30 minutes</SelectItem>
                    <SelectItem value="60">1 hour</SelectItem>
                    <SelectItem value="120">2 hours</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
};