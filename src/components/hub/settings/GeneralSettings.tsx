import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

import { Separator } from '@/components/ui/separator';
import type { HubSettings } from '@/types/hub';

interface GeneralSettingsProps {
  settings: HubSettings;
  onUpdate: (updates: Partial<HubSettings>) => void;
}

export const GeneralSettings: React.FC<GeneralSettingsProps> = ({
  settings,
  onUpdate,
}) => {
  const handleAutoRefreshChange = (enabled: boolean) => {
    onUpdate({ autoRefresh: enabled });
  };

  const handleRefreshIntervalChange = (interval: string) => {
    onUpdate({ refreshInterval: parseInt(interval) });
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold">General Settings</h3>
        <p className="text-sm text-muted-foreground">
          Configure basic dashboard behavior and refresh settings.
        </p>
      </div>

      {/* Auto Refresh Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Auto Refresh</CardTitle>
          <CardDescription>
            Automatically refresh widget data at regular intervals
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="auto-refresh" className="text-sm font-medium">
                Enable Auto Refresh
              </Label>
              <p className="text-xs text-muted-foreground">
                Automatically update widget data in the background
              </p>
            </div>
            <Switch
              id="auto-refresh"
              checked={settings.autoRefresh}
              onCheckedChange={handleAutoRefreshChange}
            />
          </div>

          {settings.autoRefresh && (
            <>
              <Separator />
              <div className="space-y-2">
                <Label htmlFor="refresh-interval" className="text-sm font-medium">
                  Refresh Interval
                </Label>
                <Select 
                  value={settings.refreshInterval.toString()} 
                  onValueChange={handleRefreshIntervalChange}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="5">5 minutes</SelectItem>
                    <SelectItem value="15">15 minutes</SelectItem>
                    <SelectItem value="30">30 minutes</SelectItem>
                    <SelectItem value="60">1 hour</SelectItem>
                    <SelectItem value="120">2 hours</SelectItem>
                    <SelectItem value="240">4 hours</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-xs text-muted-foreground">
                  How often to refresh all widget data automatically
                </p>
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Widget Management */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Widget Management</CardTitle>
          <CardDescription>
            Configure which widgets are enabled by default
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-3">
            {[
              { id: 'reddit', label: 'Reddit Feed', description: 'Show Reddit posts from selected subreddits' },
              { id: 'github-trending', label: 'GitHub Trending', description: 'Display trending repositories and developers' },
              { id: 'coding-progress', label: 'Coding Progress', description: 'Track coding activity and progress metrics' },
              { id: 'ai-news', label: 'AI News', description: 'Latest news and updates from AI sources' },
              { id: 'learning', label: 'Learning Resources', description: 'Recommended courses and tutorials' },
            ].map((widget) => (
              <div key={widget.id} className="flex items-center justify-between py-2">
                <div className="space-y-0.5">
                  <Label className="text-sm font-medium">{widget.label}</Label>
                  <p className="text-xs text-muted-foreground">{widget.description}</p>
                </div>
                <Switch
                  checked={settings.enabledWidgets.includes(widget.id)}
                  onCheckedChange={(enabled) => {
                    const updatedEnabledWidgets = enabled
                      ? [...settings.enabledWidgets, widget.id]
                      : settings.enabledWidgets.filter(id => id !== widget.id);
                    onUpdate({ enabledWidgets: updatedEnabledWidgets });
                  }}
                />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Display Preferences */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Display Preferences</CardTitle>
          <CardDescription>
            Basic appearance and behavior settings
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-sm font-medium">Show Widget Titles</Label>
              <p className="text-xs text-muted-foreground">
                Display titles on all widgets
              </p>
            </div>
            <Switch
              checked={settings.theme.showWidgetTitles}
              onCheckedChange={(enabled) => 
                onUpdate({ 
                  theme: { ...settings.theme, showWidgetTitles: enabled } 
                })
              }
            />
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-sm font-medium">Enable Animations</Label>
              <p className="text-xs text-muted-foreground">
                Use smooth animations for transitions
              </p>
            </div>
            <Switch
              checked={settings.theme.animationsEnabled}
              onCheckedChange={(enabled) => 
                onUpdate({ 
                  theme: { ...settings.theme, animationsEnabled: enabled } 
                })
              }
            />
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-sm font-medium">Compact Mode</Label>
              <p className="text-xs text-muted-foreground">
                Use smaller spacing and reduced padding
              </p>
            </div>
            <Switch
              checked={settings.theme.compactMode}
              onCheckedChange={(enabled) => 
                onUpdate({ 
                  theme: { ...settings.theme, compactMode: enabled } 
                })
              }
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
};