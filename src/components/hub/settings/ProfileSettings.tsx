import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';

import { 
  User,
  MapPin,
  Clock,
  Code,
  Heart,
  Github,
  Plus,
  X,
  BookOpen
} from 'lucide-react';
import type { HubSettings, UserProfile } from '@/types/hub';

interface ProfileSettingsProps {
  settings: HubSettings;
  onUpdate: (updates: Partial<HubSettings>) => void;
}

const PROGRAMMING_LANGUAGES = [
  'JavaScript', 'TypeScript', 'Python', 'Java', 'Go', 'Rust', 'C++', 'C#',
  '<PERSON>', 'Kotlin', 'P<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>lojure', 'Has<PERSON>', 'Dart',
  '<PERSON>xir', 'F#', 'R', 'MATLAB', 'SQL', 'HTML', 'CSS', 'Shell'
];

const SKILLS_SUGGESTIONS = [
  'Frontend Development', 'Backend Development', 'Full Stack', 'Mobile Development',
  'DevOps', 'Cloud Computing', 'Machine Learning', 'Data Science', 'AI/ML',
  'Web Design', 'UI/UX Design', 'Database Design', 'System Architecture',
  'Microservices', 'API Development', 'Testing', 'Security', 'Performance Optimization'
];

const INTERESTS_SUGGESTIONS = [
  'Open Source', 'Artificial Intelligence', 'Machine Learning', 'Web Development',
  'Mobile Apps', 'Game Development', 'Blockchain', 'IoT', 'Cybersecurity',
  'Cloud Computing', 'DevOps', 'Data Science', 'AR/VR', 'Quantum Computing',
  'Robotics', 'Startups', 'Tech News', 'Programming Languages', 'Frameworks'
];

const CONTENT_TYPES = [
  'articles', 'tutorials', 'documentation', 'videos', 'podcasts', 'books',
  'courses', 'workshops', 'conferences', 'newsletters', 'blogs', 'papers'
];

const DIFFICULTY_LEVELS = ['beginner', 'intermediate', 'advanced', 'expert'];

export const ProfileSettings: React.FC<ProfileSettingsProps> = ({
  settings,
  onUpdate,
}) => {
  const [newSkill, setNewSkill] = useState('');
  const [newInterest, setNewInterest] = useState('');

  const handleProfileUpdate = (profileUpdates: Partial<UserProfile>) => {
    onUpdate({
      userProfile: { ...settings.userProfile, ...profileUpdates }
    });
  };

  const handlePreferencesUpdate = (preferencesUpdates: Partial<UserProfile['preferences']>) => {
    handleProfileUpdate({
      preferences: { ...settings.userProfile.preferences, ...preferencesUpdates }
    });
  };

  const addSkill = (skill: string) => {
    if (skill.trim() && !settings.userProfile.skills.includes(skill.trim())) {
      handleProfileUpdate({
        skills: [...settings.userProfile.skills, skill.trim()]
      });
    }
  };

  const removeSkill = (skill: string) => {
    handleProfileUpdate({
      skills: settings.userProfile.skills.filter(s => s !== skill)
    });
  };

  const addInterest = (interest: string) => {
    if (interest.trim() && !settings.userProfile.interests.includes(interest.trim())) {
      handleProfileUpdate({
        interests: [...settings.userProfile.interests, interest.trim()]
      });
    }
  };

  const removeInterest = (interest: string) => {
    handleProfileUpdate({
      interests: settings.userProfile.interests.filter(i => i !== interest)
    });
  };

  const togglePreferredLanguage = (language: string) => {
    const current = settings.userProfile.preferredLanguages || [];
    const updated = current.includes(language)
      ? current.filter(l => l !== language)
      : [...current, language];
    handleProfileUpdate({ preferredLanguages: updated });
  };

  const toggleContentType = (contentType: string) => {
    const current = settings.userProfile.preferences.contentTypes || [];
    const updated = current.includes(contentType)
      ? current.filter(t => t !== contentType)
      : [...current, contentType];
    handlePreferencesUpdate({ contentTypes: updated });
  };

  const toggleDifficultyLevel = (level: string) => {
    const current = settings.userProfile.preferences.difficultyLevels || [];
    const updated = current.includes(level)
      ? current.filter(l => l !== level)
      : [...current, level];
    handlePreferencesUpdate({ difficultyLevels: updated });
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold">Profile Settings</h3>
        <p className="text-sm text-muted-foreground">
          Personalize your profile to get better content recommendations.
        </p>
      </div>

      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <User className="h-4 w-4" />
            Basic Information
          </CardTitle>
          <CardDescription>
            Your personal details and basic profile information
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label className="text-sm font-medium">Display Name</Label>
              <Input
                placeholder="Your name"
                value={settings.userProfile.name || ''}
                onChange={(e) => handleProfileUpdate({ name: e.target.value })}
              />
            </div>

            <div className="space-y-2">
              <Label className="text-sm font-medium">Experience Level</Label>
              <Select 
                value={settings.userProfile.experienceLevel}
                onValueChange={(value: any) => handleProfileUpdate({ experienceLevel: value })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="beginner">Beginner - Learning the basics</SelectItem>
                  <SelectItem value="intermediate">Intermediate - Some experience</SelectItem>
                  <SelectItem value="advanced">Advanced - Experienced developer</SelectItem>
                  <SelectItem value="expert">Expert - Industry veteran</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label className="text-sm font-medium flex items-center gap-2">
              <MapPin className="h-4 w-4" />
              Timezone
            </Label>
            <Select 
              value={settings.userProfile.timezone}
              onValueChange={(value) => handleProfileUpdate({ timezone: value })}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="America/New_York">Eastern Time (ET)</SelectItem>
                <SelectItem value="America/Chicago">Central Time (CT)</SelectItem>
                <SelectItem value="America/Denver">Mountain Time (MT)</SelectItem>
                <SelectItem value="America/Los_Angeles">Pacific Time (PT)</SelectItem>
                <SelectItem value="Europe/London">London (GMT)</SelectItem>
                <SelectItem value="Europe/Berlin">Berlin (CET)</SelectItem>
                <SelectItem value="Asia/Tokyo">Tokyo (JST)</SelectItem>
                <SelectItem value="Asia/Shanghai">Shanghai (CST)</SelectItem>
                <SelectItem value="Australia/Sydney">Sydney (AEST)</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Working Hours */}
          <div className="space-y-2">
            <Label className="text-sm font-medium flex items-center gap-2">
              <Clock className="h-4 w-4" />
              Working Hours (Optional)
            </Label>
            <div className="grid grid-cols-2 gap-2">
              <div>
                <Label className="text-xs">Start Time</Label>
                <Input
                  type="time"
                  value={settings.userProfile.workingHours?.start || '09:00'}
                  onChange={(e) => handleProfileUpdate({
                    workingHours: {
                      ...settings.userProfile.workingHours,
                      start: e.target.value,
                      end: settings.userProfile.workingHours?.end || '17:00',
                      timezone: settings.userProfile.timezone
                    }
                  })}
                />
              </div>
              <div>
                <Label className="text-xs">End Time</Label>
                <Input
                  type="time"
                  value={settings.userProfile.workingHours?.end || '17:00'}
                  onChange={(e) => handleProfileUpdate({
                    workingHours: {
                      ...settings.userProfile.workingHours,
                      start: settings.userProfile.workingHours?.start || '09:00',
                      end: e.target.value,
                      timezone: settings.userProfile.timezone
                    }
                  })}
                />
              </div>
            </div>
            <p className="text-xs text-muted-foreground">
              Used for timing notifications and content recommendations
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Skills */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <Code className="h-4 w-4" />
            Skills & Expertise
          </CardTitle>
          <CardDescription>
            Your technical skills and areas of expertise
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Add Skill */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Add Skills</Label>
            <div className="flex gap-2">
              <Input
                placeholder="Enter a skill..."
                value={newSkill}
                onChange={(e) => setNewSkill(e.target.value)}
                onKeyPress={(e) => {
                  if (e.key === 'Enter' && newSkill.trim()) {
                    addSkill(newSkill);
                    setNewSkill('');
                  }
                }}
              />
              <Button
                onClick={() => {
                  if (newSkill.trim()) {
                    addSkill(newSkill);
                    setNewSkill('');
                  }
                }}
                disabled={!newSkill.trim()}
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>

            {/* Skill Suggestions */}
            <div className="space-y-2">
              <Label className="text-xs text-muted-foreground">Quick Add:</Label>
              <div className="flex flex-wrap gap-1">
                {SKILLS_SUGGESTIONS.filter(skill => 
                  !settings.userProfile.skills.includes(skill)
                ).slice(0, 8).map((skill) => (
                  <Button
                    key={skill}
                    variant="outline"
                    size="sm"
                    onClick={() => addSkill(skill)}
                    className="text-xs h-7"
                  >
                    {skill}
                  </Button>
                ))}
              </div>
            </div>
          </div>

          {/* Current Skills */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Your Skills</Label>
            <div className="flex flex-wrap gap-2">
              {settings.userProfile.skills.map((skill) => (
                <Badge key={skill} variant="secondary" className="gap-1">
                  {skill}
                  <button
                    onClick={() => removeSkill(skill)}
                    className="ml-1 hover:text-destructive"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              ))}
              {settings.userProfile.skills.length === 0 && (
                <p className="text-sm text-muted-foreground">No skills added yet</p>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Programming Languages */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Preferred Programming Languages</CardTitle>
          <CardDescription>
            Languages you work with or want to learn about
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-2">
            {PROGRAMMING_LANGUAGES.map((language) => (
              <button
                key={language}
                onClick={() => togglePreferredLanguage(language)}
                className={
                  "p-2 text-xs rounded border transition-all text-left " +
                  ((settings.userProfile.preferredLanguages || []).includes(language)
                    ? "border-primary bg-primary/10 text-primary"
                    : "border-border hover:border-primary/50")
                }
              >
                {language}
              </button>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Interests */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <Heart className="h-4 w-4" />
            Interests
          </CardTitle>
          <CardDescription>
            Topics and areas you're passionate about
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Add Interest */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Add Interests</Label>
            <div className="flex gap-2">
              <Input
                placeholder="Enter an interest..."
                value={newInterest}
                onChange={(e) => setNewInterest(e.target.value)}
                onKeyPress={(e) => {
                  if (e.key === 'Enter' && newInterest.trim()) {
                    addInterest(newInterest);
                    setNewInterest('');
                  }
                }}
              />
              <Button
                onClick={() => {
                  if (newInterest.trim()) {
                    addInterest(newInterest);
                    setNewInterest('');
                  }
                }}
                disabled={!newInterest.trim()}
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>

            {/* Interest Suggestions */}
            <div className="space-y-2">
              <Label className="text-xs text-muted-foreground">Popular Topics:</Label>
              <div className="flex flex-wrap gap-1">
                {INTERESTS_SUGGESTIONS.filter(interest => 
                  !settings.userProfile.interests.includes(interest)
                ).slice(0, 8).map((interest) => (
                  <Button
                    key={interest}
                    variant="outline"
                    size="sm"
                    onClick={() => addInterest(interest)}
                    className="text-xs h-7"
                  >
                    {interest}
                  </Button>
                ))}
              </div>
            </div>
          </div>

          {/* Current Interests */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Your Interests</Label>
            <div className="flex flex-wrap gap-2">
              {settings.userProfile.interests.map((interest) => (
                <Badge key={interest} variant="secondary" className="gap-1">
                  {interest}
                  <button
                    onClick={() => removeInterest(interest)}
                    className="ml-1 hover:text-destructive"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              ))}
              {settings.userProfile.interests.length === 0 && (
                <p className="text-sm text-muted-foreground">No interests added yet</p>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Content Preferences */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <BookOpen className="h-4 w-4" />
            Content Preferences
          </CardTitle>
          <CardDescription>
            Types of content you prefer to see in your feed
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Content Types */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Preferred Content Types</Label>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
              {CONTENT_TYPES.map((contentType) => (
                <button
                  key={contentType}
                  onClick={() => toggleContentType(contentType)}
                  className={
                    "p-2 text-xs rounded border transition-all text-left capitalize " +
                    ((settings.userProfile.preferences.contentTypes || []).includes(contentType)
                      ? "border-primary bg-primary/10 text-primary"
                      : "border-border hover:border-primary/50")
                  }
                >
                  {contentType}
                </button>
              ))}
            </div>
          </div>

          <Separator />

          {/* Difficulty Levels */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Preferred Difficulty Levels</Label>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
              {DIFFICULTY_LEVELS.map((level) => (
                <button
                  key={level}
                  onClick={() => toggleDifficultyLevel(level)}
                  className={
                    "p-2 text-xs rounded border transition-all text-left capitalize " +
                    ((settings.userProfile.preferences.difficultyLevels || []).includes(level)
                      ? "border-primary bg-primary/10 text-primary"
                      : "border-border hover:border-primary/50")
                  }
                >
                  {level}
                </button>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* GitHub Integration */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <Github className="h-4 w-4" />
            GitHub Integration
          </CardTitle>
          <CardDescription>
            Connect your GitHub account for enhanced features
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between p-3 rounded border">
            <div className="space-y-1">
              <div className="flex items-center gap-2">
                <Github className="h-4 w-4" />
                <span className="font-medium">GitHub Account</span>
                {settings.userProfile.github?.connected && (
                  <Badge variant="secondary" className="text-xs">Connected</Badge>
                )}
              </div>
              <p className="text-xs text-muted-foreground">
                {settings.userProfile.github?.connected 
                  ? `Connected as ${settings.userProfile.github.username}`
                  : 'Connect to get personalized repository recommendations'
                }
              </p>
            </div>
            <Button
              variant={settings.userProfile.github?.connected ? "outline" : "default"}
              size="sm"
            >
              {settings.userProfile.github?.connected ? "Disconnect" : "Connect"}
            </Button>
          </div>

          {!settings.userProfile.github?.connected && (
            <div className="space-y-2">
              <Label className="text-sm font-medium">GitHub Username (Optional)</Label>
              <Input
                placeholder="Your GitHub username"
                value={settings.userProfile.github?.username || ''}
                onChange={(e) => handleProfileUpdate({
                  github: {
                    username: e.target.value,
                    connected: false
                  }
                })}
              />
              <p className="text-xs text-muted-foreground">
                We'll use this to provide better repository recommendations
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};