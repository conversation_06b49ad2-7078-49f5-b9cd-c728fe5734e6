import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Keyboard, Plus, X, RotateCcw } from 'lucide-react';
import type { HubSettings, KeyboardShortcuts, CustomShortcut } from '@/types/hub';

interface KeyboardSettingsProps {
  settings: HubSettings;
  onUpdate: (updates: Partial<HubSettings>) => void;
}

const SHORTCUT_DESCRIPTIONS = {
  'hub.refresh': 'Refresh all widgets',
  'hub.settings': 'Open settings',
  'hub.toggleEdit': 'Toggle edit mode',
  'hub.addWidget': 'Add new widget',
  'hub.search': 'Focus search',
  'hub.help': 'Show help',
  'widget.refresh': 'Refresh focused widget',
  'widget.settings': 'Open widget settings',
  'widget.remove': 'Remove focused widget',
  'widget.duplicate': 'Duplicate focused widget',
};

export const KeyboardSettings: React.FC<KeyboardSettingsProps> = ({
  settings,
  onUpdate,
}) => {
  const [isRecording, setIsRecording] = useState<string | null>(null);
  const [newShortcutName, setNewShortcutName] = useState('');
  const [newShortcutAction, setNewShortcutAction] = useState('');

  const handleKeyboardUpdate = (keyboardUpdates: Partial<KeyboardShortcuts>) => {
    onUpdate({
      keyboardShortcuts: { ...settings.keyboardShortcuts, ...keyboardUpdates }
    });
  };

  const handleShortcutChange = (action: string, newKey: string) => {
    const updatedShortcuts = {
      ...settings.keyboardShortcuts.shortcuts,
      [action]: newKey
    };
    handleKeyboardUpdate({ shortcuts: updatedShortcuts });
  };

  const handleCustomShortcutAdd = () => {
    if (!newShortcutName.trim() || !newShortcutAction.trim()) return;

    const newShortcut: CustomShortcut = {
      id: `custom-${Date.now()}`,
      name: newShortcutName,
      key: '',
      action: newShortcutAction,
      scope: 'global',
      enabled: true,
    };

    const updatedCustomShortcuts = [
      ...settings.keyboardShortcuts.customShortcuts,
      newShortcut
    ];

    handleKeyboardUpdate({ customShortcuts: updatedCustomShortcuts });
    setNewShortcutName('');
    setNewShortcutAction('');
  };

  const handleCustomShortcutUpdate = (id: string, updates: Partial<CustomShortcut>) => {
    const updatedCustomShortcuts = settings.keyboardShortcuts.customShortcuts.map(
      shortcut => shortcut.id === id ? { ...shortcut, ...updates } : shortcut
    );
    handleKeyboardUpdate({ customShortcuts: updatedCustomShortcuts });
  };

  const handleCustomShortcutRemove = (id: string) => {
    const updatedCustomShortcuts = settings.keyboardShortcuts.customShortcuts.filter(
      shortcut => shortcut.id !== id
    );
    handleKeyboardUpdate({ customShortcuts: updatedCustomShortcuts });
  };

  const resetToDefaults = () => {
    const { DEFAULT_KEYBOARD_SHORTCUTS } = require('@/types/hub');
    handleKeyboardUpdate(DEFAULT_KEYBOARD_SHORTCUTS);
  };

  const handleKeyRecord = (action: string, event: React.KeyboardEvent) => {
    event.preventDefault();
    
    const keys = [];
    if (event.ctrlKey) keys.push('ctrl');
    if (event.altKey) keys.push('alt');
    if (event.shiftKey) keys.push('shift');
    if (event.metaKey) keys.push('meta');
    
    if (event.key && !['Control', 'Alt', 'Shift', 'Meta'].includes(event.key)) {
      keys.push(event.key.toLowerCase());
    }
    
    if (keys.length > 0) {
      const shortcut = keys.join('+');
      handleShortcutChange(action, shortcut);
      setIsRecording(null);
    }
  };

  const formatShortcut = (shortcut: string) => {
    return shortcut
      .split('+')
      .map(key => {
        switch (key) {
          case 'ctrl': return '⌃';
          case 'alt': return '⌥';
          case 'shift': return '⇧';
          case 'meta': return '⌘';
          default: return key.toUpperCase();
        }
      })
      .join(' + ');
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold">Keyboard Settings</h3>
        <p className="text-sm text-muted-foreground">
          Configure keyboard shortcuts and hotkeys for faster navigation.
        </p>
      </div>

      {/* General Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <Keyboard className="h-4 w-4" />
            General Settings
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-sm font-medium">Enable Keyboard Shortcuts</Label>
              <p className="text-xs text-muted-foreground">
                Turn keyboard shortcuts on or off globally
              </p>
            </div>
            <Switch
              checked={settings.keyboardShortcuts.enabled}
              onCheckedChange={(enabled) => handleKeyboardUpdate({ enabled })}
            />
          </div>

          <Separator />

          <div className="flex justify-end">
            <Button
              variant="outline"
              size="sm"
              onClick={resetToDefaults}
              className="gap-2"
            >
              <RotateCcw className="h-4 w-4" />
              Reset to Defaults
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Default Shortcuts */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Default Shortcuts</CardTitle>
          <CardDescription>
            Built-in keyboard shortcuts for common actions
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-3">
          {Object.entries(settings.keyboardShortcuts.shortcuts).map(([action, key]) => (
            <div key={action} className="flex items-center justify-between p-2 rounded border">
              <div className="space-y-0.5">
                <Label className="text-sm font-medium">
                  {SHORTCUT_DESCRIPTIONS[action as keyof typeof SHORTCUT_DESCRIPTIONS] || action}
                </Label>
                <p className="text-xs text-muted-foreground">{action}</p>
              </div>
              <div className="flex items-center gap-2">
                {isRecording === action ? (
                  <div className="flex items-center gap-2">
                    <Input
                      placeholder="Press keys..."
                      className="w-32 text-center"
                      onKeyDown={(e) => handleKeyRecord(action, e)}
                      autoFocus
                    />
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setIsRecording(null)}
                    >
                      Cancel
                    </Button>
                  </div>
                ) : (
                  <>
                    <Badge variant="outline" className="font-mono">
                      {formatShortcut(key)}
                    </Badge>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setIsRecording(action)}
                      disabled={!settings.keyboardShortcuts.enabled}
                    >
                      Change
                    </Button>
                  </>
                )}
              </div>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Custom Shortcuts */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Custom Shortcuts</CardTitle>
          <CardDescription>
            Create your own keyboard shortcuts for specific actions
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Add Custom Shortcut */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Add Custom Shortcut</Label>
            <div className="grid grid-cols-2 gap-2">
              <Input
                placeholder="Shortcut name..."
                value={newShortcutName}
                onChange={(e) => setNewShortcutName(e.target.value)}
              />
              <Input
                placeholder="Action/command..."
                value={newShortcutAction}
                onChange={(e) => setNewShortcutAction(e.target.value)}
              />
            </div>
            <Button
              onClick={handleCustomShortcutAdd}
              disabled={!newShortcutName.trim() || !newShortcutAction.trim() || !settings.keyboardShortcuts.enabled}
              className="w-full gap-2"
            >
              <Plus className="h-4 w-4" />
              Add Custom Shortcut
            </Button>
          </div>

          <Separator />

          {/* Custom Shortcuts List */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Your Custom Shortcuts</Label>
            {settings.keyboardShortcuts.customShortcuts.length === 0 ? (
              <p className="text-sm text-muted-foreground py-4 text-center">
                No custom shortcuts created yet
              </p>
            ) : (
              <div className="space-y-2">
                {settings.keyboardShortcuts.customShortcuts.map((shortcut) => (
                  <div key={shortcut.id} className="flex items-center justify-between p-3 rounded border">
                    <div className="space-y-0.5">
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{shortcut.name}</span>
                        <Badge variant="outline" className="text-xs">
                          {shortcut.scope}
                        </Badge>
                        {!shortcut.enabled && (
                          <Badge variant="secondary" className="text-xs">
                            Disabled
                          </Badge>
                        )}
                      </div>
                      <p className="text-xs text-muted-foreground">{shortcut.action}</p>
                      {shortcut.key && (
                        <Badge variant="outline" className="font-mono text-xs">
                          {formatShortcut(shortcut.key)}
                        </Badge>
                      )}
                    </div>
                    <div className="flex items-center gap-1">
                      <Switch
                        checked={shortcut.enabled}
                        onCheckedChange={(enabled) => 
                          handleCustomShortcutUpdate(shortcut.id, { enabled })
                        }
                      />
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setIsRecording(`custom-${shortcut.id}`)}
                        disabled={!shortcut.enabled}
                      >
                        {shortcut.key ? 'Change' : 'Set Key'}
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleCustomShortcutRemove(shortcut.id)}
                        className="text-destructive hover:text-destructive"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Help */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Keyboard Navigation Help</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3 text-sm">
            <div>
              <strong>General Navigation:</strong>
              <ul className="list-disc list-inside text-muted-foreground mt-1 space-y-1">
                <li>Tab / Shift+Tab - Navigate between elements</li>
                <li>Enter / Space - Activate buttons and controls</li>
                <li>Escape - Close dialogs and modals</li>
                <li>Arrow keys - Navigate within components</li>
              </ul>
            </div>
            <div>
              <strong>Widget Navigation:</strong>
              <ul className="list-disc list-inside text-muted-foreground mt-1 space-y-1">
                <li>Focus a widget, then use your custom shortcuts</li>
                <li>Use Tab to move between widget controls</li>
                <li>Enter to activate widget-specific actions</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};