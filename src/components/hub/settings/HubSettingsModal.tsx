import React, { useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Settings,
  Monitor,
  Palette,
  Layout,
  User,
  Bell,
  Shield,
  Zap,
  Accessibility,
  Keyboard,
  Wrench,
  Download,
  Upload,
  RotateCcw,
} from 'lucide-react';
import { Button } from '@/components/ui/button';

import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';

import { cn } from '@/lib/utils';
import { useHubStore } from '@/stores/hubStore';
import type { HubSettings } from '@/types/hub';

// Import setting panels
import { GeneralSettings } from './GeneralSettings';
import { LayoutSettings } from './LayoutSettings';
import { ThemeSettings } from './ThemeSettings';
import { WidgetSettings } from './WidgetSettings';
import { ProfileSettings } from './ProfileSettings';
import { NotificationSettings } from './NotificationSettings';
import { PrivacySettings } from './PrivacySettings';
import { PerformanceSettings } from './PerformanceSettings';
import { AccessibilitySettings } from './AccessibilitySettings';
import { KeyboardSettings } from './KeyboardSettings';
import { AdvancedSettings } from './AdvancedSettings';

interface HubSettingsModalProps {
  isOpen: boolean;
  onClose: () => void;
}

interface SettingsTab {
  id: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  component: React.ComponentType<{
    settings: HubSettings;
    onUpdate: (updates: Partial<HubSettings>) => void;
  }>;
  description: string;
}

const SETTINGS_TABS: SettingsTab[] = [
  {
    id: 'general',
    label: 'General',
    icon: Monitor,
    component: GeneralSettings,
    description: 'Basic dashboard settings and refresh options',
  },
  {
    id: 'layout',
    label: 'Layout',
    icon: Layout,
    component: LayoutSettings,
    description: 'Customize widget layout, grid, and positioning',
  },
  {
    id: 'theme',
    label: 'Theme',
    icon: Palette,
    component: ThemeSettings,
    description: 'Appearance, colors, and visual preferences',
  },
  {
    id: 'widgets',
    label: 'Widgets',
    icon: Settings,
    component: WidgetSettings,
    description: 'Configure individual widget settings',
  },
  {
    id: 'profile',
    label: 'Profile',
    icon: User,
    component: ProfileSettings,
    description: 'Personal information and preferences',
  },
  {
    id: 'notifications',
    label: 'Notifications',
    icon: Bell,
    component: NotificationSettings,
    description: 'Alert preferences and notification settings',
  },
  {
    id: 'privacy',
    label: 'Privacy',
    icon: Shield,
    component: PrivacySettings,
    description: 'Data sharing and privacy controls',
  },
  {
    id: 'performance',
    label: 'Performance',
    icon: Zap,
    component: PerformanceSettings,
    description: 'Cache, loading, and performance options',
  },
  {
    id: 'accessibility',
    label: 'Accessibility',
    icon: Accessibility,
    component: AccessibilitySettings,
    description: 'Accessibility and usability options',
  },
  {
    id: 'keyboard',
    label: 'Keyboard',
    icon: Keyboard,
    component: KeyboardSettings,
    description: 'Keyboard shortcuts and hotkeys',
  },
  {
    id: 'advanced',
    label: 'Advanced',
    icon: Wrench,
    component: AdvancedSettings,
    description: 'Developer options and advanced features',
  },
];

export const HubSettingsModal: React.FC<HubSettingsModalProps> = ({
  isOpen,
  onClose,
}) => {
  const [activeTab, setActiveTab] = useState('general');
  const [hasChanges, setHasChanges] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  const [isExporting, setIsExporting] = useState(false);

  const { settings, updateSettings } = useHubStore();

  const handleSettingsUpdate = useCallback((updates: Partial<HubSettings>) => {
    updateSettings(updates);
    setHasChanges(true);
  }, [updateSettings]);

  const handleExportSettings = async () => {
    setIsExporting(true);
    try {
      const settingsBlob = new Blob(
        [JSON.stringify(settings, null, 2)],
        { type: 'application/json' }
      );
      const url = URL.createObjectURL(settingsBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `claudia-hub-settings-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Failed to export settings:', error);
    } finally {
      setIsExporting(false);
    }
  };

  const handleImportSettings = () => {
    setIsImporting(true);
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = async (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (!file) {
        setIsImporting(false);
        return;
      }

      try {
        const text = await file.text();
        const importedSettings = JSON.parse(text) as HubSettings;
        
        // Validate basic structure
        if (importedSettings && typeof importedSettings === 'object') {
          updateSettings(importedSettings);
          setHasChanges(true);
        } else {
          throw new Error('Invalid settings format');
        }
      } catch (error) {
        console.error('Failed to import settings:', error);
        alert('Failed to import settings. Please check the file format.');
      } finally {
        setIsImporting(false);
      }
    };
    document.body.appendChild(input);
    input.click();
    document.body.removeChild(input);
  };

  const handleResetToDefaults = () => {
    if (confirm('Are you sure you want to reset all settings to defaults? This cannot be undone.')) {
      // Reset to defaults - this would import DEFAULT_HUB_SETTINGS
      const { DEFAULT_HUB_SETTINGS } = require('@/types/hub');
      updateSettings(DEFAULT_HUB_SETTINGS);
      setHasChanges(true);
    }
  };

  const activeTabData = SETTINGS_TABS.find(tab => tab.id === activeTab);
  const ActiveComponent = activeTabData?.component;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl h-[80vh] p-0 gap-0">
        <DialogHeader className="px-6 py-4 border-b bg-card/50">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-gradient-to-br from-blue-500/10 to-purple-500/10">
                <Settings className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <DialogTitle className="text-xl font-semibold">
                  Hub Settings
                </DialogTitle>
                {activeTabData && (
                  <p className="text-sm text-muted-foreground mt-1">
                    {activeTabData.description}
                  </p>
                )}
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              {/* Settings Actions */}
              <Button
                variant="outline"
                size="sm"
                onClick={handleExportSettings}
                disabled={isExporting}
                className="gap-2"
              >
                <Download className="h-4 w-4" />
                Export
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleImportSettings}
                disabled={isImporting}
                className="gap-2"
              >
                <Upload className="h-4 w-4" />
                Import
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleResetToDefaults}
                className="gap-2 text-destructive hover:text-destructive"
              >
                <RotateCcw className="h-4 w-4" />
                Reset
              </Button>
            </div>
          </div>
        </DialogHeader>

        <div className="flex flex-1 overflow-hidden">
          {/* Settings Navigation */}
          <div className="w-64 border-r bg-card/30 overflow-y-auto">
            <div className="p-4">
              <nav className="space-y-1">
                {SETTINGS_TABS.map((tab) => {
                  const Icon = tab.icon;
                  return (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={cn(
                        "w-full flex items-center gap-3 px-3 py-2 rounded-lg text-left transition-colors",
                        activeTab === tab.id
                          ? "bg-primary text-primary-foreground"
                          : "hover:bg-muted text-muted-foreground hover:text-foreground"
                      )}
                    >
                      <Icon className="h-4 w-4 flex-shrink-0" />
                      <span className="text-sm font-medium">{tab.label}</span>
                    </button>
                  );
                })}
              </nav>
            </div>
          </div>

          {/* Settings Content */}
          <div className="flex-1 overflow-y-auto">
            <div className="p-6">
              <AnimatePresence mode="wait">
                <motion.div
                  key={activeTab}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.2 }}
                >
                  {ActiveComponent && (
                    <ActiveComponent
                      settings={settings}
                      onUpdate={handleSettingsUpdate}
                    />
                  )}
                </motion.div>
              </AnimatePresence>
            </div>
          </div>
        </div>

        {/* Footer with status */}
        {hasChanges && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="border-t bg-card/50 px-6 py-3"
          >
            <div className="flex items-center justify-between">
              <p className="text-sm text-muted-foreground">
                Changes saved automatically
              </p>
              <div className="w-2 h-2 rounded-full bg-green-500" />
            </div>
          </motion.div>
        )}
      </DialogContent>
    </Dialog>
  );
};