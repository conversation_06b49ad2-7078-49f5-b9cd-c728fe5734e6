import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';

import { 
  Grid, 
  Save, 
  Plus, 
  Trash2, 
  Monitor,
  Tablet,
  Smartphone
} from 'lucide-react';
import type { HubSettings, LayoutPreset, ResponsiveBreakpoint } from '@/types/hub';

interface LayoutSettingsProps {
  settings: HubSettings;
  onUpdate: (updates: Partial<HubSettings>) => void;
}

export const LayoutSettings: React.FC<LayoutSettingsProps> = ({
  settings,
  onUpdate,
}) => {
  const [newPresetName, setNewPresetName] = useState('');
  const [showBreakpointEditor, setShowBreakpointEditor] = useState(false);

  const handleLayoutUpdate = (layoutUpdates: Partial<typeof settings.layout>) => {
    onUpdate({
      layout: { ...settings.layout, ...layoutUpdates }
    });
  };

  const handleCreatePreset = () => {
    if (!newPresetName.trim()) return;

    const newPreset: LayoutPreset = {
      id: `preset-${Date.now()}`,
      name: newPresetName,
      description: `Custom layout preset`,
      layout: [], // Would get current layout from widgets
      widgetTypes: settings.enabledWidgets as any[],
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    handleLayoutUpdate({
      layoutPresets: [...settings.layout.layoutPresets, newPreset]
    });
    setNewPresetName('');
  };

  const handleDeletePreset = (presetId: string) => {
    if (confirm('Are you sure you want to delete this layout preset?')) {
      handleLayoutUpdate({
        layoutPresets: settings.layout.layoutPresets.filter(p => p.id !== presetId)
      });
    }
  };

  const handleBreakpointUpdate = (index: number, updates: Partial<ResponsiveBreakpoint>) => {
    const updatedBreakpoints = [...settings.layout.responsiveBreakpoints];
    updatedBreakpoints[index] = { ...updatedBreakpoints[index], ...updates };
    handleLayoutUpdate({ responsiveBreakpoints: updatedBreakpoints });
  };

  const getDeviceIcon = (breakpointName: string) => {
    switch (breakpointName) {
      case 'lg': return Monitor;
      case 'md': return Monitor;
      case 'sm': return Tablet;
      case 'xs': return Smartphone;
      case 'xxs': return Smartphone;
      default: return Monitor;
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold">Layout Settings</h3>
        <p className="text-sm text-muted-foreground">
          Configure widget positioning, grid behavior, and layout presets.
        </p>
      </div>

      {/* Grid Configuration */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <Grid className="h-4 w-4" />
            Grid Configuration
          </CardTitle>
          <CardDescription>
            Control how widgets are positioned and arranged
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-sm font-medium">Enable Drag & Drop</Label>
              <p className="text-xs text-muted-foreground">
                Allow widgets to be repositioned by dragging
              </p>
            </div>
            <Switch
              checked={settings.layout.enableDragDrop}
              onCheckedChange={(enabled) => 
                handleLayoutUpdate({ enableDragDrop: enabled })
              }
            />
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-sm font-medium">Snap to Grid</Label>
              <p className="text-xs text-muted-foreground">
                Automatically align widgets to grid positions
              </p>
            </div>
            <Switch
              checked={settings.layout.snapToGrid}
              onCheckedChange={(enabled) => 
                handleLayoutUpdate({ snapToGrid: enabled })
              }
            />
          </div>

          <Separator />

          <div className="space-y-2">
            <Label className="text-sm font-medium">Grid Size</Label>
            <div className="space-y-2">
              <Slider
                value={[settings.layout.gridSize]}
                onValueChange={(value: number[]) => handleLayoutUpdate({ gridSize: value[0] })}
                min={5}
                max={20}
                step={1}
                className="w-full"
              />
              <div className="flex items-center justify-between text-xs text-muted-foreground">
                <span>5px</span>
                <span>Current: {settings.layout.gridSize}px</span>
                <span>20px</span>
              </div>
            </div>
            <p className="text-xs text-muted-foreground">
              Size of grid units for widget positioning
            </p>
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-sm font-medium">Show Grid Lines</Label>
              <p className="text-xs text-muted-foreground">
                Display grid lines when in edit mode
              </p>
            </div>
            <Switch
              checked={settings.layout.showGridLines}
              onCheckedChange={(enabled) => 
                handleLayoutUpdate({ showGridLines: enabled })
              }
            />
          </div>
        </CardContent>
      </Card>

      {/* Layout Presets */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <Save className="h-4 w-4" />
            Layout Presets
          </CardTitle>
          <CardDescription>
            Save and manage different dashboard layouts
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Create New Preset */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Create New Preset</Label>
            <div className="flex gap-2">
              <Input
                placeholder="Preset name..."
                value={newPresetName}
                onChange={(e) => setNewPresetName(e.target.value)}
                className="flex-1"
              />
              <Button
                onClick={handleCreatePreset}
                disabled={!newPresetName.trim()}
                className="gap-2"
              >
                <Plus className="h-4 w-4" />
                Create
              </Button>
            </div>
          </div>

          <Separator />

          {/* Existing Presets */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Saved Presets</Label>
            {settings.layout.layoutPresets.length === 0 ? (
              <p className="text-sm text-muted-foreground py-4 text-center">
                No layout presets saved yet
              </p>
            ) : (
              <div className="space-y-2">
                {settings.layout.layoutPresets.map((preset) => (
                  <div
                    key={preset.id}
                    className="flex items-center justify-between p-3 rounded-lg border bg-card"
                  >
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{preset.name}</span>
                        {preset.isDefault && (
                          <Badge variant="secondary" className="text-xs">
                            Default
                          </Badge>
                        )}
                      </div>
                      <p className="text-xs text-muted-foreground">
                        {preset.description}
                      </p>
                      <div className="flex gap-1">
                        {preset.widgetTypes.map((type) => (
                          <Badge key={type} variant="outline" className="text-xs">
                            {type}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    <div className="flex gap-1">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleLayoutUpdate({ currentPreset: preset.id })}
                      >
                        Apply
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDeletePreset(preset.id)}
                        className="text-destructive hover:text-destructive"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Responsive Breakpoints */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <Monitor className="h-4 w-4" />
            Responsive Breakpoints
          </CardTitle>
          <CardDescription>
            Configure how the layout adapts to different screen sizes
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <Label className="text-sm font-medium">Breakpoint Configuration</Label>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowBreakpointEditor(!showBreakpointEditor)}
            >
              {showBreakpointEditor ? 'Hide' : 'Show'} Editor
            </Button>
          </div>

          {!showBreakpointEditor ? (
            <div className="space-y-2">
              {settings.layout.responsiveBreakpoints.map((breakpoint) => {
                const Icon = getDeviceIcon(breakpoint.name);
                return (
                  <div
                    key={breakpoint.name}
                    className="flex items-center justify-between p-3 rounded-lg border bg-card"
                  >
                    <div className="flex items-center gap-3">
                      <Icon className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <span className="font-medium capitalize">{breakpoint.name}</span>
                        <p className="text-xs text-muted-foreground">
                          ≥{breakpoint.minWidth}px • {breakpoint.cols} columns
                        </p>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          ) : (
            <div className="space-y-4">
              {settings.layout.responsiveBreakpoints.map((breakpoint, index) => {
                const Icon = getDeviceIcon(breakpoint.name);
                return (
                  <Card key={breakpoint.name}>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-sm flex items-center gap-2">
                        <Icon className="h-4 w-4" />
                        {breakpoint.name.toUpperCase()} Breakpoint
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div className="grid grid-cols-2 gap-3">
                        <div className="space-y-1">
                          <Label className="text-xs">Min Width (px)</Label>
                          <Input
                            type="number"
                            value={breakpoint.minWidth}
                            onChange={(e) => handleBreakpointUpdate(index, {
                              minWidth: parseInt(e.target.value) || 0
                            })}
                            className="h-8"
                          />
                        </div>
                        <div className="space-y-1">
                          <Label className="text-xs">Columns</Label>
                          <Input
                            type="number"
                            value={breakpoint.cols}
                            onChange={(e) => handleBreakpointUpdate(index, {
                              cols: parseInt(e.target.value) || 1
                            })}
                            className="h-8"
                          />
                        </div>
                        <div className="space-y-1">
                          <Label className="text-xs">Row Height (px)</Label>
                          <Input
                            type="number"
                            value={breakpoint.rowHeight}
                            onChange={(e) => handleBreakpointUpdate(index, {
                              rowHeight: parseInt(e.target.value) || 60
                            })}
                            className="h-8"
                          />
                        </div>
                        <div className="space-y-1">
                          <Label className="text-xs">Margin [X, Y]</Label>
                          <div className="flex gap-1">
                            <Input
                              type="number"
                              value={breakpoint.margin[0]}
                              onChange={(e) => handleBreakpointUpdate(index, {
                                margin: [parseInt(e.target.value) || 0, breakpoint.margin[1]]
                              })}
                              className="h-8"
                              placeholder="X"
                            />
                            <Input
                              type="number"
                              value={breakpoint.margin[1]}
                              onChange={(e) => handleBreakpointUpdate(index, {
                                margin: [breakpoint.margin[0], parseInt(e.target.value) || 0]
                              })}
                              className="h-8"
                              placeholder="Y"
                            />
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};