import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  FileText,
  Code,
  Bug,
  Wrench,
  BookOpen,
  Plus,
  X,
  Copy,
  Trash2
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { cn } from "@/lib/utils";

interface SessionTemplate {
  id: string;
  name: string;
  description: string;
  prompt: string;
  icon: React.ReactNode;
  category: 'development' | 'review' | 'documentation' | 'debugging' | 'custom';
  variables?: string[]; // Variables like {projectName}, {language}
}

interface SessionTemplatesManagerProps {
  onSelectTemplate: (template: SessionTemplate) => void;
  onClose: () => void;
  projectPath?: string;
}

const DEFAULT_TEMPLATES: SessionTemplate[] = [
  {
    id: 'code-review',
    name: 'Code Review',
    description: 'Comprehensive code review and suggestions',
    prompt: 'Please review the code in this project. Focus on:\n\n1. Code quality and best practices\n2. Potential bugs or security issues\n3. Performance optimizations\n4. Code organization and structure\n5. Documentation improvements\n\nProvide specific suggestions with examples where possible.',
    icon: <Code className="h-5 w-5" />,
    category: 'review'
  },
  {
    id: 'bug-fix',
    name: 'Bug Investigation',
    description: 'Debug and fix issues in the codebase',
    prompt: 'I need help debugging an issue in my project. Please:\n\n1. Analyze the codebase for potential problems\n2. Help identify the root cause of bugs\n3. Suggest fixes with explanations\n4. Provide testing strategies\n\nLet me know what specific issue you\'d like me to investigate.',
    icon: <Bug className="h-5 w-5" />,
    category: 'debugging'
  },
  {
    id: 'feature-development',
    name: 'Feature Development',
    description: 'Plan and implement new features',
    prompt: 'I want to develop a new feature for this project. Please help me:\n\n1. Plan the feature architecture\n2. Identify required changes\n3. Implement the feature step by step\n4. Consider edge cases and testing\n5. Ensure code quality and best practices\n\nWhat feature would you like to implement?',
    icon: <Plus className="h-5 w-5" />,
    category: 'development'
  },
  {
    id: 'refactoring',
    name: 'Code Refactoring',
    description: 'Improve code structure and maintainability',
    prompt: 'Please help me refactor this codebase to improve:\n\n1. Code organization and structure\n2. Performance and efficiency\n3. Readability and maintainability\n4. Following best practices\n5. Reducing technical debt\n\nAnalyze the current code and suggest improvements.',
    icon: <Wrench className="h-5 w-5" />,
    category: 'development'
  },
  {
    id: 'documentation',
    name: 'Documentation',
    description: 'Generate comprehensive documentation',
    prompt: 'Please help me create comprehensive documentation for this project:\n\n1. README with setup instructions\n2. API documentation\n3. Code comments and docstrings\n4. Architecture overview\n5. Usage examples\n\nAnalyze the codebase and generate appropriate documentation.',
    icon: <BookOpen className="h-5 w-5" />,
    category: 'documentation'
  }
];

export const SessionTemplatesManager: React.FC<SessionTemplatesManagerProps> = ({
  onSelectTemplate,
  onClose
}) => {
  const [templates, setTemplates] = useState<SessionTemplate[]>(DEFAULT_TEMPLATES);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [newTemplate, setNewTemplate] = useState({
    name: '',
    description: '',
    prompt: '',
    category: 'custom' as const
  });
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  const categories = [
    { id: 'all', name: 'All Templates', count: templates.length },
    { id: 'development', name: 'Development', count: templates.filter(t => t.category === 'development').length },
    { id: 'review', name: 'Code Review', count: templates.filter(t => t.category === 'review').length },
    { id: 'debugging', name: 'Debugging', count: templates.filter(t => t.category === 'debugging').length },
    { id: 'documentation', name: 'Documentation', count: templates.filter(t => t.category === 'documentation').length },
    { id: 'custom', name: 'Custom', count: templates.filter(t => t.category === 'custom').length }
  ];

  const filteredTemplates = selectedCategory === 'all' 
    ? templates 
    : templates.filter(t => t.category === selectedCategory);

  const handleCreateTemplate = () => {
    if (!newTemplate.name || !newTemplate.prompt) return;

    const template: SessionTemplate = {
      id: `custom-${Date.now()}`,
      name: newTemplate.name,
      description: newTemplate.description,
      prompt: newTemplate.prompt,
      icon: <FileText className="h-5 w-5" />,
      category: newTemplate.category
    };

    setTemplates(prev => [...prev, template]);
    setNewTemplate({ name: '', description: '', prompt: '', category: 'custom' });
    setShowCreateDialog(false);
  };

  const handleDeleteTemplate = (templateId: string) => {
    setTemplates(prev => prev.filter(t => t.id !== templateId));
  };

  const handleDuplicateTemplate = (template: SessionTemplate) => {
    const duplicated: SessionTemplate = {
      ...template,
      id: `${template.id}-copy-${Date.now()}`,
      name: `${template.name} (Copy)`,
      category: 'custom'
    };
    setTemplates(prev => [...prev, duplicated]);
  };

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-6 border-b">
        <div>
          <h2 className="text-2xl font-bold">Session Templates</h2>
          <p className="text-sm text-muted-foreground mt-1">
            Quick start your coding session with predefined prompts
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            onClick={() => setShowCreateDialog(true)}
            size="sm"
            className="bg-primary hover:bg-primary/90"
          >
            <Plus className="h-4 w-4 mr-2" />
            Create Template
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={onClose}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <div className="flex-1 flex overflow-hidden">
        {/* Categories Sidebar */}
        <div className="w-64 border-r bg-muted/30 p-4">
          <h3 className="font-semibold mb-3">Categories</h3>
          <div className="space-y-1">
            {categories.map(category => (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                className={cn(
                  "w-full text-left px-3 py-2 rounded-md text-sm transition-colors",
                  selectedCategory === category.id
                    ? "bg-primary text-primary-foreground"
                    : "hover:bg-muted"
                )}
              >
                <div className="flex items-center justify-between">
                  <span>{category.name}</span>
                  <span className="text-xs opacity-70">{category.count}</span>
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Templates Grid */}
        <div className="flex-1 p-6 overflow-y-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <AnimatePresence>
              {filteredTemplates.map(template => (
                <motion.div
                  key={template.id}
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.9 }}
                  className="group relative bg-card border rounded-lg p-4 hover:shadow-md transition-all cursor-pointer"
                  onClick={() => onSelectTemplate(template)}
                >
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-primary/10 rounded-md text-primary">
                        {template.icon}
                      </div>
                      <div className="flex-1">
                        <h4 className="font-semibold">{template.name}</h4>
                        <p className="text-xs text-muted-foreground capitalize">
                          {template.category}
                        </p>
                      </div>
                    </div>
                    
                    {template.category === 'custom' && (
                      <div className="opacity-0 group-hover:opacity-100 transition-opacity flex gap-1">
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-6 w-6"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDuplicateTemplate(template);
                          }}
                        >
                          <Copy className="h-3 w-3" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-6 w-6 text-destructive hover:text-destructive"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteTemplate(template.id);
                          }}
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    )}
                  </div>
                  
                  <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
                    {template.description}
                  </p>
                  
                  <div className="text-xs text-muted-foreground bg-muted/50 rounded p-2 font-mono line-clamp-3">
                    {template.prompt.substring(0, 120)}...
                  </div>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
        </div>
      </div>

      {/* Create Template Dialog */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Create Custom Template</DialogTitle>
            <DialogDescription>
              Create a reusable template for your coding sessions
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="template-name">Template Name</Label>
                <Input
                  id="template-name"
                  placeholder="e.g., API Development"
                  value={newTemplate.name}
                  onChange={(e) => setNewTemplate(prev => ({ ...prev, name: e.target.value }))}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="template-category">Category</Label>
                <select
                  id="template-category"
                  className="w-full px-3 py-2 border border-input bg-background rounded-md"
                  value={newTemplate.category}
                  onChange={(e) => setNewTemplate(prev => ({ ...prev, category: e.target.value as any }))}
                >
                  <option value="custom">Custom</option>
                  <option value="development">Development</option>
                  <option value="review">Code Review</option>
                  <option value="debugging">Debugging</option>
                  <option value="documentation">Documentation</option>
                </select>
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="template-description">Description</Label>
              <Input
                id="template-description"
                placeholder="Brief description of what this template does"
                value={newTemplate.description}
                onChange={(e) => setNewTemplate(prev => ({ ...prev, description: e.target.value }))}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="template-prompt">Prompt Template</Label>
              <Textarea
                id="template-prompt"
                placeholder="Enter the prompt template. You can use variables like {projectName}, {language}, etc."
                value={newTemplate.prompt}
                onChange={(e) => setNewTemplate(prev => ({ ...prev, prompt: e.target.value }))}
                rows={8}
                className="font-mono text-sm"
              />
            </div>
          </div>
          
          <div className="flex justify-end gap-2">
            <Button
              variant="outline"
              onClick={() => setShowCreateDialog(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={handleCreateTemplate}
              disabled={!newTemplate.name || !newTemplate.prompt}
            >
              Create Template
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};