import React, { useState, useEffect, useMemo } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Search,
  Filter,
  X,
  User,
  Bo<PERSON>,
  Code,
  FileText,
  AlertCircle,
  CheckCircle,
  Hash,
  ChevronDown,
  ChevronUp
} from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Popover } from "@/components/ui/popover";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { cn } from "@/lib/utils";

interface Message {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  timestamp: Date;
  type?: 'text' | 'code' | 'error' | 'success';
  tokens?: number;
  model?: string;
}

interface MessageSearchProps {
  messages: Message[];
  onSearchResults: (results: Message[]) => void;
  onClose: () => void;
  className?: string;
}

interface SearchFilters {
  roles: ('user' | 'assistant')[];
  types: ('text' | 'code' | 'error' | 'success')[];
  dateRange: {
    start?: Date;
    end?: Date;
  };
  minTokens?: number;
  maxTokens?: number;
  models: string[];
}

const DEFAULT_FILTERS: SearchFilters = {
  roles: ['user', 'assistant'],
  types: ['text', 'code', 'error', 'success'],
  dateRange: {},
  models: []
};

export const MessageSearch: React.FC<MessageSearchProps> = ({
  messages,
  onSearchResults,
  onClose,
  className
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState<SearchFilters>(DEFAULT_FILTERS);
  const [isFiltersOpen, setIsFiltersOpen] = useState(false);
  const [isExpanded, setIsExpanded] = useState(true);

  // Get unique models from messages
  const availableModels = useMemo(() => {
    const models = new Set<string>();
    messages.forEach(msg => {
      if (msg.model) models.add(msg.model);
    });
    return Array.from(models);
  }, [messages]);

  // Filter and search messages
  const filteredMessages = useMemo(() => {
    let results = messages;

    // Apply text search
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      results = results.filter(msg => 
        msg.content.toLowerCase().includes(query)
      );
    }

    // Apply role filter
    if (filters.roles.length > 0 && filters.roles.length < 2) {
      results = results.filter(msg => filters.roles.includes(msg.role));
    }

    // Apply type filter
    if (filters.types.length > 0 && filters.types.length < 4) {
      results = results.filter(msg => 
        msg.type ? filters.types.includes(msg.type) : filters.types.includes('text')
      );
    }

    // Apply date range filter
    if (filters.dateRange.start) {
      results = results.filter(msg => msg.timestamp >= filters.dateRange.start!);
    }
    if (filters.dateRange.end) {
      results = results.filter(msg => msg.timestamp <= filters.dateRange.end!);
    }

    // Apply token range filter
    if (filters.minTokens !== undefined) {
      results = results.filter(msg => (msg.tokens || 0) >= filters.minTokens!);
    }
    if (filters.maxTokens !== undefined) {
      results = results.filter(msg => (msg.tokens || 0) <= filters.maxTokens!);
    }

    // Apply model filter
    if (filters.models.length > 0) {
      results = results.filter(msg => 
        msg.model ? filters.models.includes(msg.model) : false
      );
    }

    return results;
  }, [messages, searchQuery, filters]);

  // Update search results
  useEffect(() => {
    onSearchResults(filteredMessages);
  }, [filteredMessages, onSearchResults]);

  const updateFilter = <K extends keyof SearchFilters>(
    key: K,
    value: SearchFilters[K]
  ) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const toggleRole = (role: 'user' | 'assistant') => {
    const newRoles = filters.roles.includes(role)
      ? filters.roles.filter(r => r !== role)
      : [...filters.roles, role];
    updateFilter('roles', newRoles);
  };

  const toggleType = (type: 'text' | 'code' | 'error' | 'success') => {
    const newTypes = filters.types.includes(type)
      ? filters.types.filter(t => t !== type)
      : [...filters.types, type];
    updateFilter('types', newTypes);
  };

  const toggleModel = (model: string) => {
    const newModels = filters.models.includes(model)
      ? filters.models.filter(m => m !== model)
      : [...filters.models, model];
    updateFilter('models', newModels);
  };

  const clearFilters = () => {
    setFilters(DEFAULT_FILTERS);
    setSearchQuery('');
  };

  const hasActiveFilters = useMemo(() => {
    return (
      searchQuery.trim() !== '' ||
      filters.roles.length < 2 ||
      filters.types.length < 4 ||
      filters.dateRange.start ||
      filters.dateRange.end ||
      filters.minTokens !== undefined ||
      filters.maxTokens !== undefined ||
      filters.models.length > 0
    );
  }, [searchQuery, filters]);

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'code': return Code;
      case 'error': return AlertCircle;
      case 'success': return CheckCircle;
      default: return FileText;
    }
  };


  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className={cn(
        "bg-background border rounded-lg shadow-lg",
        className
      )}
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center gap-2">
          <Search className="h-5 w-5 text-muted-foreground" />
          <h3 className="font-semibold">Search Messages</h3>
          {filteredMessages.length !== messages.length && (
            <Badge variant="secondary">
              {filteredMessages.length} of {messages.length}
            </Badge>
          )}
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
          >
            {isExpanded ? (
              <ChevronUp className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )}
          </Button>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            className="overflow-hidden"
          >
            <div className="p-4 space-y-4">
              {/* Search Input */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search message content..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 pr-10"
                />
                {searchQuery && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setSearchQuery('')}
                    className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
                  >
                    <X className="h-3 w-3" />
                  </Button>
                )}
              </div>

              {/* Filters */}
              <div className="flex items-center gap-2">
                <Popover
                  open={isFiltersOpen}
                  onOpenChange={setIsFiltersOpen}
                  className="w-80"
                  align="start"
                  trigger={
                    <Button
                      variant="outline"
                      size="sm"
                      className={cn(
                        "flex items-center gap-2",
                        hasActiveFilters && "border-primary bg-primary/5"
                      )}
                    >
                      <Filter className="h-4 w-4" />
                      Filters
                      {hasActiveFilters && (
                        <Badge variant="secondary" className="ml-1 h-5 px-1.5 text-xs">
                          Active
                        </Badge>
                      )}
                    </Button>
                  }
                  content={
                    <ScrollArea className="h-96">
                      <div className="space-y-4">
                        {/* Role Filter */}
                        <div className="space-y-2">
                          <Label className="text-sm font-medium">Message Role</Label>
                          <div className="flex gap-2">
                            {[
                              { value: 'user', label: 'User', icon: User },
                              { value: 'assistant', label: 'Assistant', icon: Bot }
                            ].map(({ value, label, icon: Icon }) => (
                              <button
                                key={value}
                                onClick={() => toggleRole(value as any)}
                                className={cn(
                                  "flex items-center gap-2 px-3 py-2 rounded-md border transition-colors",
                                  filters.roles.includes(value as any)
                                    ? "border-primary bg-primary/10 text-primary"
                                    : "border-border hover:bg-muted/50"
                                )}
                              >
                                <Icon className="h-4 w-4" />
                                <span className="text-sm">{label}</span>
                              </button>
                            ))}
                          </div>
                        </div>

                        <Separator />

                        {/* Type Filter */}
                        <div className="space-y-2">
                          <Label className="text-sm font-medium">Message Type</Label>
                          <div className="grid grid-cols-2 gap-2">
                            {[
                              { value: 'text', label: 'Text' },
                              { value: 'code', label: 'Code' },
                              { value: 'error', label: 'Error' },
                              { value: 'success', label: 'Success' }
                            ].map(({ value, label }) => {
                              const Icon = getTypeIcon(value);
                              return (
                                <button
                                  key={value}
                                  onClick={() => toggleType(value as any)}
                                  className={cn(
                                    "flex items-center gap-2 px-3 py-2 rounded-md border transition-colors",
                                    filters.types.includes(value as any)
                                      ? "border-primary bg-primary/10 text-primary"
                                      : "border-border hover:bg-muted/50"
                                  )}
                                >
                                  <Icon className="h-4 w-4" />
                                  <span className="text-sm">{label}</span>
                                </button>
                              );
                            })}
                          </div>
                        </div>

                        {availableModels.length > 0 && (
                          <>
                            <Separator />
                            
                            {/* Model Filter */}
                            <div className="space-y-2">
                              <Label className="text-sm font-medium">Model</Label>
                              <div className="space-y-2">
                                {availableModels.map(model => (
                                  <div key={model} className="flex items-center space-x-2">
                                    <Checkbox
                                      id={`model-${model}`}
                                      checked={filters.models.includes(model)}
                                      onCheckedChange={() => toggleModel(model)}
                                    />
                                    <Label
                                      htmlFor={`model-${model}`}
                                      className="text-sm font-normal cursor-pointer"
                                    >
                                      {model}
                                    </Label>
                                  </div>
                                ))}
                              </div>
                            </div>
                          </>
                        )}

                        <Separator />

                        {/* Token Range */}
                        <div className="space-y-2">
                          <Label className="text-sm font-medium">Token Range</Label>
                          <div className="grid grid-cols-2 gap-2">
                            <div className="space-y-1">
                              <Label className="text-xs text-muted-foreground">Min</Label>
                              <Input
                                type="number"
                                placeholder="0"
                                value={filters.minTokens || ''}
                                onChange={(e) => updateFilter('minTokens', e.target.value ? parseInt(e.target.value) : undefined)}
                                className="h-8"
                              />
                            </div>
                            <div className="space-y-1">
                              <Label className="text-xs text-muted-foreground">Max</Label>
                              <Input
                                type="number"
                                placeholder="∞"
                                value={filters.maxTokens || ''}
                                onChange={(e) => updateFilter('maxTokens', e.target.value ? parseInt(e.target.value) : undefined)}
                                className="h-8"
                              />
                            </div>
                          </div>
                        </div>

                        <Separator />

                        {/* Clear Filters */}
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={clearFilters}
                          className="w-full"
                          disabled={!hasActiveFilters}
                        >
                          Clear All Filters
                        </Button>
                      </div>
                    </ScrollArea>
                  }
                />

                {hasActiveFilters && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={clearFilters}
                    className="text-muted-foreground hover:text-foreground"
                  >
                    Clear
                  </Button>
                )}
              </div>

              {/* Active Filters Display */}
              {hasActiveFilters && (
                <div className="flex flex-wrap gap-2">
                  {searchQuery && (
                    <Badge variant="secondary" className="flex items-center gap-1">
                      <Search className="h-3 w-3" />
                      "{searchQuery}"
                      <button
                        onClick={() => setSearchQuery('')}
                        className="ml-1 hover:bg-muted-foreground/20 rounded-full p-0.5"
                      >
                        <X className="h-2 w-2" />
                      </button>
                    </Badge>
                  )}
                  
                  {filters.roles.length === 1 && (
                    <Badge variant="secondary" className="flex items-center gap-1">
                      {filters.roles[0] === 'user' ? (
                        <User className="h-3 w-3" />
                      ) : (
                        <Bot className="h-3 w-3" />
                      )}
                      {filters.roles[0]}
                    </Badge>
                  )}
                  
                  {filters.types.length < 4 && filters.types.map(type => {
                    const Icon = getTypeIcon(type);
                    return (
                      <Badge key={type} variant="secondary" className="flex items-center gap-1">
                        <Icon className="h-3 w-3" />
                        {type}
                      </Badge>
                    );
                  })}
                  
                  {filters.models.map(model => (
                    <Badge key={model} variant="secondary" className="flex items-center gap-1">
                      <Hash className="h-3 w-3" />
                      {model}
                    </Badge>
                  ))}
                  
                  {(filters.minTokens !== undefined || filters.maxTokens !== undefined) && (
                    <Badge variant="secondary" className="flex items-center gap-1">
                      <Hash className="h-3 w-3" />
                      {filters.minTokens || 0}-{filters.maxTokens || '∞'} tokens
                    </Badge>
                  )}
                </div>
              )}

              {/* Results Summary */}
              <div className="text-sm text-muted-foreground">
                {filteredMessages.length === 0 ? (
                  "No messages match your search criteria"
                ) : filteredMessages.length === messages.length ? (
                  `Showing all ${messages.length} messages`
                ) : (
                  `Found ${filteredMessages.length} of ${messages.length} messages`
                )}
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};