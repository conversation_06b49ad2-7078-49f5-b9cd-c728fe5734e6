import React from 'react';
import { 
  Download, 
  CheckCircle, 
  RefreshCw, 
  Trash2,
  AlertCircle,
  ExternalLink
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { useMarketplaceStore, type MarketplaceAgent } from '@/stores/marketplaceStore';

interface InstallButtonProps {
  agent: MarketplaceAgent;
  onClick?: (e: React.MouseEvent) => void;
  className?: string;
  showDropdown?: boolean;
}

export const InstallButton: React.FC<InstallButtonProps> = ({
  agent,
  onClick,
  className,
  showDropdown = true
}) => {
  const { 
    isInstalling, 
    installAgent, 
    uninstallAgent, 
    updateAgent
  } = useMarketplaceStore();

  const handleInstall = async (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onClick) {
      onClick(e);
      return;
    }
    
    try {
      await installAgent(agent.id);
    } catch (error) {
      console.error('Failed to install agent:', error);
    }
  };

  const handleUninstall = async (e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      await uninstallAgent(agent.id);
    } catch (error) {
      console.error('Failed to uninstall agent:', error);
    }
  };

  const handleUpdate = async (e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      await updateAgent(agent.id);
    } catch (error) {
      console.error('Failed to update agent:', error);
    }
  };

  const hasUpdate = agent.isInstalled && 
    agent.installedVersion && 
    agent.installedVersion !== agent.version;

  // Simple install button for non-installed agents
  if (!agent.isInstalled) {
    return (
      <Button
        size="sm"
        onClick={handleInstall}
        disabled={isInstalling}
        className={cn("flex items-center gap-2", className)}
      >
        {isInstalling ? (
          <>
            <RefreshCw className="h-4 w-4 animate-spin" />
            Installing...
          </>
        ) : (
          <>
            <Download className="h-4 w-4" />
            Install
          </>
        )}
      </Button>
    );
  }

  // Installed agent with dropdown menu
  if (showDropdown) {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            size="sm"
            variant={hasUpdate ? "default" : "secondary"}
            disabled={isInstalling}
            className={cn("flex items-center gap-2", className)}
            onClick={(e) => e.stopPropagation()}
          >
            {isInstalling ? (
              <>
                <RefreshCw className="h-4 w-4 animate-spin" />
                {hasUpdate ? 'Updating...' : 'Processing...'}
              </>
            ) : hasUpdate ? (
              <>
                <AlertCircle className="h-4 w-4" />
                Update Available
              </>
            ) : (
              <>
                <CheckCircle className="h-4 w-4" />
                Installed
              </>
            )}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" onClick={(e) => e.stopPropagation()}>
          <div className="px-2 py-1.5 text-sm font-medium">
            {agent.name}
          </div>
          <div className="px-2 pb-2 text-xs text-muted-foreground">
            Installed: v{agent.installedVersion}
            {hasUpdate && (
              <>
                <br />
                Available: v{agent.version}
              </>
            )}
          </div>
          <DropdownMenuSeparator />
          
          {hasUpdate && (
            <DropdownMenuItem onClick={handleUpdate} disabled={isInstalling}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Update to v{agent.version}
            </DropdownMenuItem>
          )}
          
          <DropdownMenuItem 
            onClick={(e) => {
              e.stopPropagation();
              window.open(agent.sourceUrl || agent.installUrl, '_blank');
            }}
          >
            <ExternalLink className="h-4 w-4 mr-2" />
            View Source
          </DropdownMenuItem>
          
          <DropdownMenuSeparator />
          
          <DropdownMenuItem 
            onClick={handleUninstall}
            disabled={isInstalling}
            className="text-destructive focus:text-destructive"
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Uninstall
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    );
  }

  // Simple installed button without dropdown
  return (
    <div className={cn("flex items-center gap-2", className)}>
      <Badge 
        variant={hasUpdate ? "default" : "secondary"}
        className="flex items-center gap-1"
      >
        {hasUpdate ? (
          <>
            <AlertCircle className="h-3 w-3" />
            Update Available
          </>
        ) : (
          <>
            <CheckCircle className="h-3 w-3" />
            Installed
          </>
        )}
      </Badge>
    </div>
  );
};