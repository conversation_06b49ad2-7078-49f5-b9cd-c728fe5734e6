import React, { useEffect, useState } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, Database, CheckCircle, AlertCircle } from 'lucide-react';

interface MarketplaceInitializerProps {
  onInitialized: () => void;
}

export const MarketplaceInitializer: React.FC<MarketplaceInitializerProps> = ({ onInitialized }) => {
  const [isInitializing, setIsInitializing] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [message, setMessage] = useState<string>('');

  const initializeMarketplace = async () => {
    setIsInitializing(true);
    setError(null);
    setMessage('');

    try {
      // Seed the marketplace with sample data
      const result = await invoke<string>('marketplace_seed_sample_data');
      setMessage(result);
      setIsInitialized(true);
      
      // Wait a moment then call the callback
      setTimeout(() => {
        onInitialized();
      }, 1500);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to initialize marketplace');
    } finally {
      setIsInitializing(false);
    }
  };

  // Auto-check if marketplace needs initialization
  useEffect(() => {
    const checkMarketplace = async () => {
      try {
        // Try to fetch agents to see if marketplace is populated
        const result = await invoke('marketplace_search_agents', {
          params: { page: 1, limit: 1 }
        });
        
        if (result && (result as any).agents && (result as any).agents.length > 0) {
          setIsInitialized(true);
          onInitialized();
        }
      } catch (err) {
        // Marketplace likely needs initialization
        console.log('Marketplace needs initialization');
      }
    };

    checkMarketplace();
  }, [onInitialized]);

  if (isInitialized) {
    return (
      <Card className="w-full max-w-md mx-auto">
        <CardHeader className="text-center">
          <CheckCircle className="w-12 h-12 text-green-500 mx-auto mb-2" />
          <CardTitle>Marketplace Ready</CardTitle>
          <CardDescription>
            The marketplace has been initialized successfully
          </CardDescription>
        </CardHeader>
        <CardContent>
          {message && (
            <p className="text-sm text-muted-foreground text-center">{message}</p>
          )}
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="text-center">
        <Database className="w-12 h-12 text-blue-500 mx-auto mb-2" />
        <CardTitle>Initialize Marketplace</CardTitle>
        <CardDescription>
          Set up the marketplace with sample agents to get started
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {error && (
          <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-md">
            <AlertCircle className="w-4 h-4 text-red-500" />
            <span className="text-sm text-red-700">{error}</span>
          </div>
        )}
        
        {message && (
          <div className="flex items-center gap-2 p-3 bg-green-50 border border-green-200 rounded-md">
            <CheckCircle className="w-4 h-4 text-green-500" />
            <span className="text-sm text-green-700">{message}</span>
          </div>
        )}

        <Button 
          onClick={initializeMarketplace} 
          disabled={isInitializing}
          className="w-full"
        >
          {isInitializing ? (
            <>
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              Initializing...
            </>
          ) : (
            'Initialize Marketplace'
          )}
        </Button>
        
        <p className="text-xs text-muted-foreground text-center">
          This will populate the marketplace with sample agents for demonstration purposes.
        </p>
      </CardContent>
    </Card>
  );
};