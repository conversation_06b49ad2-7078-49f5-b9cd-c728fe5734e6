import React, { useEffect } from 'react';
import { 
  Star, 
  Download, 
  User, 
  Package, 
  Shield, 
  Crown, 
  ExternalLink, 
  Tag,
  Clock,
  GitBranch,
  FileText,
  CheckCircle,
  Eye
} from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useMarketplaceStore, type MarketplaceAgent } from '@/stores/marketplaceStore';
import { AGENT_ICONS } from '@/components/CCAgents';
import { InstallButton } from './InstallButton';
import { RatingSystem } from './RatingSystem';

interface AgentDetailsModalProps {
  isOpen: boolean;
  agent: MarketplaceAgent | null;
  onClose: () => void;
}

export const AgentDetailsModal: React.FC<AgentDetailsModalProps> = ({
  isOpen,
  agent,
  onClose
}) => {
  const { reviews, fetchReviews } = useMarketplaceStore();

  useEffect(() => {
    if (agent && isOpen) {
      fetchReviews(agent.id);
    }
  }, [agent?.id, isOpen, fetchReviews]);

  if (!agent) return null;

  const renderIcon = (iconName: string) => {
    const Icon = AGENT_ICONS[iconName as keyof typeof AGENT_ICONS] || AGENT_ICONS.bot;
    return <Icon className="h-12 w-12" />;
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  const formatSize = (bytes: number) => {
    if (bytes >= 1024 * 1024) {
      return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
    }
    if (bytes >= 1024) {
      return (bytes / 1024).toFixed(0) + ' KB';
    }
    return bytes + ' B';
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const agentReviews = reviews[agent.id] || [];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] p-0 overflow-hidden">
        <div className="flex flex-col h-full">
          {/* Header */}
          <DialogHeader className="p-6 pb-4 border-b">
            <div className="flex items-start gap-4">
              <div className="p-3 rounded-lg bg-primary/10 text-primary shrink-0">
                {renderIcon(agent.icon)}
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-2">
                  <DialogTitle className="text-2xl font-bold truncate">
                    {agent.name}
                  </DialogTitle>
                  <div className="flex items-center gap-1 shrink-0">
                    {agent.isVerified && (
                      <Shield className="h-5 w-5 text-blue-500" />
                    )}
                    {agent.isFeatured && (
                      <Crown className="h-5 w-5 text-yellow-500" />
                    )}
                    {agent.isInstalled && (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    )}
                  </div>
                </div>
                <p className="text-muted-foreground mb-3">
                  {agent.description}
                </p>
                <div className="flex items-center gap-4 text-sm text-muted-foreground">
                  <div className="flex items-center gap-1">
                    <User className="h-4 w-4" />
                    {agent.author}
                  </div>
                  <div className="flex items-center gap-1">
                    <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                    {agent.rating.toFixed(1)} ({formatNumber(agent.reviewCount)} reviews)
                  </div>
                  <div className="flex items-center gap-1">
                    <Download className="h-4 w-4" />
                    {formatNumber(agent.downloadCount)} downloads
                  </div>
                </div>
              </div>
              <div className="shrink-0">
                <InstallButton agent={agent} className="min-w-[120px]" />
              </div>
            </div>
          </DialogHeader>

          {/* Content */}
          <ScrollArea className="flex-1">
            <div className="p-6">
              <Tabs defaultValue="overview" className="w-full">
                <TabsList className="grid w-full grid-cols-4">
                  <TabsTrigger value="overview">Overview</TabsTrigger>
                  <TabsTrigger value="reviews">Reviews</TabsTrigger>
                  <TabsTrigger value="dependencies">Dependencies</TabsTrigger>
                  <TabsTrigger value="changelog">Changelog</TabsTrigger>
                </TabsList>

                <TabsContent value="overview" className="mt-6 space-y-6">
                  {/* Long Description */}
                  {agent.longDescription && (
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <FileText className="h-5 w-5" />
                          About This Agent
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-sm leading-relaxed">
                          {agent.longDescription}
                        </p>
                      </CardContent>
                    </Card>
                  )}

                  {/* Screenshots */}
                  {agent.screenshots && agent.screenshots.length > 0 && (
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <Eye className="h-5 w-5" />
                          Screenshots
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {agent.screenshots.map((screenshot, index) => (
                            <img
                              key={index}
                              src={screenshot}
                              alt={`${agent.name} screenshot ${index + 1}`}
                              className="rounded-lg border bg-muted"
                            />
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  )}

                  {/* Tags */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Tag className="h-5 w-5" />
                        Tags
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex flex-wrap gap-2">
                        {agent.tags.map(tag => (
                          <Badge key={tag} variant="secondary">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </CardContent>
                  </Card>

                  {/* Details Grid */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Version Info */}
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <GitBranch className="h-5 w-5" />
                          Version Information
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-3">
                        <div className="flex justify-between">
                          <span className="text-sm text-muted-foreground">Current Version</span>
                          <Badge>{agent.version}</Badge>
                        </div>
                        {agent.isInstalled && agent.installedVersion && (
                          <div className="flex justify-between">
                            <span className="text-sm text-muted-foreground">Installed Version</span>
                            <Badge variant="outline">{agent.installedVersion}</Badge>
                          </div>
                        )}
                        <div className="flex justify-between">
                          <span className="text-sm text-muted-foreground">Last Updated</span>
                          <span className="text-sm">{formatDate(agent.lastUpdated)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-muted-foreground">Package Size</span>
                          <span className="text-sm">{formatSize(agent.size)}</span>
                        </div>
                      </CardContent>
                    </Card>

                    {/* License & Links */}
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <Package className="h-5 w-5" />
                          License & Links
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-3">
                        <div className="flex justify-between">
                          <span className="text-sm text-muted-foreground">License</span>
                          <Badge variant={agent.licenseType === 'Commercial' ? 'default' : 'secondary'}>
                            {agent.licenseType}
                          </Badge>
                        </div>
                        <div className="space-y-2">
                          <Button
                            variant="outline"
                            size="sm"
                            className="w-full justify-start"
                            onClick={() => window.open(agent.installUrl, '_blank')}
                          >
                            <ExternalLink className="h-4 w-4 mr-2" />
                            Install URL
                          </Button>
                          {agent.sourceUrl && (
                            <Button
                              variant="outline"
                              size="sm"
                              className="w-full justify-start"
                              onClick={() => window.open(agent.sourceUrl, '_blank')}
                            >
                              <ExternalLink className="h-4 w-4 mr-2" />
                              Source Code
                            </Button>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </TabsContent>

                <TabsContent value="reviews" className="mt-6">
                  <RatingSystem agent={agent} reviews={agentReviews} />
                </TabsContent>

                <TabsContent value="dependencies" className="mt-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Package className="h-5 w-5" />
                        Dependencies
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      {agent.dependencies.length > 0 ? (
                        <div className="space-y-2">
                          {agent.dependencies.map(dep => (
                            <div key={dep} className="flex items-center gap-2 p-2 rounded border">
                              <Package className="h-4 w-4 text-muted-foreground" />
                              <span className="text-sm font-mono">{dep}</span>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="text-center py-8 text-muted-foreground">
                          <Package className="h-8 w-8 mx-auto mb-2 opacity-50" />
                          <p>No dependencies required</p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="changelog" className="mt-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Clock className="h-5 w-5" />
                        Changelog
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {/* Mock changelog - replace with actual data */}
                        <div className="border-l-2 border-primary pl-4">
                          <div className="flex items-center gap-2 mb-1">
                            <Badge>{agent.version}</Badge>
                            <span className="text-sm text-muted-foreground">
                              {formatDate(agent.lastUpdated)}
                            </span>
                          </div>
                          <ul className="text-sm space-y-1 list-disc list-inside text-muted-foreground">
                            <li>Performance improvements and bug fixes</li>
                            <li>Updated dependencies to latest versions</li>
                            <li>Enhanced error handling and logging</li>
                          </ul>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
              </Tabs>
            </div>
          </ScrollArea>
        </div>
      </DialogContent>
    </Dialog>
  );
};