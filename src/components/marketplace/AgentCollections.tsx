import React from 'react';
import { motion } from 'framer-motion';
import { 
  Star, 
  Users, 
  Crown,
  Package,
  Calendar
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { cn } from '@/lib/utils';
import { useMarketplaceStore } from '@/stores/marketplaceStore';

export const AgentCollections: React.FC = () => {
  const { 
    collections, 
    agents,
    selectedCollection,
    setSelectedCollection 
  } = useMarketplaceStore();

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 1) return '1 day ago';
    if (diffDays < 7) return `${diffDays} days ago`;
    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} weeks ago`;
    return date.toLocaleDateString();
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-base flex items-center gap-2">
          <Package className="h-4 w-4" />
          Collections
        </CardTitle>
      </CardHeader>
      <CardContent className="p-0">
        <ScrollArea className="h-[300px]">
          <div className="p-4 space-y-3">
            {/* Clear Selection */}
            {selectedCollection && (
              <Button
                variant="outline"
                size="sm"
                className="w-full"
                onClick={() => setSelectedCollection(null)}
              >
                Clear Collection Filter
              </Button>
            )}

            {collections.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <Package className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm">No collections available</p>
              </div>
            ) : (
              collections.map((collection, index) => {
                const isSelected = selectedCollection === collection.id;
                const collectionAgents = agents.filter(agent => 
                  collection.agents.includes(agent.id)
                );
                const averageRating = collectionAgents.length > 0
                  ? collectionAgents.reduce((sum, agent) => sum + agent.rating, 0) / collectionAgents.length
                  : 0;

                return (
                  <motion.div
                    key={collection.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.2, delay: index * 0.05 }}
                  >
                    <Card 
                      className={cn(
                        "cursor-pointer transition-all hover:shadow-md",
                        isSelected && "ring-2 ring-primary bg-primary/5"
                      )}
                      onClick={() => setSelectedCollection(
                        isSelected ? null : collection.id
                      )}
                    >
                      <CardContent className="p-4">
                        <div className="flex items-start gap-3">
                          <div className={cn(
                            "p-2 rounded-lg shrink-0",
                            collection.isOfficial 
                              ? "bg-yellow-500/10 text-yellow-600" 
                              : "bg-primary/10 text-primary"
                          )}>
                            {collection.isOfficial ? (
                              <Crown className="h-4 w-4" />
                            ) : (
                              <Users className="h-4 w-4" />
                            )}
                          </div>
                          
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2 mb-1">
                              <h3 className="font-medium text-sm truncate">
                                {collection.name}
                              </h3>
                              {collection.isOfficial && (
                                <Badge variant="secondary" className="text-xs">
                                  Official
                                </Badge>
                              )}
                            </div>
                            
                            <p className="text-xs text-muted-foreground line-clamp-2 mb-2">
                              {collection.description}
                            </p>
                            
                            <div className="flex items-center gap-3 text-xs text-muted-foreground">
                              <div className="flex items-center gap-1">
                                <Package className="h-3 w-3" />
                                <span>{collection.agents.length} agents</span>
                              </div>
                              
                              {averageRating > 0 && (
                                <div className="flex items-center gap-1">
                                  <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                                  <span>{averageRating.toFixed(1)}</span>
                                </div>
                              )}
                            </div>
                            
                            <div className="flex items-center justify-between mt-2">
                              <div className="flex items-center gap-1 text-xs text-muted-foreground">
                                <Users className="h-3 w-3" />
                                <span>{collection.curator}</span>
                              </div>
                              
                              <div className="flex items-center gap-1 text-xs text-muted-foreground">
                                <Calendar className="h-3 w-3" />
                                <span>{formatDate(collection.createdAt)}</span>
                              </div>
                            </div>
                          </div>
                          
                          {isSelected && (
                            <div className="shrink-0">
                              <div className="w-2 h-2 bg-primary rounded-full"></div>
                            </div>
                          )}
                        </div>
                        
                        {/* Preview agents when selected */}
                        {isSelected && collectionAgents.length > 0 && (
                          <motion.div
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: 'auto' }}
                            exit={{ opacity: 0, height: 0 }}
                            transition={{ duration: 0.2 }}
                            className="mt-3 pt-3 border-t space-y-1"
                          >
                            {collectionAgents.slice(0, 3).map(agent => (
                              <div 
                                key={agent.id}
                                className="flex items-center gap-2 p-1 rounded text-xs hover:bg-muted/50"
                              >
                                <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                                <span className="flex-1 truncate">{agent.name}</span>
                                <span className="text-muted-foreground">{agent.rating.toFixed(1)}</span>
                              </div>
                            ))}
                            
                            {collectionAgents.length > 3 && (
                              <div className="text-xs text-muted-foreground text-center py-1">
                                +{collectionAgents.length - 3} more agents
                              </div>
                            )}
                          </motion.div>
                        )}
                      </CardContent>
                    </Card>
                  </motion.div>
                );
              })
            )}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
};