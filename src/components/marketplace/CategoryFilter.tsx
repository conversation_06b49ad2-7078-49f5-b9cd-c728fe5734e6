import React from 'react';
import { motion } from 'framer-motion';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { cn } from '@/lib/utils';
import { useMarketplaceStore } from '@/stores/marketplaceStore';
import { AGENT_ICONS } from '@/components/CCAgents';
import { Grid3X3 } from 'lucide-react';

export const CategoryFilter: React.FC = () => {
  const { 
    categories, 
    selectedCategory,
    setSelectedCategory 
  } = useMarketplaceStore();

  const renderIcon = (iconName: string) => {
    if (iconName === 'grid') {
      return <Grid3X3 className="h-4 w-4" />;
    }
    const Icon = AGENT_ICONS[iconName as keyof typeof AGENT_ICONS] || Grid3X3;
    return <Icon className="h-4 w-4" />;
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-base">Categories</CardTitle>
      </CardHeader>
      <CardContent className="p-0">
        <ScrollArea className="h-[400px]">
          <div className="p-4 space-y-2">
            {/* All Categories */}
            <Button
              variant={selectedCategory === null ? "default" : "ghost"}
              className="w-full justify-start gap-3 h-auto p-3"
              onClick={() => setSelectedCategory(null)}
            >
              <div className="p-1 rounded bg-primary/10 text-primary">
                {renderIcon('grid')}
              </div>
              <div className="flex-1 text-left">
                <div className="font-medium">All Categories</div>
                <div className="text-xs text-muted-foreground">
                  {categories.reduce((sum, cat) => sum + cat.agentCount, 0)} agents
                </div>
              </div>
            </Button>

            {/* Individual Categories */}
            {categories.map((category, index) => (
              <motion.div
                key={category.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.2, delay: index * 0.05 }}
              >
                <Button
                  variant={selectedCategory === category.id ? "default" : "ghost"}
                  className="w-full justify-start gap-3 h-auto p-3"
                  onClick={() => setSelectedCategory(
                    selectedCategory === category.id ? null : category.id
                  )}
                >
                  <div className={cn(
                    "p-1 rounded",
                    selectedCategory === category.id 
                      ? "bg-primary-foreground/20 text-primary-foreground" 
                      : "bg-primary/10 text-primary"
                  )}>
                    {renderIcon(category.icon)}
                  </div>
                  <div className="flex-1 text-left">
                    <div className="font-medium">{category.name}</div>
                    <div className={cn(
                      "text-xs",
                      selectedCategory === category.id 
                        ? "text-primary-foreground/60" 
                        : "text-muted-foreground"
                    )}>
                      {category.agentCount} agents
                    </div>
                  </div>
                  <Badge 
                    variant={selectedCategory === category.id ? "secondary" : "outline"}
                    className="text-xs"
                  >
                    {category.agentCount}
                  </Badge>
                </Button>
              </motion.div>
            ))}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
};