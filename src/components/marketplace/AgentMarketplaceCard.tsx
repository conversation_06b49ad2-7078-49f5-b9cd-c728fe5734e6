import React from 'react';
import { motion } from 'framer-motion';
import { 
  Star, 
  Download, 
  Shield, 
  Crown, 
  Clock,
  User,
  Tag,
  ArrowRight,
  CheckCircle
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON>ontent, CardFooter, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useMarketplaceStore, type MarketplaceAgent } from '@/stores/marketplaceStore';
import { AGENT_ICONS } from '@/components/CCAgents';
import { InstallButton } from './InstallButton';
import { MagicCard } from '@/components/ui/magic-card';
import { ShimmerButton } from '@/components/ui/shimmer-button';

interface AgentMarketplaceCardProps {
  agent: MarketplaceAgent;
  viewMode: 'grid' | 'list';
  index: number;
}

export const AgentMarketplaceCard: React.FC<AgentMarketplaceCardProps> = ({
  agent,
  viewMode,
  index
}) => {
  const { openDetailsModal, openInstallDialog } = useMarketplaceStore();

  const renderIcon = (iconName: string) => {
    const Icon = AGENT_ICONS[iconName as keyof typeof AGENT_ICONS] || AGENT_ICONS.bot;
    return <Icon className="h-8 w-8" />;
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  const formatSize = (bytes: number) => {
    if (bytes >= 1024 * 1024) {
      return (bytes / (1024 * 1024)).toFixed(1) + 'MB';
    }
    if (bytes >= 1024) {
      return (bytes / 1024).toFixed(0) + 'KB';
    }
    return bytes + 'B';
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 1) return 'Yesterday';
    if (diffDays < 7) return `${diffDays} days ago`;
    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} weeks ago`;
    if (diffDays < 365) return `${Math.ceil(diffDays / 30)} months ago`;
    return `${Math.ceil(diffDays / 365)} years ago`;
  };

  const handleCardClick = () => {
    openDetailsModal(agent);
  };

  const handleInstallClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    openInstallDialog(agent);
  };

  if (viewMode === 'list') {
    return (
      <motion.div
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.4, delay: index * 0.05, type: "spring", stiffness: 100 }}
        whileHover={{ scale: 1.02, y: -2 }}
        whileTap={{ scale: 0.98 }}
      >
        <div onClick={handleCardClick}>
          <MagicCard 
            className="hover:shadow-2xl transition-all duration-300 cursor-pointer group backdrop-blur-sm border-0 bg-gradient-to-br from-card/80 to-card/60"
            gradientColor="#3b82f6"
          >
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              {/* Icon */}
              <div className="shrink-0 p-3 rounded-lg bg-primary/10 text-primary">
                {renderIcon(agent.icon)}
              </div>
              
              {/* Main Content */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-2">
                  <h3 className="text-lg font-semibold truncate group-hover:text-primary transition-colors">
                    {agent.name}
                  </h3>
                  <div className="flex items-center gap-1 shrink-0">
                    {agent.isVerified && (
                      <Shield className="h-4 w-4 text-blue-500" />
                    )}
                    {agent.isFeatured && (
                      <Crown className="h-4 w-4 text-yellow-500" />
                    )}
                    {agent.isInstalled && (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    )}
                  </div>
                </div>
                
                <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
                  {agent.description}
                </p>
                
                <div className="flex items-center gap-4 text-xs text-muted-foreground mb-3">
                  <div className="flex items-center gap-1">
                    <User className="h-3 w-3" />
                    {agent.author}
                  </div>
                  <div className="flex items-center gap-1">
                    <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                    {agent.rating.toFixed(1)} ({formatNumber(agent.reviewCount)})
                  </div>
                  <div className="flex items-center gap-1">
                    <Download className="h-3 w-3" />
                    {formatNumber(agent.downloadCount)}
                  </div>
                  <div className="flex items-center gap-1">
                    <Clock className="h-3 w-3" />
                    {formatDate(agent.lastUpdated)}
                  </div>
                </div>
                
                {/* Tags */}
                <div className="flex flex-wrap gap-1 mb-3">
                  {agent.tags.slice(0, 3).map(tag => (
                    <Badge key={tag} variant="secondary" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                  {agent.tags.length > 3 && (
                    <Badge variant="outline" className="text-xs">
                      +{agent.tags.length - 3}
                    </Badge>
                  )}
                </div>
              </div>
              
              {/* Actions */}
              <div className="shrink-0 flex items-center gap-2">
                <Badge variant={agent.licenseType === 'Commercial' ? 'default' : 'secondary'}>
                  {agent.licenseType === 'Commercial' ? 'Paid' : 'Free'}
                </Badge>
                <InstallButton agent={agent} onClick={handleInstallClick} />
                <Button variant="ghost" size="sm" className="opacity-0 group-hover:opacity-100 transition-opacity">
                  <ArrowRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardContent>
          </MagicCard>
        </div>
      </motion.div>
    );
  }

  // Grid view
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95, y: 20 }}
      animate={{ opacity: 1, scale: 1, y: 0 }}
      transition={{ duration: 0.4, delay: index * 0.05, type: "spring", stiffness: 120, damping: 20 }}
      whileHover={{ scale: 1.05, y: -5, rotateY: 2 }}
      whileTap={{ scale: 0.98 }}
      style={{ perspective: 1000 }}
    >
      <div onClick={handleCardClick}>
        <MagicCard 
          className="h-full hover:shadow-2xl transition-all duration-500 cursor-pointer group backdrop-blur-sm border-0 bg-gradient-to-br from-card/90 to-card/70 overflow-hidden"
          gradientColor="#8b5cf6"
        >
        <CardHeader className="pb-4 relative">
          {/* Subtle gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-transparent to-purple-500/5 rounded-t-lg" />
          
          <div className="flex items-start justify-between relative z-10">
            <motion.div 
              className="p-3 rounded-lg bg-gradient-to-br from-primary/20 to-primary/10 text-primary shadow-lg"
              whileHover={{ scale: 1.1, rotate: 5 }}
              transition={{ type: "spring", stiffness: 400, damping: 25 }}
            >
              {renderIcon(agent.icon)}
            </motion.div>
            <div className="flex items-center gap-1">
              {agent.isVerified && (
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  whileHover={{ scale: 1.2, rotate: 10 }}
                  transition={{ type: "spring", stiffness: 500, damping: 30 }}
                >
                  <Shield className="h-4 w-4 text-blue-500 drop-shadow-sm" />
                </motion.div>
              )}
              {agent.isFeatured && (
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  whileHover={{ scale: 1.2, rotate: -10 }}
                  transition={{ type: "spring", stiffness: 500, damping: 30, delay: 0.1 }}
                >
                  <Crown className="h-4 w-4 text-yellow-500 drop-shadow-sm" />
                </motion.div>
              )}
              {agent.isInstalled && (
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  whileHover={{ scale: 1.2 }}
                  transition={{ type: "spring", stiffness: 500, damping: 30, delay: 0.2 }}
                >
                  <CheckCircle className="h-4 w-4 text-green-500 drop-shadow-sm" />
                </motion.div>
              )}
            </div>
          </div>
          
          <div>
            <h3 className="text-lg font-semibold mb-2 line-clamp-1 group-hover:text-primary transition-colors">
              {agent.name}
            </h3>
            <p className="text-sm text-muted-foreground line-clamp-3">
              {agent.description}
            </p>
          </div>
        </CardHeader>
        
        <CardContent className="pb-4">
          {/* Stats */}
          <div className="grid grid-cols-2 gap-3 mb-4 text-xs text-muted-foreground">
            <div className="flex items-center gap-1">
              <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
              <span>{agent.rating.toFixed(1)}</span>
              <span>({formatNumber(agent.reviewCount)})</span>
            </div>
            <div className="flex items-center gap-1">
              <Download className="h-3 w-3" />
              <span>{formatNumber(agent.downloadCount)}</span>
            </div>
            <div className="flex items-center gap-1">
              <User className="h-3 w-3" />
              <span className="truncate">{agent.author}</span>
            </div>
            <div className="flex items-center gap-1">
              <Tag className="h-3 w-3" />
              <span>{formatSize(agent.size)}</span>
            </div>
          </div>
          
          {/* Tags */}
          <div className="flex flex-wrap gap-1 mb-4">
            {agent.tags.slice(0, 2).map(tag => (
              <Badge key={tag} variant="secondary" className="text-xs">
                {tag}
              </Badge>
            ))}
            {agent.tags.length > 2 && (
              <Badge variant="outline" className="text-xs">
                +{agent.tags.length - 2}
              </Badge>
            )}
          </div>
        </CardContent>
        
        <CardFooter className="pt-0">
          <div className="w-full space-y-3">
            {/* License */}
            <div className="flex justify-between items-center">
              <Badge variant={agent.licenseType === 'Commercial' ? 'default' : 'secondary'}>
                {agent.licenseType === 'Commercial' ? 'Paid' : 'Free'}
              </Badge>
              <span className="text-xs text-muted-foreground">
                v{agent.version}
              </span>
            </div>
            
            {/* Install Button */}
            <motion.div
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <ShimmerButton
                onClick={handleInstallClick}
                className="w-full h-10"
                shimmerColor={agent.isInstalled ? "#10b981" : "#3b82f6"}
                disabled={agent.isInstalled}
              >
                {agent.isInstalled ? (
                  <>
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Installed
                  </>
                ) : (
                  <>
                    <Download className="h-4 w-4 mr-2" />
                    Install
                  </>
                )}
              </ShimmerButton>
            </motion.div>
           </div>
         </CardFooter>
       </MagicCard>
     </div>
   </motion.div>
   );
};