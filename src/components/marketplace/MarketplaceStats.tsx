import React from 'react';
import { motion } from 'framer-motion';
import { 
  TrendingUp, 
  Download, 
  Users, 
  Package, 
  Star,
  Shield,
  Crown
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useMarketplaceStore } from '@/stores/marketplaceStore';

export const MarketplaceStats: React.FC = () => {
  const { agents, categories } = useMarketplaceStore();

  // Calculate statistics
  const totalAgents = agents.length;
  const totalDownloads = agents.reduce((sum, agent) => sum + agent.downloadCount, 0);
  const averageRating = agents.length > 0 
    ? agents.reduce((sum, agent) => sum + agent.rating, 0) / agents.length 
    : 0;
  const verifiedAgents = agents.filter(agent => agent.isVerified).length;
  const featuredAgents = agents.filter(agent => agent.isFeatured).length;
  const installedAgents = agents.filter(agent => agent.isInstalled).length;

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  const stats = [
    {
      label: 'Total Agents',
      value: totalAgents,
      icon: Package,
      color: 'text-blue-500',
      bgColor: 'bg-blue-500/10'
    },
    {
      label: 'Total Downloads',
      value: formatNumber(totalDownloads),
      icon: Download,
      color: 'text-green-500',
      bgColor: 'bg-green-500/10'
    },
    {
      label: 'Average Rating',
      value: averageRating.toFixed(1),
      icon: Star,
      color: 'text-yellow-500',
      bgColor: 'bg-yellow-500/10'
    },
    {
      label: 'Categories',
      value: categories.length,
      icon: TrendingUp,
      color: 'text-purple-500',
      bgColor: 'bg-purple-500/10'
    }
  ];

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            Marketplace Stats
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Main Stats Grid */}
          <div className="grid grid-cols-2 gap-3">
            {stats.map((stat, index) => (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.2, delay: index * 0.05 }}
                className="text-center p-3 rounded-lg border bg-card"
              >
                <div className={`inline-flex p-2 rounded-lg ${stat.bgColor} ${stat.color} mb-2`}>
                  <stat.icon className="h-4 w-4" />
                </div>
                <div className="text-lg font-bold">{stat.value}</div>
                <div className="text-xs text-muted-foreground">{stat.label}</div>
              </motion.div>
            ))}
          </div>

          {/* Additional Stats */}
          <div className="space-y-2 pt-2 border-t">
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2 text-sm">
                <Shield className="h-3 w-3 text-blue-500" />
                <span className="text-muted-foreground">Verified Agents</span>
              </div>
              <Badge variant="secondary">{verifiedAgents}</Badge>
            </div>

            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2 text-sm">
                <Crown className="h-3 w-3 text-yellow-500" />
                <span className="text-muted-foreground">Featured Agents</span>
              </div>
              <Badge variant="secondary">{featuredAgents}</Badge>
            </div>

            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2 text-sm">
                <Users className="h-3 w-3 text-green-500" />
                <span className="text-muted-foreground">Installed</span>
              </div>
              <Badge variant="secondary">{installedAgents}</Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};