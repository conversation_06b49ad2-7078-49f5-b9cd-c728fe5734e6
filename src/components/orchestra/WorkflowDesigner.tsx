import React, { useState, useRef } from "react";
import { motion } from "framer-motion";
import {
  Workflow,
  Plus,
  Save,
  Upload,
  Download,
  Play,
  GitBranch,
  Bo<PERSON>,
  Timer,
  Filter,
  Repeat,
  Zap,
  X,
  ArrowRight,
  Trash2
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { cn } from "@/lib/utils";
import type { Agent } from "@/lib/api";
import type { AgentWorkflow, WorkflowNode, WorkflowEdge, WorkflowTrigger } from "../AgentOrchestraPanel";

interface WorkflowDesignerProps {
  onClose: () => void;
  onSave: (workflow: AgentWorkflow) => void;
  existingAgents: Agent[];
  workflow?: AgentWorkflow;
}

interface NodeTemplate {
  type: WorkflowNode['type'];
  name: string;
  description: string;
  icon: React.ComponentType<any>;
  color: string;
  defaultConfig: Record<string, any>;
}

const NODE_TEMPLATES: NodeTemplate[] = [
  {
    type: 'agent',
    name: 'Agent Task',
    description: 'Assign a task to an agent',
    icon: Bot,
    color: 'bg-blue-500',
    defaultConfig: {
      task: '',
      timeout: 300000
    }
  },
  {
    type: 'condition',
    name: 'Condition',
    description: 'Branch based on condition',
    icon: Filter,
    color: 'bg-yellow-500',
    defaultConfig: {
      condition: '',
      operator: 'equals'
    }
  },
  {
    type: 'parallel',
    name: 'Parallel',
    description: 'Run tasks in parallel',
    icon: GitBranch,
    color: 'bg-purple-500',
    defaultConfig: {
      waitForAll: true
    }
  },
  {
    type: 'delay',
    name: 'Delay',
    description: 'Wait for specified time',
    icon: Timer,
    color: 'bg-orange-500',
    defaultConfig: {
      duration: 1000
    }
  },
  {
    type: 'loop',
    name: 'Loop',
    description: 'Repeat tasks',
    icon: Repeat,
    color: 'bg-green-500',
    defaultConfig: {
      iterations: 1,
      condition: ''
    }
  },
  {
    type: 'webhook',
    name: 'Webhook',
    description: 'Send HTTP request',
    icon: Zap,
    color: 'bg-red-500',
    defaultConfig: {
      url: '',
      method: 'POST',
      headers: {}
    }
  }
];

export default function WorkflowDesigner({ 
  onClose, 
  onSave, 
  existingAgents, 
  workflow 
}: WorkflowDesignerProps) {
  const [workflowName, setWorkflowName] = useState(workflow?.name || '');
  const [workflowDescription, setWorkflowDescription] = useState(workflow?.description || '');
  const [nodes, setNodes] = useState<WorkflowNode[]>(workflow?.nodes || []);
  const [edges, setEdges] = useState<WorkflowEdge[]>(workflow?.edges || []);
  const [triggers, setTriggers] = useState<WorkflowTrigger[]>(workflow?.triggers || []);
  const [selectedNode, setSelectedNode] = useState<string | null>(null);
  const [draggedTemplate, setDraggedTemplate] = useState<NodeTemplate | null>(null);
  const [isConnecting, setIsConnecting] = useState(false);
  const [connectionSource, setConnectionSource] = useState<string | null>(null);
  const canvasRef = useRef<HTMLDivElement>(null);

  // Add node to canvas
  const addNode = (template: NodeTemplate, position: { x: number; y: number }) => {
    const newNode: WorkflowNode = {
      id: `node_${Date.now()}`,
      type: template.type,
      name: template.name,
      position,
      config: { ...template.defaultConfig }
    };
    setNodes(prev => [...prev, newNode]);
  };

  // Update node configuration
  const updateNodeConfig = (nodeId: string, config: Record<string, any>) => {
    setNodes(prev => prev.map(node => 
      node.id === nodeId ? { ...node, config } : node
    ));
  };

  // Update node position
  const updateNodePosition = (nodeId: string, position: { x: number; y: number }) => {
    setNodes(prev => prev.map(node => 
      node.id === nodeId ? { ...node, position } : node
    ));
  };

  // Delete node
  const deleteNode = (nodeId: string) => {
    setNodes(prev => prev.filter(node => node.id !== nodeId));
    setEdges(prev => prev.filter(edge => 
      edge.source !== nodeId && edge.target !== nodeId
    ));
    setSelectedNode(null);
  };

  // Add edge between nodes
  const addEdge = (source: string, target: string) => {
    const newEdge: WorkflowEdge = {
      id: `edge_${Date.now()}`,
      source,
      target,
      condition: undefined
    };
    setEdges(prev => [...prev, newEdge]);
  };

  // Handle canvas drop
  const handleCanvasDrop = (e: React.DragEvent) => {
    e.preventDefault();
    if (!draggedTemplate || !canvasRef.current) return;

    const rect = canvasRef.current.getBoundingClientRect();
    const position = {
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    };

    addNode(draggedTemplate, position);
    setDraggedTemplate(null);
  };

  // Handle canvas drag over
  const handleCanvasDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  // Save workflow
  const handleSave = () => {
    if (!workflowName.trim()) {
      alert('Please enter a workflow name');
      return;
    }

    const newWorkflow: AgentWorkflow = {
      id: workflow?.id || `workflow_${Date.now()}`,
      name: workflowName,
      description: workflowDescription,
      nodes,
      edges,
      triggers,
      variables: workflow?.variables || {},
      status: 'draft',
      createdAt: workflow?.createdAt || new Date(),
      updatedAt: new Date()
    };

    onSave(newWorkflow);
  };

  // Get selected node data
  const selectedNodeData = selectedNode ? nodes.find(n => n.id === selectedNode) : null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        className="bg-white dark:bg-gray-900 rounded-lg shadow-xl w-full h-full max-w-7xl max-h-[90vh] flex flex-col"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center gap-3">
            <Workflow className="w-6 h-6 text-blue-500" />
            <div>
              <h2 className="text-xl font-semibold">Workflow Designer</h2>
              <p className="text-sm text-gray-500">Design and configure agent workflows</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" onClick={handleSave}>
              <Save className="w-4 h-4 mr-2" />
              Save
            </Button>
            <Button variant="ghost" onClick={onClose}>
              <X className="w-4 h-4" />
            </Button>
          </div>
        </div>

        <div className="flex flex-1 overflow-hidden">
          {/* Left Sidebar - Node Templates */}
          <div className="w-80 border-r bg-gray-50 dark:bg-gray-800 p-4 overflow-y-auto">
            <div className="space-y-4">
              {/* Workflow Info */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Workflow Info</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div>
                    <Label htmlFor="workflow-name">Name</Label>
                    <Input
                      id="workflow-name"
                      value={workflowName}
                      onChange={(e) => setWorkflowName(e.target.value)}
                      placeholder="Enter workflow name"
                    />
                  </div>
                  <div>
                    <Label htmlFor="workflow-description">Description</Label>
                    <Textarea
                      id="workflow-description"
                      value={workflowDescription}
                      onChange={(e) => setWorkflowDescription(e.target.value)}
                      placeholder="Enter workflow description"
                      rows={3}
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Node Templates */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Node Templates</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {NODE_TEMPLATES.map((template) => {
                      const IconComponent = template.icon;
                      return (
                        <div
                          key={template.type}
                          draggable
                          onDragStart={() => setDraggedTemplate(template)}
                          className="flex items-center gap-3 p-3 rounded-lg border bg-white dark:bg-gray-700 cursor-move hover:shadow-md transition-shadow"
                        >
                          <div className={cn("w-8 h-8 rounded flex items-center justify-center", template.color)}>
                            <IconComponent className="w-4 h-4 text-white" />
                          </div>
                          <div className="flex-1">
                            <div className="font-medium text-sm">{template.name}</div>
                            <div className="text-xs text-gray-500">{template.description}</div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>

              {/* Triggers */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Triggers</CardTitle>
                </CardHeader>
                <CardContent>
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full"
                    onClick={() => {
                      const newTrigger: WorkflowTrigger = {
                        id: `trigger_${Date.now()}`,
                        type: 'manual',
                        config: {}
                      };
                      setTriggers(prev => [...prev, newTrigger]);
                    }}
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    Add Trigger
                  </Button>
                  <div className="mt-2 space-y-2">
                    {triggers.map((trigger) => (
                      <div key={trigger.id} className="flex items-center justify-between p-2 bg-gray-100 dark:bg-gray-700 rounded">
                        <span className="text-sm capitalize">{trigger.type}</span>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setTriggers(prev => prev.filter(t => t.id !== trigger.id))}
                        >
                          <Trash2 className="w-3 h-3" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Main Canvas */}
          <div className="flex-1 flex flex-col">
            {/* Canvas Toolbar */}
            <div className="flex items-center justify-between p-4 border-b bg-gray-50 dark:bg-gray-800">
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm">
                  <Upload className="w-4 h-4 mr-2" />
                  Import
                </Button>
                <Button variant="outline" size="sm">
                  <Download className="w-4 h-4 mr-2" />
                  Export
                </Button>
              </div>
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm">
                  <Play className="w-4 h-4 mr-2" />
                  Test Run
                </Button>
              </div>
            </div>

            {/* Canvas */}
            <div
              ref={canvasRef}
              className="flex-1 relative bg-gray-100 dark:bg-gray-900 overflow-hidden"
              onDrop={handleCanvasDrop}
              onDragOver={handleCanvasDragOver}
              style={{
                backgroundImage: 'radial-gradient(circle, #e5e7eb 1px, transparent 1px)',
                backgroundSize: '20px 20px'
              }}
            >
              {/* Render Nodes */}
              {nodes.map((node) => {
                const template = NODE_TEMPLATES.find(t => t.type === node.type);
                if (!template) return null;
                
                const IconComponent = template.icon;
                const isSelected = selectedNode === node.id;

                return (
                  <motion.div
                    key={node.id}
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    className={cn(
                      "absolute w-48 bg-white dark:bg-gray-800 rounded-lg border-2 shadow-lg cursor-move",
                      isSelected ? "border-blue-500" : "border-gray-200 dark:border-gray-700"
                    )}
                    style={{
                      left: node.position.x,
                      top: node.position.y
                    }}
                    onClick={() => setSelectedNode(node.id)}
                    onMouseDown={(e) => {
                      // Handle node dragging
                      const startX = e.clientX - node.position.x;
                      const startY = e.clientY - node.position.y;

                      const handleMouseMove = (e: MouseEvent) => {
                        updateNodePosition(node.id, {
                          x: e.clientX - startX,
                          y: e.clientY - startY
                        });
                      };

                      const handleMouseUp = () => {
                        document.removeEventListener('mousemove', handleMouseMove);
                        document.removeEventListener('mouseup', handleMouseUp);
                      };

                      document.addEventListener('mousemove', handleMouseMove);
                      document.addEventListener('mouseup', handleMouseUp);
                    }}
                  >
                    {/* Node Header */}
                    <div className={cn("flex items-center gap-2 p-3 rounded-t-lg", template.color)}>
                      <IconComponent className="w-4 h-4 text-white" />
                      <span className="text-white font-medium text-sm">{node.name}</span>
                      <div className="ml-auto flex items-center gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-6 w-6 p-0 text-white hover:bg-white/20"
                          onClick={(e) => {
                            e.stopPropagation();
                            if (isConnecting) {
                              if (connectionSource && connectionSource !== node.id) {
                                addEdge(connectionSource, node.id);
                                setIsConnecting(false);
                                setConnectionSource(null);
                              }
                            } else {
                              setIsConnecting(true);
                              setConnectionSource(node.id);
                            }
                          }}
                        >
                          <ArrowRight className="w-3 h-3" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-6 w-6 p-0 text-white hover:bg-white/20"
                          onClick={(e) => {
                            e.stopPropagation();
                            deleteNode(node.id);
                          }}
                        >
                          <Trash2 className="w-3 h-3" />
                        </Button>
                      </div>
                    </div>

                    {/* Node Content */}
                    <div className="p-3">
                      <div className="text-xs text-gray-500 mb-2">
                        {template.description}
                      </div>
                      {node.type === 'agent' && node.config.agentId && (
                        <div className="text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded">
                          Agent: {existingAgents.find(a => a.id === node.config.agentId)?.name || 'Unknown'}
                        </div>
                      )}
                      {node.type === 'condition' && node.config.condition && (
                        <div className="text-xs bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 px-2 py-1 rounded">
                          {node.config.condition}
                        </div>
                      )}
                    </div>

                    {/* Connection Points */}
                    <div className="absolute -right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 bg-blue-500 rounded-full border-2 border-white"></div>
                    <div className="absolute -left-2 top-1/2 transform -translate-y-1/2 w-4 h-4 bg-green-500 rounded-full border-2 border-white"></div>
                  </motion.div>
                );
              })}

              {/* Render Edges */}
              <svg className="absolute inset-0 pointer-events-none">
                {edges.map((edge) => {
                  const sourceNode = nodes.find(n => n.id === edge.source);
                  const targetNode = nodes.find(n => n.id === edge.target);
                  
                  if (!sourceNode || !targetNode) return null;

                  const sourceX = sourceNode.position.x + 192; // Node width
                  const sourceY = sourceNode.position.y + 40; // Half node height
                  const targetX = targetNode.position.x;
                  const targetY = targetNode.position.y + 40;

                  return (
                    <g key={edge.id}>
                      <path
                        d={`M ${sourceX} ${sourceY} Q ${sourceX + 50} ${sourceY} ${targetX} ${targetY}`}
                        stroke="#3b82f6"
                        strokeWidth="2"
                        fill="none"
                        markerEnd="url(#arrowhead)"
                      />
                    </g>
                  );
                })}
                <defs>
                  <marker
                    id="arrowhead"
                    markerWidth="10"
                    markerHeight="7"
                    refX="9"
                    refY="3.5"
                    orient="auto"
                  >
                    <polygon
                      points="0 0, 10 3.5, 0 7"
                      fill="#3b82f6"
                    />
                  </marker>
                </defs>
              </svg>

              {/* Empty State */}
              {nodes.length === 0 && (
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-center">
                    <Workflow className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                      Start Building Your Workflow
                    </h3>
                    <p className="text-gray-500 mb-4">
                      Drag and drop nodes from the sidebar to create your workflow
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Right Sidebar - Node Properties */}
          {selectedNodeData && (
            <div className="w-80 border-l bg-gray-50 dark:bg-gray-800 p-4 overflow-y-auto">
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Node Properties</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label>Node Name</Label>
                    <Input
                      value={selectedNodeData.name}
                      onChange={(e) => {
                        setNodes(prev => prev.map(node => 
                          node.id === selectedNode ? { ...node, name: e.target.value } : node
                        ));
                      }}
                    />
                  </div>

                  {/* Agent-specific configuration */}
                  {selectedNodeData.type === 'agent' && (
                    <div className="space-y-3">
                      <div>
                        <Label>Select Agent</Label>
                        <Select
                          value={selectedNodeData.config.agentId?.toString()}
                          onValueChange={(value) => updateNodeConfig(selectedNode!, {
                            ...selectedNodeData.config,
                            agentId: value
                          })}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Choose an agent" />
                          </SelectTrigger>
                          <SelectContent>
                            {existingAgents.map((agent) => (
                              <SelectItem key={agent.id} value={agent.id?.toString() || ''}>
                                {agent.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label>Task Description</Label>
                        <Textarea
                          value={selectedNodeData.config.task}
                          onChange={(e) => updateNodeConfig(selectedNode!, {
                            ...selectedNodeData.config,
                            task: e.target.value
                          })}
                          placeholder="Describe the task for this agent"
                          rows={3}
                        />
                      </div>
                      <div>
                        <Label>Timeout (ms)</Label>
                        <Input
                          type="number"
                          value={selectedNodeData.config.timeout?.toString()}
                          onChange={(e) => updateNodeConfig(selectedNode!, {
                            ...selectedNodeData.config,
                            timeout: parseInt(e.target.value) || 300000
                          })}
                        />
                      </div>
                    </div>
                  )}

                  {/* Condition-specific configuration */}
                  {selectedNodeData.type === 'condition' && (
                    <div className="space-y-3">
                      <div>
                        <Label>Condition Expression</Label>
                        <Input
                          value={selectedNodeData.config.condition}
                          onChange={(e) => updateNodeConfig(selectedNode!, {
                            ...selectedNodeData.config,
                            condition: e.target.value
                          })}
                          placeholder="e.g., result.success === true"
                        />
                      </div>
                      <div>
                        <Label>Operator</Label>
                        <Select
                          value={selectedNodeData.config.operator}
                          onValueChange={(value) => updateNodeConfig(selectedNode!, {
                            ...selectedNodeData.config,
                            operator: value
                          })}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="equals">Equals</SelectItem>
                            <SelectItem value="not_equals">Not Equals</SelectItem>
                            <SelectItem value="contains">Contains</SelectItem>
                            <SelectItem value="greater_than">Greater Than</SelectItem>
                            <SelectItem value="less_than">Less Than</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  )}

                  {/* Delay-specific configuration */}
                  {selectedNodeData.type === 'delay' && (
                    <div>
                      <Label>Duration (ms)</Label>
                      <Input
                        type="number"
                        value={selectedNodeData.config.duration?.toString()}
                        onChange={(e) => updateNodeConfig(selectedNode!, {
                          ...selectedNodeData.config,
                          duration: parseInt(e.target.value) || 1000
                        })}
                      />
                    </div>
                  )}

                  {/* Loop-specific configuration */}
                  {selectedNodeData.type === 'loop' && (
                    <div className="space-y-3">
                      <div>
                        <Label>Iterations</Label>
                        <Input
                          type="number"
                          value={selectedNodeData.config.iterations?.toString()}
                          onChange={(e) => updateNodeConfig(selectedNode!, {
                            ...selectedNodeData.config,
                            iterations: parseInt(e.target.value) || 1
                          })}
                        />
                      </div>
                      <div>
                        <Label>Loop Condition (optional)</Label>
                        <Input
                          value={selectedNodeData.config.condition}
                          onChange={(e) => updateNodeConfig(selectedNode!, {
                            ...selectedNodeData.config,
                            condition: e.target.value
                          })}
                          placeholder="e.g., counter < 10"
                        />
                      </div>
                    </div>
                  )}

                  {/* Webhook-specific configuration */}
                  {selectedNodeData.type === 'webhook' && (
                    <div className="space-y-3">
                      <div>
                        <Label>URL</Label>
                        <Input
                          value={selectedNodeData.config.url}
                          onChange={(e) => updateNodeConfig(selectedNode!, {
                            ...selectedNodeData.config,
                            url: e.target.value
                          })}
                          placeholder="https://api.example.com/webhook"
                        />
                      </div>
                      <div>
                        <Label>Method</Label>
                        <Select
                          value={selectedNodeData.config.method}
                          onValueChange={(value) => updateNodeConfig(selectedNode!, {
                            ...selectedNodeData.config,
                            method: value
                          })}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="GET">GET</SelectItem>
                            <SelectItem value="POST">POST</SelectItem>
                            <SelectItem value="PUT">PUT</SelectItem>
                            <SelectItem value="DELETE">DELETE</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  )}

                  {/* Parallel-specific configuration */}
                  {selectedNodeData.type === 'parallel' && (
                    <div>
                      <Label className="flex items-center gap-2">
                        <input
                          type="checkbox"
                          checked={selectedNodeData.config.waitForAll}
                          onChange={(e) => updateNodeConfig(selectedNode!, {
                            ...selectedNodeData.config,
                            waitForAll: e.target.checked
                          })}
                        />
                        Wait for all branches to complete
                      </Label>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          )}
        </div>
      </motion.div>
    </div>
  );
}