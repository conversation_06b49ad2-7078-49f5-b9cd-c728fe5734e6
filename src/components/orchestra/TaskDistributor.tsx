import React, { useState, use<PERSON>emo } from "react";
import {
  Target,
  Plus,
  Play,
  Pause,
  Clock,
  AlertCircle,
  CheckCircle,
  ArrowRight,
  GitBranch,
  Sparkles,
  ChevronDown,
  ChevronRight,
  Users,
  Zap,
  Link,
  Flag,
  Loader2,
  Edit,
  Trash2,
  Co<PERSON>,
  MoreVertical
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Progress } from "@/components/ui/progress";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { cn } from "@/lib/utils";
import type { AgentTask, OrchestratedAgent } from "../AgentOrchestraPanel";

interface TaskDistributorProps {
  tasks: AgentTask[];
  orchestratedAgents: OrchestratedAgent[];
  onTaskCreate: (task: AgentTask) => void;
  onTaskAssign: (task: AgentTask) => void;
  onTaskUpdate: (taskId: string, updates: Partial<AgentTask>) => void;
}

interface TaskTemplate {
  id: string;
  name: string;
  description: string;
  requiredCapabilities: string[];
  priority: AgentTask['priority'];
}

const TASK_TEMPLATES: TaskTemplate[] = [
  {
    id: 'code-review',
    name: 'Code Review',
    description: 'Review code for quality, security, and best practices',
    requiredCapabilities: ['reviewing', 'security'],
    priority: 'medium'
  },
  {
    id: 'test-creation',
    name: 'Test Creation',
    description: 'Create comprehensive unit and integration tests',
    requiredCapabilities: ['testing', 'coding'],
    priority: 'high'
  },
  {
    id: 'documentation',
    name: 'Documentation',
    description: 'Write or update documentation',
    requiredCapabilities: ['documentation'],
    priority: 'medium'
  },
  {
    id: 'performance-optimization',
    name: 'Performance Optimization',
    description: 'Analyze and optimize code performance',
    requiredCapabilities: ['optimization', 'coding'],
    priority: 'high'
  },
  {
    id: 'security-audit',
    name: 'Security Audit',
    description: 'Perform security analysis and fixes',
    requiredCapabilities: ['security', 'reviewing'],
    priority: 'critical'
  }
];

export const TaskDistributor: React.FC<TaskDistributorProps> = ({
  tasks,
  orchestratedAgents,
  onTaskCreate,
  onTaskAssign,
  onTaskUpdate
}) => {
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [newTask, setNewTask] = useState<Partial<AgentTask>>({
    description: '',
    priority: 'medium',
    dependencies: [],
    requiredCapabilities: []
  });
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [filterPriority, setFilterPriority] = useState<string>('all');
  const [expandedTasks, setExpandedTasks] = useState<Set<string>>(new Set());
  const [showDependencyView, setShowDependencyView] = useState(false);

  // AI-suggested task breakdown
  const [taskBreakdownSuggestions, setTaskBreakdownSuggestions] = useState<string[]>([]);
  const [isAnalyzingTask, setIsAnalyzingTask] = useState(false);

  const filteredTasks = useMemo(() => {
    return tasks.filter(task => {
      if (filterStatus !== 'all' && task.status !== filterStatus) return false;
      if (filterPriority !== 'all' && task.priority !== filterPriority) return false;
      return true;
    });
  }, [tasks, filterStatus, filterPriority]);

  const tasksByStatus = useMemo(() => {
    const grouped = {
      pending: [] as AgentTask[],
      'in-progress': [] as AgentTask[],
      completed: [] as AgentTask[],
      failed: [] as AgentTask[]
    };
    
    filteredTasks.forEach(task => {
      grouped[task.status].push(task);
    });
    
    return grouped;
  }, [filteredTasks]);

  const analyzeTaskForBreakdown = async () => {
    setIsAnalyzingTask(true);
    
    // Simulate AI analysis
    setTimeout(() => {
      const suggestions = [
        "Set up development environment",
        "Analyze existing codebase structure",
        "Implement core functionality",
        "Write unit tests",
        "Add integration tests",
        "Update documentation",
        "Perform code review",
        "Deploy to staging"
      ].filter(() => Math.random() > 0.5);
      
      setTaskBreakdownSuggestions(suggestions);
      setIsAnalyzingTask(false);
    }, 1500);
  };

  const handleTemplateSelect = (templateId: string) => {
    const template = TASK_TEMPLATES.find(t => t.id === templateId);
    if (template) {
      setNewTask({
        ...newTask,
        description: template.description,
        priority: template.priority,
        requiredCapabilities: template.requiredCapabilities
      });
      setSelectedTemplate(templateId);
    }
  };

  const handleCreateTask = () => {
    const task: AgentTask = {
      id: `task_${Date.now()}`,
      description: newTask.description || '',
      assignedTo: [],
      dependencies: newTask.dependencies || [],
      priority: newTask.priority || 'medium',
      status: 'pending',
      progress: 0,
      requiredCapabilities: newTask.requiredCapabilities || []
    };
    
    onTaskCreate(task);
    
    // Reset form
    setNewTask({
      description: '',
      priority: 'medium',
      dependencies: [],
      requiredCapabilities: []
    });
    setSelectedTemplate(null);
    setTaskBreakdownSuggestions([]);
    setShowCreateDialog(false);
    
    // Auto-assign if AI routing is enabled
    if (orchestratedAgents.some(oa => oa.status === 'idle')) {
      onTaskAssign(task);
    }
  };

  const handleCreateSubtasks = () => {
    taskBreakdownSuggestions.forEach((suggestion, index) => {
      const subtask: AgentTask = {
        id: `task_${Date.now()}_${index}`,
        description: suggestion,
        assignedTo: [],
        dependencies: index > 0 ? [`task_${Date.now()}_${index - 1}`] : [],
        priority: newTask.priority || 'medium',
        status: 'pending',
        progress: 0,
        requiredCapabilities: []
      };
      
      onTaskCreate(subtask);
    });
    
    setTaskBreakdownSuggestions([]);
    setShowCreateDialog(false);
  };

  const getAgentName = (agentId: string) => {
    const agent = orchestratedAgents.find(oa => String(oa.agent.id) === agentId);
    return agent?.agent.name || 'Unknown Agent';
  };

  const getPriorityIcon = (priority: AgentTask['priority']) => {
    switch (priority) {
      case 'critical': return Flag;
      case 'high': return Zap;
      case 'medium': return Target;
      case 'low': return Clock;
    }
  };

  const getPriorityColor = (priority: AgentTask['priority']) => {
    switch (priority) {
      case 'critical': return 'text-red-500';
      case 'high': return 'text-orange-500';
      case 'medium': return 'text-yellow-500';
      case 'low': return 'text-gray-500';
    }
  };

  const getStatusIcon = (status: AgentTask['status']) => {
    switch (status) {
      case 'pending': return Clock;
      case 'in-progress': return Loader2;
      case 'completed': return CheckCircle;
      case 'failed': return AlertCircle;
    }
  };

  const getStatusColor = (status: AgentTask['status']) => {
    switch (status) {
      case 'pending': return 'text-gray-500';
      case 'in-progress': return 'text-blue-500';
      case 'completed': return 'text-green-500';
      case 'failed': return 'text-red-500';
    }
  };

  const renderTaskCard = (task: AgentTask) => {
    const isExpanded = expandedTasks.has(task.id);
    const PriorityIcon = getPriorityIcon(task.priority);
    const StatusIcon = getStatusIcon(task.status);
    
    return (
      <Card key={task.id} className={cn(
        "transition-all",
        task.status === 'in-progress' && "border-primary/50 shadow-sm"
      )}>
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-1">
              <StatusIcon className={cn("h-4 w-4", getStatusColor(task.status))} />
              <h4 className="font-medium text-sm line-clamp-1">{task.description}</h4>
            </div>
              
              <div className="flex items-center gap-4 text-xs text-muted-foreground">
                <div className="flex items-center gap-1">
                  <PriorityIcon className={cn("h-3 w-3", getPriorityColor(task.priority))} />
                  {task.priority}
                </div>
                
                {task.assignedTo.length > 0 && (
                  <div className="flex items-center gap-1">
                    <Users className="h-3 w-3" />
                    {task.assignedTo.map(id => getAgentName(id)).join(', ')}
                  </div>
                )}
                
                {task.dependencies.length > 0 && (
                  <div className="flex items-center gap-1">
                    <Link className="h-3 w-3" />
                    {task.dependencies.length} deps
                  </div>
                )}
              </div>
            </div>
            
            <div className="flex items-center gap-1">
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={() => {
                  setExpandedTasks(prev => {
                    const next = new Set(prev);
                    if (next.has(task.id)) {
                      next.delete(task.id);
                    } else {
                      next.add(task.id);
                    }
                    return next;
                  });
                }}
              >
                {isExpanded ? (
                  <ChevronDown className="h-4 w-4" />
                ) : (
                  <ChevronRight className="h-4 w-4" />
                )}
              </Button>
              
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon" className="h-8 w-8">
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => {
                    // Edit task
                  }}>
                    <Edit className="h-4 w-4 mr-2" />
                    Edit
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => {
                    // Duplicate task
                    const duplicated: AgentTask = {
                      ...task,
                      id: `task_${Date.now()}`,
                      status: 'pending',
                      progress: 0,
                      assignedTo: []
                    };
                    onTaskCreate(duplicated);
                  }}>
                    <Copy className="h-4 w-4 mr-2" />
                    Duplicate
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem 
                    className="text-destructive"
                    onClick={() => {
                      // Delete task (would need to add this to props)
                    }}
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
          
          {task.status === 'in-progress' && (
            <Progress value={typeof task.progress === 'number' ? task.progress : 0} className="h-2 mt-2" />
          )}
        </CardHeader>
        
        {isExpanded && (
          <CardContent className="pt-0">
            <div className="space-y-3">
              {/* Required Capabilities */}
              {task.requiredCapabilities.length > 0 && (
                <div>
                  <Label className="text-xs">Required Capabilities</Label>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {task.requiredCapabilities.map(cap => (
                      <Badge key={cap} variant="outline" className="text-xs">
                        {cap}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
              
              {/* Dependencies */}
              {task.dependencies.length > 0 && (
                <div>
                  <Label className="text-xs">Dependencies</Label>
                  <div className="space-y-1 mt-1">
                    {task.dependencies.map(depId => {
                      const depTask = tasks.find(t => t.id === depId);
                      return depTask ? (
                        <div key={depId} className="flex items-center gap-2 text-xs">
                          <ArrowRight className="h-3 w-3" />
                          <span className={cn(
                            depTask.status === 'completed' && "line-through text-muted-foreground"
                          )}>
                            {depTask.description}
                          </span>
                        </div>
                      ) : null;
                    })}
                  </div>
                </div>
              )}
              
              {/* Timestamps */}
              <div className="flex items-center gap-4 text-xs text-muted-foreground">
                {task.startedAt && (
                  <div>
                    Started: {new Date(task.startedAt).toLocaleTimeString()}
                  </div>
                )}
                {task.completedAt && (
                  <div>
                    Completed: {new Date(task.completedAt).toLocaleTimeString()}
                  </div>
                )}
              </div>
              
              {/* Actions */}
              <div className="flex gap-2">
                {task.status === 'pending' && (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => onTaskAssign(task)}
                    disabled={orchestratedAgents.every(oa => oa.status !== 'idle')}
                  >
                    <Play className="h-4 w-4 mr-1" />
                    Assign
                  </Button>
                )}
                {task.status === 'in-progress' && (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => onTaskUpdate(task.id, { status: 'pending' })}
                  >
                    <Pause className="h-4 w-4 mr-1" />
                    Pause
                  </Button>
                )}
              </div>
            </div>
          </CardContent>
        )}
      </Card>
    );
  };

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="space-y-3 mb-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Button onClick={() => setShowCreateDialog(true)}>
              <Plus className="h-4 w-4 mr-1" />
              Create Task
            </Button>
            <Button
              variant="outline"
              onClick={() => setShowDependencyView(!showDependencyView)}
            >
              <GitBranch className="h-4 w-4 mr-1" />
              Dependency View
            </Button>
          </div>
          
          <div className="flex items-center gap-2">
            <Select value={filterStatus} onValueChange={setFilterStatus}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="All Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="in-progress">In Progress</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="failed">Failed</SelectItem>
              </SelectContent>
            </Select>
            
            <Select value={filterPriority} onValueChange={setFilterPriority}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="All Priority" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Priority</SelectItem>
                <SelectItem value="critical">Critical</SelectItem>
                <SelectItem value="high">High</SelectItem>
                <SelectItem value="medium">Medium</SelectItem>
                <SelectItem value="low">Low</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        
        {/* Stats */}
        <div className="flex items-center gap-4 text-sm">
          <Badge variant="outline" className="gap-1">
            <Clock className="h-3 w-3" />
            {tasksByStatus.pending.length} Pending
          </Badge>
          <Badge variant="secondary" className="gap-1">
            <Loader2 className="h-3 w-3 animate-spin" />
            {tasksByStatus['in-progress'].length} Active
          </Badge>
          <Badge variant="secondary" className="gap-1">
            <CheckCircle className="h-3 w-3" />
            {tasksByStatus.completed.length} Completed
          </Badge>
          {tasksByStatus.failed.length > 0 && (
            <Badge variant="destructive" className="gap-1">
              <AlertCircle className="h-3 w-3" />
              {tasksByStatus.failed.length} Failed
            </Badge>
          )}
        </div>
      </div>

      {/* Task List or Dependency View */}
      {showDependencyView ? (
        <Card className="flex-1 p-4">
          <div className="h-full flex items-center justify-center text-muted-foreground">
            <div className="text-center">
              <GitBranch className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>Dependency visualization</p>
              <p className="text-sm mt-2">Coming soon...</p>
            </div>
          </div>
        </Card>
      ) : (
        <ScrollArea className="flex-1">
          <div className="space-y-4">
            {/* In Progress Tasks */}
            {tasksByStatus['in-progress'].length > 0 && (
              <div>
                <h4 className="text-sm font-medium mb-2 flex items-center gap-2">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  In Progress
                </h4>
                <div className="space-y-2">
                  {tasksByStatus['in-progress'].map(renderTaskCard)}
                </div>
              </div>
            )}
            
            {/* Pending Tasks */}
            {tasksByStatus.pending.length > 0 && (
              <div>
                <h4 className="text-sm font-medium mb-2 flex items-center gap-2">
                  <Clock className="h-4 w-4" />
                  Pending
                </h4>
                <div className="space-y-2">
                  {tasksByStatus.pending.map(renderTaskCard)}
                </div>
              </div>
            )}
            
            {/* Completed Tasks */}
            {tasksByStatus.completed.length > 0 && (
              <div>
                <h4 className="text-sm font-medium mb-2 flex items-center gap-2">
                  <CheckCircle className="h-4 w-4" />
                  Completed
                </h4>
                <div className="space-y-2 opacity-60">
                  {tasksByStatus.completed.map(renderTaskCard)}
                </div>
              </div>
            )}
            
            {/* Failed Tasks */}
            {tasksByStatus.failed.length > 0 && (
              <div>
                <h4 className="text-sm font-medium mb-2 flex items-center gap-2 text-destructive">
                  <AlertCircle className="h-4 w-4" />
                  Failed
                </h4>
                <div className="space-y-2">
                  {tasksByStatus.failed.map(renderTaskCard)}
                </div>
              </div>
            )}
          </div>
        </ScrollArea>
      )}

      {/* Create Task Dialog */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Create New Task</DialogTitle>
            <p className="text-sm text-muted-foreground">
              Define a task for your agent orchestra to complete
            </p>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            {/* Task Templates */}
            <div>
              <Label>Quick Templates</Label>
              <div className="grid grid-cols-2 gap-2 mt-2">
                {TASK_TEMPLATES.map(template => (
                  <Button
                    key={template.id}
                    variant={selectedTemplate === template.id ? "default" : "outline"}
                    size="sm"
                    className="justify-start"
                    onClick={() => handleTemplateSelect(template.id)}
                  >
                    {template.name}
                  </Button>
                ))}
              </div>
            </div>
            
            {/* Task Description */}
            <div>
              <Label htmlFor="task-description">Description</Label>
              <Textarea
                id="task-description"
                value={newTask.description}
                onChange={(e) => setNewTask({ ...newTask, description: e.target.value })}
                placeholder="Describe what needs to be done..."
                className="min-h-[100px] mt-2"
              />
              {newTask.description && (
                <Button
                  variant="link"
                  size="sm"
                  className="mt-1"
                  onClick={() => analyzeTaskForBreakdown()}
                  disabled={isAnalyzingTask}
                >
                  {isAnalyzingTask ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                      Analyzing...
                    </>
                  ) : (
                    <>
                      <Sparkles className="h-4 w-4 mr-1" />
                      AI Breakdown Suggestions
                    </>
                  )}
                </Button>
              )}
            </div>
            
            {/* AI Suggestions */}
            {taskBreakdownSuggestions.length > 0 && (
              <div className="p-3 bg-muted rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <Label className="text-sm">AI Suggested Subtasks</Label>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleCreateSubtasks}
                  >
                    Create All
                  </Button>
                </div>
                <div className="space-y-1">
                  {taskBreakdownSuggestions.map((suggestion, index) => (
                    <div key={index} className="flex items-center gap-2 text-sm">
                      <ArrowRight className="h-3 w-3 text-muted-foreground" />
                      {suggestion}
                    </div>
                  ))}
                </div>
              </div>
            )}
            
            {/* Priority */}
            <div>
              <Label htmlFor="task-priority">Priority</Label>
              <Select
                value={newTask.priority}
                onValueChange={(value) => setNewTask({ ...newTask, priority: value as AgentTask['priority'] })}
              >
                <SelectTrigger id="task-priority" className="mt-2">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="low">Low</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="high">High</SelectItem>
                  <SelectItem value="critical">Critical</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            {/* Dependencies */}
            {tasks.length > 0 && (
              <div>
                <Label>Dependencies (Optional)</Label>
                <div className="space-y-2 mt-2 max-h-32 overflow-y-auto">
                  {tasks.filter(t => t.status !== 'completed').map(task => (
                    <label
                      key={task.id}
                      className="flex items-center gap-2 text-sm cursor-pointer"
                    >
                      <input
                        type="checkbox"
                        checked={newTask.dependencies?.includes(task.id) || false}
                        onChange={(e) => {
                          const deps = newTask.dependencies || [];
                          if (e.target.checked) {
                            setNewTask({ ...newTask, dependencies: [...deps, task.id] });
                          } else {
                            setNewTask({ ...newTask, dependencies: deps.filter(d => d !== task.id) });
                          }
                        }}
                        className="rounded"
                      />
                      {task.description}
                    </label>
                  ))}
                </div>
              </div>
            )}
          </div>
          
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreateTask} disabled={!newTask.description}>
              Create Task
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};
