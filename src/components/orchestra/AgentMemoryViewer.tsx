import React, { useState } from "react";
import {
  Brain,
  TrendingUp,
  Lightbulb,
  Target,
  CheckCircle,
  XCircle,
  Clock,
  Sparkles,
  Settings,
  Download,
  Upload,
  Trash2,
  RefreshCw,
  Edit,
  Save,
  Plus,
  Search,
  BookOpen
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import type { OrchestratedAgent, AgentMemory } from "../AgentOrchestraPanel";

interface AgentMemoryViewerProps {
  orchestratedAgents: OrchestratedAgent[];
  onMemoryUpdate: (agentId: string, memory: AgentMemory) => void;
}

interface MemoryInsight {
  type: 'pattern' | 'preference' | 'learning' | 'improvement';
  title: string;
  description: string;
  confidence: number;
  timestamp: string;
  relatedTasks: string[];
}

interface TaskHistory {
  taskId: string;
  description: string;
  status: 'success' | 'failure';
  duration: number;
  timestamp: string;
  learnings: string[];
  performance: {
    speed: number;
    accuracy: number;
    efficiency: number;
  };
}

export const AgentMemoryViewer: React.FC<AgentMemoryViewerProps> = ({
  orchestratedAgents,
  onMemoryUpdate
}) => {
  const [selectedAgent, setSelectedAgent] = useState<string | null>(
    orchestratedAgents[0]?.agent.id !== undefined ? String(orchestratedAgents[0].agent.id) : null
  );
  const [editingPreferences, setEditingPreferences] = useState(false);
  const [showAddLearning, setShowAddLearning] = useState(false);
  const [newLearning, setNewLearning] = useState("");
  const [filterType, setFilterType] = useState<string>("all");
  const [searchQuery, setSearchQuery] = useState("");

  const selectedAgentData = orchestratedAgents.find(
    (oa) => String(oa.agent.id) === selectedAgent
  );

  // Generate mock insights for demonstration
  const generateInsights = (agent: OrchestratedAgent): MemoryInsight[] => {
    const insights: MemoryInsight[] = [];
    
    if (agent.memory.successfulTasks > 5) {
      insights.push({
        type: 'pattern',
        title: 'Consistent Success Pattern',
        description: `${agent.agent.name} shows high success rate in ${agent.performance.specialties[0] || 'general'} tasks`,
        confidence: 0.85,
        timestamp: new Date().toISOString(),
        relatedTasks: []
      });
    }
    
    if (agent.performance.tokenEfficiency > 0.7) {
      insights.push({
        type: 'improvement',
        title: 'Token Efficiency Improvement',
        description: 'Agent has optimized token usage by 30% over last 10 tasks',
        confidence: 0.92,
        timestamp: new Date().toISOString(),
        relatedTasks: []
      });
    }
    
    return insights;
  };

  // Generate mock task history
  const generateTaskHistory = (agent: OrchestratedAgent): TaskHistory[] => {
    const history: TaskHistory[] = [];
    
    for (let i = 0; i < agent.memory.totalTasks; i++) {
      const isSuccess = i < agent.memory.successfulTasks;
      history.push({
        taskId: `task_${i}`,
        description: `Sample task ${i + 1}`,
        status: isSuccess ? 'success' : 'failure',
        duration: Math.floor(Math.random() * 300) + 60,
        timestamp: new Date(Date.now() - i * 3600000).toISOString(),
        learnings: isSuccess ? [`Learning from task ${i + 1}`] : [],
        performance: {
          speed: Math.random(),
          accuracy: isSuccess ? 0.8 + Math.random() * 0.2 : 0.3 + Math.random() * 0.4,
          efficiency: Math.random()
        }
      });
    }
    
    return history;
  };

  const handleAddLearning = () => {
    if (!selectedAgentData || !newLearning.trim()) return;
    
    const updatedMemory: AgentMemory = {
      ...selectedAgentData.memory,
      learnings: [...selectedAgentData.memory.learnings, newLearning],
      lastUpdated: new Date().toISOString()
    };
    
    onMemoryUpdate(selectedAgent!, updatedMemory);
    setNewLearning("");
    setShowAddLearning(false);
  };

  const handleDeleteLearning = (index: number) => {
    if (!selectedAgentData) return;
    
    const updatedMemory: AgentMemory = {
      ...selectedAgentData.memory,
      learnings: selectedAgentData.memory.learnings.filter((_, i) => i !== index),
      lastUpdated: new Date().toISOString()
    };
    
    onMemoryUpdate(selectedAgent!, updatedMemory);
  };

  const handleResetMemory = () => {
    if (!selectedAgentData) return;
    
    const resetMemory: AgentMemory = {
      totalTasks: 0,
      successfulTasks: 0,
      failedTasks: 0,
      learnings: [],
      preferences: {},
      knownPatterns: [],
      lastUpdated: new Date().toISOString()
    };
    
    onMemoryUpdate(selectedAgent!, resetMemory);
  };

  const exportMemory = () => {
    if (!selectedAgentData) return;
    
    const exportData = {
      agent: {
        id: selectedAgentData.agent.id,
        name: selectedAgentData.agent.name
      },
      memory: selectedAgentData.memory,
      performance: selectedAgentData.performance,
      exportedAt: new Date().toISOString()
    };
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `agent_memory_${selectedAgentData.agent.id}_${Date.now()}.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  if (!selectedAgentData) {
    return (
      <div className="flex items-center justify-center h-full text-muted-foreground">
        <div className="text-center">
          <Brain className="h-12 w-12 mx-auto mb-4 opacity-50" />
          <p>No agents available</p>
        </div>
      </div>
    );
  }

  const insights = generateInsights(selectedAgentData);
  const taskHistory = generateTaskHistory(selectedAgentData);

  return (
    <div className="flex flex-col h-full">
      {/* Agent Selector */}
      <div className="flex items-center justify-between mb-4">
        <Select value={selectedAgent || ""} onValueChange={setSelectedAgent}>
          <SelectTrigger className="w-64">
            <SelectValue placeholder="Select an agent" />
          </SelectTrigger>
          <SelectContent>
            {orchestratedAgents.map(oa => (
              <SelectItem key={oa.agent.id} value={String(oa.agent.id)}>
                <div className="flex items-center gap-2">
                  <Brain className="h-4 w-4" />
                  {oa.agent.name}
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        
        <div className="flex items-center gap-2">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="outline" size="icon" onClick={exportMemory}>
                  <Download className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Export Memory</TooltipContent>
            </Tooltip>
          </TooltipProvider>
          
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="outline" size="icon">
                  <Upload className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Import Memory</TooltipContent>
            </Tooltip>
          </TooltipProvider>
          
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button 
                  variant="outline" 
                  size="icon"
                  onClick={handleResetMemory}
                >
                  <RefreshCw className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Reset Memory</TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>

      {/* Memory Overview */}
      <div className="grid grid-cols-4 gap-4 mb-6">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Tasks</p>
                <p className="text-2xl font-bold">{selectedAgentData.memory.totalTasks}</p>
              </div>
              <Target className="h-8 w-8 text-muted-foreground opacity-50" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Success Rate</p>
                <p className="text-2xl font-bold">
                  {selectedAgentData.memory.totalTasks > 0
                    ? Math.round((selectedAgentData.memory.successfulTasks / selectedAgentData.memory.totalTasks) * 100)
                    : 0}%
                </p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-500 opacity-50" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Learnings</p>
                <p className="text-2xl font-bold">{selectedAgentData.memory.learnings.length}</p>
              </div>
              <Lightbulb className="h-8 w-8 text-yellow-500 opacity-50" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Patterns</p>
                <p className="text-2xl font-bold">{selectedAgentData.memory.knownPatterns.length}</p>
              </div>
              <Brain className="h-8 w-8 text-purple-500 opacity-50" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tabs */}
      <Tabs defaultValue="insights" className="flex-1 flex flex-col">
        <TabsList>
          <TabsTrigger value="insights">Insights</TabsTrigger>
          <TabsTrigger value="learnings">Learnings</TabsTrigger>
          <TabsTrigger value="history">Task History</TabsTrigger>
          <TabsTrigger value="preferences">Preferences</TabsTrigger>
          <TabsTrigger value="patterns">Patterns</TabsTrigger>
        </TabsList>

        <TabsContent value="insights" className="flex-1 overflow-hidden">
          <ScrollArea className="h-full">
            <div className="space-y-4">
              {insights.length === 0 ? (
                <Card>
                  <CardContent className="py-8 text-center text-muted-foreground">
                    <Sparkles className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No insights available yet</p>
                    <p className="text-sm mt-2">Insights will appear as the agent completes more tasks</p>
                  </CardContent>
                </Card>
              ) : (
                insights.map((insight, index) => (
                  <Card key={index}>
                    <CardHeader className="pb-3">
                      <div className="flex items-start justify-between">
                        <div className="flex items-center gap-2">
                          {insight.type === 'pattern' && <Brain className="h-4 w-4 text-purple-500" />}
                          {insight.type === 'preference' && <Settings className="h-4 w-4 text-blue-500" />}
                          {insight.type === 'learning' && <Lightbulb className="h-4 w-4 text-yellow-500" />}
                          {insight.type === 'improvement' && <TrendingUp className="h-4 w-4 text-green-500" />}
                          <CardTitle className="text-base">{insight.title}</CardTitle>
                        </div>
                        <Badge variant="outline">
                          {Math.round(insight.confidence * 100)}% confidence
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-muted-foreground">{insight.description}</p>
                      <div className="flex items-center gap-2 mt-3 text-xs text-muted-foreground">
                        <Clock className="h-3 w-3" />
                        {new Date(insight.timestamp).toLocaleString()}
                      </div>
                    </CardContent>
                  </Card>
                ))
              )}
            </div>
          </ScrollArea>
        </TabsContent>

        <TabsContent value="learnings" className="flex-1 overflow-hidden">
          <div className="flex flex-col h-full">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-2 flex-1">
                <Search className="h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search learnings..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="max-w-xs"
                />
              </div>
              <Button
                size="sm"
                onClick={() => setShowAddLearning(true)}
              >
                <Plus className="h-4 w-4 mr-1" />
                Add Learning
              </Button>
            </div>
            
            <ScrollArea className="flex-1">
              <div className="space-y-3">
                {selectedAgentData.memory.learnings.length === 0 ? (
                  <Card>
                    <CardContent className="py-8 text-center text-muted-foreground">
                      <BookOpen className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>No learnings recorded yet</p>
                      <Button
                        variant="outline"
                        size="sm"
                        className="mt-4"
                        onClick={() => setShowAddLearning(true)}
                      >
                        Add First Learning
                      </Button>
                    </CardContent>
                  </Card>
                ) : (
                  selectedAgentData.memory.learnings
                    .filter(learning => 
                      searchQuery === "" || 
                      learning.toLowerCase().includes(searchQuery.toLowerCase())
                    )
                    .map((learning, index) => (
                      <Card key={index}>
                        <CardContent className="py-3">
                          <div className="flex items-start justify-between gap-3">
                            <div className="flex items-start gap-2 flex-1">
                              <Lightbulb className="h-4 w-4 text-yellow-500 mt-0.5" />
                              <p className="text-sm">{learning}</p>
                            </div>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8"
                              onClick={() => handleDeleteLearning(index)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    ))
                )}
              </div>
            </ScrollArea>
          </div>
        </TabsContent>

        <TabsContent value="history" className="flex-1 overflow-hidden">
          <div className="flex flex-col h-full">
            <div className="flex items-center gap-2 mb-4">
              <Select value={filterType} onValueChange={setFilterType}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Tasks</SelectItem>
                  <SelectItem value="success">Successful</SelectItem>
                  <SelectItem value="failure">Failed</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <ScrollArea className="flex-1">
              <div className="space-y-3">
                {taskHistory
                  .filter(task => 
                    filterType === "all" || 
                    (filterType === "success" && task.status === "success") ||
                    (filterType === "failure" && task.status === "failure")
                  )
                  .map((task) => (
                    <Card key={task.taskId}>
                      <CardHeader className="pb-3">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            {task.status === 'success' ? (
                              <CheckCircle className="h-4 w-4 text-green-500" />
                            ) : (
                              <XCircle className="h-4 w-4 text-red-500" />
                            )}
                            <span className="font-medium text-sm">{task.description}</span>
                          </div>
                          <Badge variant={task.status === 'success' ? 'default' : 'destructive'}>
                            {task.status}
                          </Badge>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-3 gap-4 text-xs">
                          <div>
                            <span className="text-muted-foreground">Duration</span>
                            <p className="font-medium">{Math.floor(task.duration / 60)}m {task.duration % 60}s</p>
                          </div>
                          <div>
                            <span className="text-muted-foreground">Accuracy</span>
                            <p className="font-medium">{Math.round(task.performance.accuracy * 100)}%</p>
                          </div>
                          <div>
                            <span className="text-muted-foreground">Efficiency</span>
                            <p className="font-medium">{Math.round(task.performance.efficiency * 100)}%</p>
                          </div>
                        </div>
                        {task.learnings.length > 0 && (
                          <div className="mt-3 pt-3 border-t">
                            <p className="text-xs text-muted-foreground mb-1">Learnings:</p>
                            {task.learnings.map((learning, idx) => (
                              <div key={idx} className="flex items-center gap-1 text-xs">
                                <Lightbulb className="h-3 w-3 text-yellow-500" />
                                {learning}
                              </div>
                            ))}
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  ))}
              </div>
            </ScrollArea>
          </div>
        </TabsContent>

        <TabsContent value="preferences" className="flex-1">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Agent Preferences</CardTitle>
                <Button
                  variant={editingPreferences ? "default" : "outline"}
                  size="sm"
                  onClick={() => setEditingPreferences(!editingPreferences)}
                >
                  {editingPreferences ? (
                    <>
                      <Save className="h-4 w-4 mr-1" />
                      Save
                    </>
                  ) : (
                    <>
                      <Edit className="h-4 w-4 mr-1" />
                      Edit
                    </>
                  )}
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {Object.keys(selectedAgentData.memory.preferences).length === 0 ? (
                <p className="text-sm text-muted-foreground">No preferences configured</p>
              ) : (
                <div className="space-y-3">
                  {Object.entries(selectedAgentData.memory.preferences).map(([key, value]) => (
                    <div key={key}>
                      <Label className="text-sm">{key}</Label>
                      <Input
                        value={JSON.stringify(value)}
                        readOnly={!editingPreferences}
                        className="mt-1"
                      />
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="patterns" className="flex-1">
          <ScrollArea className="h-full">
            <div className="space-y-3">
              {selectedAgentData.memory.knownPatterns.length === 0 ? (
                <Card>
                  <CardContent className="py-8 text-center text-muted-foreground">
                    <Brain className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No patterns identified yet</p>
                    <p className="text-sm mt-2">Patterns will emerge as the agent learns</p>
                  </CardContent>
                </Card>
              ) : (
                selectedAgentData.memory.knownPatterns.map((pattern, index) => (
                  <Card key={index}>
                    <CardContent className="py-3">
                      <div className="flex items-center gap-2">
                        <Brain className="h-4 w-4 text-purple-500" />
                        <p className="text-sm">{pattern}</p>
                      </div>
                    </CardContent>
                  </Card>
                ))
              )}
            </div>
          </ScrollArea>
        </TabsContent>
      </Tabs>

      {/* Add Learning Dialog */}
      <Dialog open={showAddLearning} onOpenChange={setShowAddLearning}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add New Learning</DialogTitle>
            <DialogDescription>
              Record a new learning or insight for {selectedAgentData.agent.name}
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <Textarea
              value={newLearning}
              onChange={(e) => setNewLearning(e.target.value)}
              placeholder="Describe what the agent has learned..."
              rows={4}
            />
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowAddLearning(false)}>
              Cancel
            </Button>
            <Button onClick={handleAddLearning} disabled={!newLearning.trim()}>
              Add Learning
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};
