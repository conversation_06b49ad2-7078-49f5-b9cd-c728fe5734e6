import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Users2,
  MessageSquare,
  Share2,
  ArrowLeftRight,
  Zap,
  Activity,
  Brain,
  Shield,
  Code,
  FileText,
  GitBranch,
  Eye,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";
import type { OrchestratedAgent } from "../AgentOrchestraPanel";

interface CollaborationIndicatorProps {
  orchestratedAgents: OrchestratedAgent[];
  onCollaborationClick?: (agent1Id: string, agent2Id: string) => void;
}

interface CollaborationLink {
  id: string;
  agent1Id: string;
  agent2Id: string;
  type: 'active' | 'pending' | 'completed';
  strength: number; // 0-1
  messages: number;
  sharedArtifacts: number;
  startTime: string;
  lastActivity: string;
}

interface CollaborationMessage {
  fromAgent: string;
  toAgent: string;
  type: 'request' | 'response' | 'share' | 'sync';
  content: string;
  timestamp: string;
}

export const CollaborationIndicator: React.FC<CollaborationIndicatorProps> = ({
  orchestratedAgents,
  onCollaborationClick
}) => {
  const [collaborationLinks, setCollaborationLinks] = useState<CollaborationLink[]>([]);
  const [recentMessages, setRecentMessages] = useState<CollaborationMessage[]>([]);
  const [selectedLink, setSelectedLink] = useState<string | null>(null);
  const [showDetails, setShowDetails] = useState(false);

  // Generate collaboration links based on agent status
  useEffect(() => {
    const links: CollaborationLink[] = [];
    
    orchestratedAgents.forEach((agent1, i) => {
      orchestratedAgents.slice(i + 1).forEach(agent2 => {
        // Check if agents are collaborating
        if (
          agent1.collaboratingWith?.includes(agent2.agent.id as never) ||
          agent2.collaboratingWith?.includes(agent1.agent.id as never)
        ) {
          links.push({
            id: `${agent1.agent.id}_${agent2.agent.id}`,
            agent1Id: String(agent1.agent.id),
            agent2Id: String(agent2.agent.id),
            type: agent1.status === 'working' && agent2.status === 'working' ? 'active' : 'pending',
            strength: calculateCollaborationStrength(agent1, agent2),
            messages: Math.floor(Math.random() * 20) + 1,
            sharedArtifacts: Math.floor(Math.random() * 5),
            startTime: new Date(Date.now() - Math.random() * 3600000).toISOString(),
            lastActivity: new Date().toISOString()
          });
        }
      });
    });
    
    setCollaborationLinks(links);
  }, [orchestratedAgents]);

  // Simulate real-time messages
  useEffect(() => {
    if (collaborationLinks.length === 0) return;
    
    const interval = setInterval(() => {
      const activeLinks = collaborationLinks.filter(link => link.type === 'active');
      if (activeLinks.length > 0) {
        const randomLink = activeLinks[Math.floor(Math.random() * activeLinks.length)];
        const agent1 = orchestratedAgents.find(a => String(a.agent.id) === randomLink.agent1Id);
        const agent2 = orchestratedAgents.find(a => String(a.agent.id) === randomLink.agent2Id);
        
        if (agent1 && agent2) {
          const messageTypes: CollaborationMessage['type'][] = ['request', 'response', 'share', 'sync'];
          const newMessage: CollaborationMessage = {
            fromAgent: String(Math.random() > 0.5 ? agent1.agent.id : agent2.agent.id),
            toAgent: String(Math.random() > 0.5 ? agent2.agent.id : agent1.agent.id),
            type: messageTypes[Math.floor(Math.random() * messageTypes.length)],
            content: generateMessageContent(messageTypes[Math.floor(Math.random() * messageTypes.length)]),
            timestamp: new Date().toISOString()
          };
          
          setRecentMessages(prev => [newMessage, ...prev].slice(0, 10));
        }
      }
    }, 5000);
    
    return () => clearInterval(interval);
  }, [collaborationLinks, orchestratedAgents]);

  const calculateCollaborationStrength = (agent1: OrchestratedAgent, agent2: OrchestratedAgent): number => {
    let strength = 0;
    
    // Check specialty overlap
    const specialtyOverlap = agent1.performance.specialties.filter(s => 
      agent2.performance.specialties.includes(s)
    ).length;
    strength += specialtyOverlap * 0.2;
    
    // Check if both are actively working
    if (agent1.status === 'working' && agent2.status === 'working') {
      strength += 0.3;
    }
    
    // Check collaboration score
    strength += (agent1.performance.collaborationScore + agent2.performance.collaborationScore) / 2 * 0.5;
    
    return Math.min(strength, 1);
  };

  const generateMessageContent = (type: CollaborationMessage['type']): string => {
    const messages = {
      request: [
        "Need assistance with code review",
        "Can you validate this approach?",
        "Requesting performance optimization insights"
      ],
      response: [
        "Analysis complete, sharing results",
        "Confirmed, approach looks optimal",
        "Here are my recommendations"
      ],
      share: [
        "Sharing discovered pattern",
        "Forwarding relevant artifact",
        "Distributing performance metrics"
      ],
      sync: [
        "Synchronizing task progress",
        "Updating shared context",
        "Aligning on next steps"
      ]
    };
    
    const options = messages[type];
    return options[Math.floor(Math.random() * options.length)];
  };

  const getAgentIcon = (agent: OrchestratedAgent) => {
    // Simple icon mapping based on agent specialties
    if (agent.performance.specialties.includes('coding')) return Code;
    if (agent.performance.specialties.includes('reviewing')) return Shield;
    if (agent.performance.specialties.includes('optimization')) return Zap;
    if (agent.performance.specialties.includes('documentation')) return FileText;
    return Brain;
  };

  const getMessageIcon = (type: CollaborationMessage['type']) => {
    switch (type) {
      case 'request': return MessageSquare;
      case 'response': return ArrowLeftRight;
      case 'share': return Share2;
      case 'sync': return GitBranch;
    }
  };

  const activeCollaborations = collaborationLinks.filter(link => link.type === 'active').length;
  const totalMessages = collaborationLinks.reduce((sum, link) => sum + link.messages, 0);

  return (
    <div className="space-y-4">
      {/* Summary Stats */}
      <div className="grid grid-cols-3 gap-3">
        <Card>
          <CardContent className="p-3">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs text-muted-foreground">Active</p>
                <p className="text-lg font-semibold">{activeCollaborations}</p>
              </div>
              <Activity className="h-4 w-4 text-green-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-3">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs text-muted-foreground">Messages</p>
                <p className="text-lg font-semibold">{totalMessages}</p>
              </div>
              <MessageSquare className="h-4 w-4 text-blue-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-3">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs text-muted-foreground">Agents</p>
                <p className="text-lg font-semibold">{orchestratedAgents.length}</p>
              </div>
              <Users2 className="h-4 w-4 text-purple-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Collaboration Network */}
      {collaborationLinks.length > 0 && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between mb-3">
              <h4 className="text-sm font-medium">Collaboration Network</h4>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowDetails(!showDetails)}
              >
                <Eye className="h-4 w-4 mr-1" />
                {showDetails ? "Hide" : "Show"} Details
              </Button>
            </div>
            
            {/* Visual Network */}
            <div className="relative h-48 bg-muted/20 rounded-lg mb-3">
              {/* Simple visualization - in production, use a proper graph library */}
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="relative">
                  {orchestratedAgents.map((agent, index) => {
                    const angle = (index / orchestratedAgents.length) * 2 * Math.PI;
                    const radius = 60;
                    const x = Math.cos(angle) * radius;
                    const y = Math.sin(angle) * radius;
                    const Icon = getAgentIcon(agent);
                    
                    return (
                      <TooltipProvider key={agent.agent.id}>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <motion.div
                              className={cn(
                                "absolute w-10 h-10 rounded-full flex items-center justify-center cursor-pointer",
                                "bg-background border-2 shadow-sm",
                                agent.status === 'working' ? "border-primary" : "border-border"
                              )}
                              style={{
                                left: `calc(50% + ${x}px - 20px)`,
                                top: `calc(50% + ${y}px - 20px)`
                              }}
                              whileHover={{ scale: 1.1 }}
                              onClick={() => {
                                // Find collaborations for this agent
                                const agentLinks = collaborationLinks.filter(link => 
                                  link.agent1Id === String(agent.agent.id) || link.agent2Id === String(agent.agent.id)
                                );
                                if (agentLinks.length > 0) {
                                  setSelectedLink(agentLinks[0].id);
                                }
                              }}
                            >
                              <Icon className="h-4 w-4" />
                            </motion.div>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p className="font-medium">{agent.agent.name}</p>
                            <p className="text-xs">{agent.status}</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    );
                  })}
                  
                  {/* Collaboration Lines */}
                  <svg className="absolute inset-0 pointer-events-none" style={{ width: '200px', height: '200px', left: 'calc(50% - 100px)', top: 'calc(50% - 100px)' }}>
                    {collaborationLinks.map(link => {
                      const agent1Index = orchestratedAgents.findIndex(a => String(a.agent.id) === link.agent1Id);
                      const agent2Index = orchestratedAgents.findIndex(a => String(a.agent.id) === link.agent2Id);
                      
                      if (agent1Index === -1 || agent2Index === -1) return null;
                      
                      const angle1 = (agent1Index / orchestratedAgents.length) * 2 * Math.PI;
                      const angle2 = (agent2Index / orchestratedAgents.length) * 2 * Math.PI;
                      const radius = 60;
                      
                      const x1 = Math.cos(angle1) * radius + 100;
                      const y1 = Math.sin(angle1) * radius + 100;
                      const x2 = Math.cos(angle2) * radius + 100;
                      const y2 = Math.sin(angle2) * radius + 100;
                      
                      return (
                        <motion.line
                          key={link.id}
                          x1={x1}
                          y1={y1}
                          x2={x2}
                          y2={y2}
                          stroke={
                            link.type === 'active' ? '#22c55e' :
                            link.type === 'pending' ? '#f59e0b' :
                            '#6b7280'
                          }
                          strokeWidth={Math.max(1, link.strength * 3)}
                          strokeDasharray={link.type === 'pending' ? "5 5" : "none"}
                          initial={{ pathLength: 0 }}
                          animate={{ pathLength: 1 }}
                          transition={{ duration: 0.5 }}
                        />
                      );
                    })}
                  </svg>
                </div>
              </div>
            </div>
            
            {/* Collaboration Details */}
            {showDetails && (
              <div className="space-y-2">
                {collaborationLinks.map(link => {
                  const agent1 = orchestratedAgents.find(a => String(a.agent.id) === link.agent1Id);
                  const agent2 = orchestratedAgents.find(a => String(a.agent.id) === link.agent2Id);
                  
                  if (!agent1 || !agent2) return null;
                  
                  return (
                    <Card
                      key={link.id}
                      className={cn(
                        "cursor-pointer transition-all",
                        selectedLink === link.id && "ring-2 ring-primary"
                      )}
                      onClick={() => {
                        setSelectedLink(link.id);
                        onCollaborationClick?.(link.agent1Id, link.agent2Id);
                      }}
                    >
                      <CardContent className="p-3">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <Badge variant={
                              link.type === 'active' ? 'default' :
                              link.type === 'pending' ? 'secondary' :
                              'outline'
                            } className="text-xs">
                              {link.type}
                            </Badge>
                            <span className="text-sm font-medium">
                              {agent1.agent.name} ↔ {agent2.agent.name}
                            </span>
                          </div>
                          <div className="flex items-center gap-3 text-xs text-muted-foreground">
                            <span>{link.messages} msgs</span>
                            <span>{link.sharedArtifacts} artifacts</span>
                          </div>
                        </div>
                        
                        <Progress value={link.strength * 100} className="h-1" />
                        
                        <div className="flex items-center gap-4 mt-2 text-xs text-muted-foreground">
                          <span>Started {new Date(link.startTime).toLocaleTimeString()}</span>
                          <span>•</span>
                          <span>Last activity {new Date(link.lastActivity).toLocaleTimeString()}</span>
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Recent Messages */}
      {recentMessages.length > 0 && (
        <Card>
          <CardContent className="p-4">
            <h4 className="text-sm font-medium mb-3">Recent Communications</h4>
            <div className="space-y-2">
              <AnimatePresence>
                {recentMessages.slice(0, 5).map((message, index) => {
                  const fromAgent = orchestratedAgents.find(a => String(a.agent.id) === message.fromAgent);
                  const toAgent = orchestratedAgents.find(a => String(a.agent.id) === message.toAgent);
                  const MessageIcon = getMessageIcon(message.type);
                  
                  return (
                    <motion.div
                      key={`${message.timestamp}_${index}`}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: 20 }}
                      className="flex items-center gap-2 text-sm"
                    >
                      <MessageIcon className="h-3 w-3 text-muted-foreground" />
                      <span className="font-medium">{fromAgent?.agent.name}</span>
                      <ArrowLeftRight className="h-3 w-3 text-muted-foreground" />
                      <span className="font-medium">{toAgent?.agent.name}</span>
                      <span className="text-muted-foreground flex-1 truncate">
                        {message.content}
                      </span>
                      <span className="text-xs text-muted-foreground">
                        {new Date(message.timestamp).toLocaleTimeString()}
                      </span>
                    </motion.div>
                  );
                })}
              </AnimatePresence>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Empty State */}
      {collaborationLinks.length === 0 && (
        <Card>
          <CardContent className="py-8 text-center text-muted-foreground">
            <Users2 className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>No active collaborations</p>
            <p className="text-sm mt-2">Agents will collaborate when working on related tasks</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
