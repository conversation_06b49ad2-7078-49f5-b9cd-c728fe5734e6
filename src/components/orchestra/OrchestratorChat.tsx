import React, { useState, useRef, useEffect } from "react";
import { motion } from "framer-motion";
import {
  Send,
  Sparkles,
  Command,
  History,
  Bot,
  User,
  Loader2,
  CheckCircle,
  AlertCircle,
  Info,
  Wand2,
  Users,
  Target,
  GitBranch,
  HelpCircle,
  Clock,
  X
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Popover } from "@/components/ui/popover";
import { cn } from "@/lib/utils";

interface OrchestratorChatProps {
  onCommand: (command: string) => void;
  commandHistory: string[];
  className?: string;
}

interface ChatMessage {
  id: string;
  type: 'user' | 'system' | 'agent';
  content: string;
  timestamp: string;
  status?: 'pending' | 'success' | 'error';
  agentId?: string;
  suggestions?: string[];
}

interface CommandSuggestion {
  command: string;
  description: string;
  category: 'agent' | 'task' | 'workflow' | 'collaboration' | 'utility';
  icon: React.ComponentType<any>;
}

const COMMAND_SUGGESTIONS: CommandSuggestion[] = [
  // Agent Commands
  {
    command: "/add [agent-name]",
    description: "Add an agent to the orchestra",
    category: "agent",
    icon: Bot
  },
  {
    command: "/remove [agent-id]",
    description: "Remove an agent from the orchestra",
    category: "agent",
    icon: Bot
  },
  {
    command: "/agents",
    description: "List all active agents",
    category: "agent",
    icon: Users
  },
  
  // Task Commands
  {
    command: "/task [agent] \"description\"",
    description: "Assign a task to a specific agent",
    category: "task",
    icon: Target
  },
  {
    command: "/distribute \"task\"",
    description: "Let AI distribute task to best agent",
    category: "task",
    icon: Wand2
  },
  {
    command: "/tasks",
    description: "Show all tasks and their status",
    category: "task",
    icon: Target
  },
  
  // Workflow Commands
  {
    command: "/workflow save \"name\"",
    description: "Save current setup as workflow",
    category: "workflow",
    icon: GitBranch
  },
  {
    command: "/workflow load \"name\"",
    description: "Load a saved workflow",
    category: "workflow",
    icon: GitBranch
  },
  
  // Collaboration Commands
  {
    command: "/collaborate [agent1] [agent2]",
    description: "Set up collaboration between agents",
    category: "collaboration",
    icon: Users
  },
  {
    command: "/isolate [agent]",
    description: "Remove agent from collaborations",
    category: "collaboration",
    icon: Bot
  },
  
  // Utility Commands
  {
    command: "/status",
    description: "Show orchestra status overview",
    category: "utility",
    icon: Info
  },
  {
    command: "/clear",
    description: "Clear completed tasks",
    category: "utility",
    icon: CheckCircle
  },
  {
    command: "/help",
    description: "Show available commands",
    category: "utility",
    icon: HelpCircle
  }
];

const NATURAL_LANGUAGE_EXAMPLES = [
  "Add the code reviewer and test writer agents to help with the API refactoring",
  "Create a task to optimize the database queries and assign it to the best available agent",
  "Show me which agents are currently collaborating",
  "Set up a workflow for code review with security scanning",
  "Help the optimizer agent work together with the code writer",
  "What's the status of all running tasks?",
  "Save this agent configuration as 'API Development Team'"
];

export const OrchestratorChat: React.FC<OrchestratorChatProps> = ({
  onCommand,
  commandHistory,
  className
}) => {
  const [input, setInput] = useState("");
  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      id: 'welcome',
      type: 'system',
      content: "👋 I'm your Orchestra Assistant. I can help you manage agents, create tasks, and coordinate workflows. Use natural language or type / for commands.",
      timestamp: new Date().toISOString()
    }
  ]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [showHistory, setShowHistory] = useState(false);
  const [selectedSuggestion, setSelectedSuggestion] = useState(0);
  
  const inputRef = useRef<HTMLInputElement>(null);
  const scrollRef = useRef<HTMLDivElement>(null);

  // Filter suggestions based on input
  const filteredSuggestions = input.startsWith('/')
    ? COMMAND_SUGGESTIONS.filter(cmd => 
        cmd.command.toLowerCase().includes(input.toLowerCase())
      )
    : [];

  // Handle command submission
  const handleSubmit = async () => {
    if (!input.trim() || isProcessing) return;
    
    const userMessage: ChatMessage = {
      id: `msg_${Date.now()}`,
      type: 'user',
      content: input,
      timestamp: new Date().toISOString()
    };
    
    setMessages(prev => [...prev, userMessage]);
    setIsProcessing(true);
    
    // Process command
    try {
      // Add to history
      onCommand(input);
      
      // Simulate processing
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Generate response
      const response = generateResponse(input);
      setMessages(prev => [...prev, response]);
      
      // Add follow-up suggestions if applicable
      if (response.suggestions) {
        const suggestionMessage: ChatMessage = {
          id: `sug_${Date.now()}`,
          type: 'system',
          content: "Here are some related actions you might want to take:",
          timestamp: new Date().toISOString(),
          suggestions: response.suggestions
        };
        setMessages(prev => [...prev, suggestionMessage]);
      }
    } catch (error) {
      const errorMessage: ChatMessage = {
        id: `err_${Date.now()}`,
        type: 'system',
        content: `Error: ${error instanceof Error ? error.message : 'Command failed'}`,
        timestamp: new Date().toISOString(),
        status: 'error'
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsProcessing(false);
      setInput("");
    }
  };

  const generateResponse = (command: string): ChatMessage => {
    const lowerCommand = command.toLowerCase();
    
    // Command responses
    if (command.startsWith('/')) {
      const [cmd, ...args] = command.slice(1).split(' ');
      
      switch (cmd) {
        case 'add':
          return {
            id: `res_${Date.now()}`,
            type: 'system',
            content: `✅ Added agent "${args.join(' ')}" to the orchestra`,
            timestamp: new Date().toISOString(),
            status: 'success',
            suggestions: ['/agents', '/task ' + args.join(' ') + ' "']
          };
          
        case 'task':
          return {
            id: `res_${Date.now()}`,
            type: 'system',
            content: `📋 Task created and assigned to ${args[0]}`,
            timestamp: new Date().toISOString(),
            status: 'success',
            suggestions: ['/tasks', '/status']
          };
          
        case 'status':
          return {
            id: `res_${Date.now()}`,
            type: 'system',
            content: `📊 Orchestra Status:\n• 3 active agents\n• 2 tasks in progress\n• 1 completed task\n• 2 active collaborations`,
            timestamp: new Date().toISOString(),
            status: 'success'
          };
          
        case 'help':
          return {
            id: `res_${Date.now()}`,
            type: 'system',
            content: `📚 Available commands:\n${COMMAND_SUGGESTIONS.map(s => `• ${s.command} - ${s.description}`).join('\n')}`,
            timestamp: new Date().toISOString(),
            status: 'success'
          };
          
        default:
          return {
            id: `res_${Date.now()}`,
            type: 'system',
            content: `Processing command: ${command}`,
            timestamp: new Date().toISOString(),
            status: 'success'
          };
      }
    }
    
    // Natural language processing
    if (lowerCommand.includes('add') && lowerCommand.includes('agent')) {
      return {
        id: `res_${Date.now()}`,
        type: 'system',
        content: "I'll help you add agents to the orchestra. Which agents would you like to add?",
        timestamp: new Date().toISOString(),
        suggestions: ['/add code-reviewer', '/add test-writer', '/agents']
      };
    }
    
    if (lowerCommand.includes('status') || lowerCommand.includes('what') || lowerCommand.includes('show')) {
      return {
        id: `res_${Date.now()}`,
        type: 'system',
        content: "Here's the current status of your agent orchestra...",
        timestamp: new Date().toISOString(),
        suggestions: ['/status', '/agents', '/tasks']
      };
    }
    
    if (lowerCommand.includes('help') || lowerCommand.includes('how')) {
      return {
        id: `res_${Date.now()}`,
        type: 'system',
        content: "I can help you manage agents, create tasks, and coordinate workflows. You can use natural language or slash commands. Type /help for a list of commands.",
        timestamp: new Date().toISOString(),
        suggestions: ['/help', '/agents', '/task']
      };
    }
    
    // Default AI response
    return {
      id: `res_${Date.now()}`,
      type: 'system',
      content: `I understand you want to: "${command}". Let me process that for you...`,
      timestamp: new Date().toISOString(),
      status: 'success',
      suggestions: ['/help', '/status']
    };
  };

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Tab' && filteredSuggestions.length > 0) {
      e.preventDefault();
      const selected = filteredSuggestions[selectedSuggestion];
      if (selected) {
        setInput(selected.command);
        setShowSuggestions(false);
      }
    } else if (e.key === 'ArrowUp') {
      if (showSuggestions && filteredSuggestions.length > 0) {
        e.preventDefault();
        setSelectedSuggestion(prev => Math.max(0, prev - 1));
      } else if (!input && commandHistory.length > 0) {
        // Navigate command history
        const lastCommand = commandHistory[commandHistory.length - 1];
        setInput(lastCommand);
      }
    } else if (e.key === 'ArrowDown' && showSuggestions) {
      e.preventDefault();
      setSelectedSuggestion(prev => Math.min(filteredSuggestions.length - 1, prev + 1));
    } else if (e.key === 'Enter') {
      if (showSuggestions && filteredSuggestions.length > 0) {
        const selected = filteredSuggestions[selectedSuggestion];
        if (selected) {
          setInput(selected.command);
          setShowSuggestions(false);
          return;
        }
      }
      handleSubmit();
    } else if (e.key === 'Escape') {
      setShowSuggestions(false);
    }
  };

  // Auto-scroll to bottom
  useEffect(() => {
    scrollRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Show suggestions when typing commands
  useEffect(() => {
    setShowSuggestions(input.startsWith('/') && input.length > 1);
    setSelectedSuggestion(0);
  }, [input]);

  return (
    <div className={cn("flex flex-col h-full", className)}>
      {/* Chat Messages */}
      <ScrollArea className="flex-1 p-4">
        <div className="space-y-4">
          {messages.map((message) => (
            <motion.div
              key={message.id}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              className={cn(
                "flex gap-3",
                message.type === 'user' && "justify-end"
              )}
            >
              {message.type !== 'user' && (
                <div className={cn(
                  "w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0",
                  message.type === 'system' ? "bg-primary/10" : "bg-muted"
                )}>
                  {message.type === 'system' ? (
                    <Sparkles className="h-4 w-4 text-primary" />
                  ) : (
                    <Bot className="h-4 w-4" />
                  )}
                </div>
              )}
              
              <div className={cn(
                "max-w-[80%] space-y-2",
                message.type === 'user' && "items-end"
              )}>
                <Card className={cn(
                  message.type === 'user' && "bg-primary text-primary-foreground"
                )}>
                  <CardContent className="p-3">
                    <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                    {message.status && (
                      <div className="flex items-center gap-2 mt-2">
                        {message.status === 'pending' && <Loader2 className="h-3 w-3 animate-spin" />}
                        {message.status === 'success' && <CheckCircle className="h-3 w-3 text-green-500" />}
                        {message.status === 'error' && <AlertCircle className="h-3 w-3 text-red-500" />}
                      </div>
                    )}
                  </CardContent>
                </Card>
                
                {message.suggestions && (
                  <div className="flex flex-wrap gap-2">
                    {message.suggestions.map((suggestion, idx) => (
                      <Button
                        key={idx}
                        variant="outline"
                        size="sm"
                        onClick={() => setInput(suggestion)}
                      >
                        {suggestion}
                      </Button>
                    ))}
                  </div>
                )}
                
                <div className="text-xs text-muted-foreground">
                  {new Date(message.timestamp).toLocaleTimeString()}
                </div>
              </div>
              
              {message.type === 'user' && (
                <div className="w-8 h-8 rounded-full bg-muted flex items-center justify-center flex-shrink-0">
                  <User className="h-4 w-4" />
                </div>
              )}
            </motion.div>
          ))}
          <div ref={scrollRef} />
        </div>
      </ScrollArea>

      {/* Input Area */}
      <div className="border-t p-4">
        {/* Command Suggestions */}
        {showSuggestions && filteredSuggestions.length > 0 && (
          <Card className="absolute bottom-20 left-4 right-4 max-h-64 overflow-hidden">
            <ScrollArea className="max-h-64">
              <div className="p-2">
                {filteredSuggestions.map((suggestion, idx) => {
                  const Icon = suggestion.icon;
                  return (
                    <div
                      key={idx}
                      className={cn(
                        "flex items-center gap-3 p-2 rounded cursor-pointer",
                        idx === selectedSuggestion && "bg-accent"
                      )}
                      onClick={() => {
                        setInput(suggestion.command);
                        setShowSuggestions(false);
                      }}
                    >
                      <Icon className="h-4 w-4 text-muted-foreground" />
                      <div className="flex-1">
                        <div className="text-sm font-medium">{suggestion.command}</div>
                        <div className="text-xs text-muted-foreground">{suggestion.description}</div>
                      </div>
                      <Badge variant="outline" className="text-xs">
                        {suggestion.category}
                      </Badge>
                    </div>
                  );
                })}
              </div>
            </ScrollArea>
          </Card>
        )}
        
        <div className="flex items-center gap-2">
          <div className="flex-1 relative">
            <Input
              ref={inputRef}
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="Type a command or describe what you want to do..."
              disabled={isProcessing}
              className="pr-10"
            />
            
            <div className="absolute right-2 top-1/2 -translate-y-1/2 flex items-center gap-1">
              {commandHistory.length > 0 && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-6 w-6"
                        onClick={() => setShowHistory(!showHistory)}
                      >
                        <History className="h-3 w-3" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>Command History</TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
              
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-6 w-6"
                      onClick={() => setInput('/')}
                    >
                      <Command className="h-3 w-3" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Show Commands</TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>
          
          <Button
            onClick={handleSubmit}
            disabled={!input.trim() || isProcessing}
            size="icon"
          >
            {isProcessing ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Send className="h-4 w-4" />
            )}
          </Button>
        </div>
        
        <div className="flex items-center justify-between mt-2">
          <div className="text-xs text-muted-foreground">
            {input.startsWith('/') ? "Command mode" : "Natural language mode"}
          </div>
          <Popover
            trigger={
              <Button variant="ghost" size="sm" className="h-6 text-xs">
                <HelpCircle className="h-3 w-3 mr-1" />
                Examples
              </Button>
            }
            content={
              <div className="w-96">
                <div className="space-y-2">
                  <h4 className="font-medium text-sm">Example Commands</h4>
                  {NATURAL_LANGUAGE_EXAMPLES.map((example, idx) => (
                    <div
                      key={idx}
                      className="text-sm p-2 rounded hover:bg-accent cursor-pointer"
                      onClick={() => setInput(example)}
                    >
                      {example}
                    </div>
                  ))}
                </div>
              </div>
            }
          />
        </div>
      </div>

      {/* Command History Popover */}
      {showHistory && commandHistory.length > 0 && (
        <Card className="absolute bottom-32 left-4 right-4 max-h-48">
          <CardContent className="p-2">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium">Command History</span>
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6"
                onClick={() => setShowHistory(false)}
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
            <ScrollArea className="max-h-32">
              {commandHistory.slice(-10).reverse().map((cmd, idx) => (
                <div
                  key={idx}
                  className="flex items-center gap-2 p-2 hover:bg-accent rounded cursor-pointer"
                  onClick={() => {
                    setInput(cmd);
                    setShowHistory(false);
                  }}
                >
                  <Clock className="h-3 w-3 text-muted-foreground" />
                  <span className="text-sm truncate">{cmd}</span>
                </div>
              ))}
            </ScrollArea>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
