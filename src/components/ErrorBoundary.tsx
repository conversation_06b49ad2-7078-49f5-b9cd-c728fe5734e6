import React, { Component, ReactNode } from "react";
import { motion, AnimatePresence } from 'framer-motion';
import { 
  AlertCircle, 
  AlertTriangle, 
  RefreshCw, 
  Bug, 
  Mail, 
  Copy, 
  ChevronDown, 
  ChevronUp
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";

export interface ErrorInfo {
  error: Error;
  errorInfo: React.ErrorInfo;
  timestamp: Date;
  userAgent: string;
  url: string;
  sessionId: string;
  componentStack?: string;
  errorId: string;
}

interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: (error: Error, reset: () => void) => ReactNode;
  onError?: (errorInfo: ErrorInfo) => void;
  enableRetry?: boolean;
  maxRetries?: number;
  resetOnPropsChange?: boolean;
  showErrorDetails?: boolean;
  context?: string;
  level?: 'widget' | 'page' | 'app';
}

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: React.ErrorInfo | null;
  errorId: string;
  showDetails: boolean;
  retryCount: number;
}

/**
 * Error Boundary component to catch and display React rendering errors
 */
export class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  private retryTimeoutId: number | null = null;

  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { 
      hasError: false, 
      error: null, 
      errorInfo: null,
      errorId: '',
      showDetails: false,
      retryCount: 0
    };
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    const errorId = `error-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    return { hasError: true, error, errorId, showDetails: false };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    const { onError, context = 'unknown' } = this.props;
    
    const errorDetails: ErrorInfo = {
      error,
      errorInfo,
      timestamp: new Date(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      sessionId: this.getSessionId(),
      componentStack: errorInfo.componentStack,
      errorId: this.state.errorId
    };

    // Update state with error info
    this.setState({ errorInfo });

    // Call custom error handler
    onError?.(errorDetails);

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.group(`🚨 Error Boundary: ${errorDetails.errorId}`);
      console.error('Error:', error);
      console.error('Component Stack:', errorInfo.componentStack);
      console.error('Error Info:', errorDetails);
      console.groupEnd();
    }

    // Attempt auto-recovery for non-critical errors
    if (this.props.level === 'widget' && this.state.retryCount < (this.props.maxRetries || 3)) {
      this.scheduleRetry();
    }
  }

  componentDidUpdate(prevProps: ErrorBoundaryProps): void {
    const { resetOnPropsChange, children } = this.props;
    const { hasError } = this.state;

    // Reset error state if props changed and resetOnPropsChange is enabled
    if (hasError && resetOnPropsChange && prevProps.children !== children) {
      this.reset();
    }
  }

  private getSessionId(): string {
    let sessionId = sessionStorage.getItem('claudia-session-id');
    if (!sessionId) {
      sessionId = `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      sessionStorage.setItem('claudia-session-id', sessionId);
    }
    return sessionId;
  }

  private scheduleRetry(): void {
    const retryDelay = Math.min(1000 * Math.pow(2, this.state.retryCount), 10000);
    
    this.retryTimeoutId = window.setTimeout(() => {
      this.setState(prevState => ({
        hasError: false,
        error: null,
        errorInfo: null,
        retryCount: prevState.retryCount + 1
      }));
    }, retryDelay);
  }

  reset = () => {
    if (this.retryTimeoutId) {
      clearTimeout(this.retryTimeoutId);
      this.retryTimeoutId = null;
    }

    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: '',
      showDetails: false,
      retryCount: 0
    });
  };

  private toggleDetails = (): void => {
    this.setState(prevState => ({
      showDetails: !prevState.showDetails
    }));
  };

  private copyErrorDetails = async (): Promise<void> => {
    const { error, errorInfo, errorId } = this.state;
    const errorDetails = {
      errorId,
      message: error?.message,
      stack: error?.stack,
      componentStack: errorInfo?.componentStack,
      timestamp: new Date().toISOString(),
      url: window.location.href,
      userAgent: navigator.userAgent
    };

    try {
      await navigator.clipboard.writeText(JSON.stringify(errorDetails, null, 2));
      console.log('Error details copied to clipboard');
    } catch (e) {
      console.warn('Failed to copy error details:', e);
    }
  };

  private reportIssue = (): void => {
    const { error, errorId } = this.state;
    const subject = encodeURIComponent(`Bug Report: ${error?.message || 'Unknown Error'}`);
    const body = encodeURIComponent(`
Error ID: ${errorId}
Error Message: ${error?.message || 'Unknown'}
URL: ${window.location.href}
Timestamp: ${new Date().toISOString()}

Please describe what you were doing when this error occurred:
[Your description here]
    `);
    
    window.open(`mailto:<EMAIL>?subject=${subject}&body=${body}`);
  };

  render() {
    const { hasError, error, showDetails, retryCount } = this.state;
    const { 
      children, 
      fallback, 
      enableRetry = true, 
      maxRetries = 3,
      showErrorDetails = true,
      context,
      level = 'widget'
    } = this.props;

    if (hasError && error) {
      // Use custom fallback if provided
      if (fallback) {
        return fallback(error, this.reset);
      }

      // Determine error severity based on level
      const isHighSeverity = level === 'app' || level === 'page';
      const canRetry = enableRetry && retryCount < maxRetries;

      return (
        <div className="flex items-center justify-center min-h-[200px] p-4">
          <Card className={cn(
            'max-w-md w-full',
            isHighSeverity ? 'border-destructive' : 'border-warning'
          )}>
            <CardContent className="p-6">
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.3 }}
                className="space-y-4"
              >
                {/* Error Icon */}
                <div className="flex items-start gap-4">
                  <div className={cn(
                    'w-10 h-10 rounded-full flex items-center justify-center flex-shrink-0',
                    isHighSeverity ? 'bg-destructive/10' : 'bg-warning/10'
                  )}>
                    {isHighSeverity ? (
                      <AlertTriangle className="h-5 w-5 text-destructive" />
                    ) : (
                      <AlertCircle className="h-5 w-5 text-warning" />
                    )}
                  </div>

                  <div className="flex-1 space-y-2">
                    <h3 className="font-semibold text-lg">
                      {isHighSeverity ? 'Application Error' : 'Something went wrong'}
                    </h3>
                    <p className="text-sm text-muted-foreground">
                      {level === 'widget' && context 
                        ? `The ${context} widget encountered an error`
                        : 'An unexpected error occurred. Please try again.'
                      }
                    </p>
                    
                    {retryCount > 0 && (
                      <Badge variant="outline" className="text-xs">
                        Attempt {retryCount + 1} of {maxRetries + 1}
                      </Badge>
                    )}
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex flex-wrap gap-2">
                  {canRetry && (
                    <Button onClick={this.reset} size="sm" className="gap-2">
                      <RefreshCw className="h-4 w-4" />
                      Try Again
                    </Button>
                  )}
                  
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={this.reportIssue}
                    className="gap-2"
                  >
                    <Mail className="h-4 w-4" />
                    Report Issue
                  </Button>

                  {showErrorDetails && (
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      onClick={this.toggleDetails}
                      className="gap-2"
                    >
                      <Bug className="h-4 w-4" />
                      {showDetails ? 'Hide' : 'Show'} Details
                      {showDetails ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                    </Button>
                  )}
                </div>

                {/* Error Details */}
                <AnimatePresence>
                  {showDetails && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      exit={{ opacity: 0, height: 0 }}
                      transition={{ duration: 0.3 }}
                      className="p-3 bg-muted rounded-lg text-left text-sm"
                    >
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium">Error Details</h4>
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          onClick={this.copyErrorDetails}
                          className="h-6 px-2 gap-1"
                        >
                          <Copy className="h-3 w-3" />
                          Copy
                        </Button>
                      </div>
                      
                      <div className="space-y-2 font-mono text-xs">
                        <div>
                          <strong>Error:</strong> {error.message}
                        </div>
                        <div>
                          <strong>ID:</strong> {this.state.errorId}
                        </div>
                        <div>
                          <strong>Time:</strong> {new Date().toLocaleString()}
                        </div>
                        {this.state.errorInfo?.componentStack && (
                          <details className="mt-2">
                            <summary className="cursor-pointer hover:text-foreground">
                              Component Stack
                            </summary>
                            <pre className="mt-1 whitespace-pre-wrap break-all text-xs text-muted-foreground">
                              {this.state.errorInfo.componentStack}
                            </pre>
                          </details>
                        )}
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </motion.div>
            </CardContent>
          </Card>
        </div>
      );
    }

    return children;
  }
}

// Higher-order component for easy error boundary wrapping
export const withErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<ErrorBoundaryProps, 'children'>
) => {
  return React.forwardRef<any, P>((props, ref) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <Component {...props} ref={ref} />
    </ErrorBoundary>
  ));
};

// Hook for error reporting
export const useErrorHandler = () => {
  const reportError = React.useCallback((error: Error, context?: string) => {
    const errorInfo: ErrorInfo = {
      error,
      errorInfo: { componentStack: '' },
      timestamp: new Date(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      sessionId: sessionStorage.getItem('claudia-session-id') || 'unknown',
      errorId: `manual-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    };

    console.error('Manual error report:', errorInfo);
  }, []);

  return { reportError };
}; 