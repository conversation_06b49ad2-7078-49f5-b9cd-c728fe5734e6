import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import type { 
  HubWidget, 
  HubState, 
  HubSettings
} from '@/types/hub';
import { 
  DEFAULT_HUB_SETTINGS,
  DEFAULT_WIDGET_SIZES,
  WIDGET_REFRESH_INTERVALS
} from '@/types/hub';
import { 
  fetchTrendingReposEnhanced
} from '@/lib/github-api';

interface HubStore extends HubState {
  // Settings
  settings: HubSettings;
  
  // Actions
  initializeWidgets: () => void;
  addWidget: (type: HubWidget['type']) => string;
  removeWidget: (id: string) => void;
  updateWidget: (id: string, updates: Partial<HubWidget>) => void;
  refreshWidget: (id: string) => Promise<void>;
  refreshAllWidgets: () => Promise<void>;
  toggleAutoRefresh: () => void;
  setRefreshInterval: (interval: number) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
  updateSettings: (updates: Partial<HubSettings>) => void;
  
  // Cache management
  getCachedData: (widgetId: string) => any;
  setCachedData: (widgetId: string, data: any, ttl?: number) => void;
  clearCache: (widgetId?: string) => void;
}

// Cache storage (in-memory for now, can be moved to localStorage later)
const widgetCache = new Map<string, { data: any; expiry: number }>();

const generateWidgetId = (): string => {
  return `widget-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
};

const createDefaultWidget = (type: HubWidget['type']): HubWidget => {
  const id = generateWidgetId();
  const defaultSize = DEFAULT_WIDGET_SIZES[type];
  const refreshInterval = WIDGET_REFRESH_INTERVALS[type];
  
  const baseWidget: Omit<HubWidget, 'title' | 'settings'> = {
    id,
    type,
    position: { x: 0, y: 0, w: defaultSize.minW, h: defaultSize.minH },
    size: defaultSize,
    isEnabled: true,
    refreshInterval,
  };

  switch (type) {
    case 'reddit':
      return {
        ...baseWidget,
        title: 'Reddit Feed',
        settings: {
          subreddits: ['programming', 'MachineLearning', 'artificial'],
          postLimit: 10,
          sortBy: 'hot',
        },
      };
    case 'github-trending':
      return {
        ...baseWidget,
        title: 'GitHub Trending',
        settings: {
          timeRange: 'weekly',
          language: 'all',
          repoLimit: 10,
          includeStarsToday: true,
          showDevelopers: true,
          showTopics: true,
          defaultView: 'repositories',
          viewMode: 'compact',
        },
      };
    case 'coding-progress':
      return {
        ...baseWidget,
        title: 'Coding Progress',
        settings: {
          timeRange: 'week',
          showGoals: true,
          showLanguageBreakdown: true,
          showActivityChart: true,
        },
      };
    case 'ai-news':
      return {
        ...baseWidget,
        title: 'AI News',
        settings: {
          sources: ['hackernews', 'arxiv'],
          categories: ['ai', 'ml', 'nlp'],
          itemLimit: 10,
          keywords: ['ai', 'machine learning', 'llm', 'claude'],
          timeRange: 'week',
        },
      };
    case 'learning':
      return {
        ...baseWidget,
        title: 'Learning Resources',
        settings: {
          resourceTypes: ['course', 'tutorial', 'paper'],
          difficulty: ['intermediate', 'advanced'],
          providers: ['coursera', 'edx', 'arxiv'],
          topics: ['machine learning', 'ai', 'programming'],
          showProgress: true,
          sortBy: 'relevance',
        },
      };
    default:
      throw new Error(`Unknown widget type: ${type}`);
  }
};

// Data fetchers for widgets - Reddit is handled by useRedditData hook
const fetchWidgetData = async (widget: HubWidget): Promise<any> => {
  // Simulate API delay for non-Reddit widgets
  await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1000));
  
  switch (widget.type) {
    case 'reddit':
      // Reddit data is handled by the useRedditData hook in the component
      // Return minimal data for store compatibility
      return {
        initialized: true,
        timestamp: new Date().toISOString()
      };
    case 'github-trending':
      try {
        const settings = widget.settings as {
          timeRange?: 'daily' | 'weekly' | 'monthly';
          language?: string;
          repoLimit?: number;
        };
        
        // Fetch real GitHub data
        const repos = await fetchTrendingReposEnhanced(
          settings.timeRange || 'weekly',
          settings.language === 'all' ? undefined : settings.language
        );
        
        return {
          repositories: repos.slice(0, settings.repoLimit || 10),
          lastFetched: new Date().toISOString(),
          totalCount: repos.length
        };
      } catch (error) {
        console.error('Failed to fetch GitHub trending data:', error);
        // Fallback to mock data
        return {
          repositories: [
            { id: 1, name: 'sample-repo', stars: 1234, language: 'TypeScript' },
            { id: 2, name: 'another-repo', stars: 567, language: 'Python' },
          ],
          error: 'Failed to fetch live data'
        };
      }
    case 'coding-progress':
      // Coding progress data is handled by the useCodingProgress hook in the component
      // Return minimal data for store compatibility
      return {
        initialized: true,
        timestamp: new Date().toISOString()
      };
    case 'ai-news':
      // AI news data is handled by the useAINews hook in the component
      // Return minimal data for store compatibility
      return {
        initialized: true,
        timestamp: new Date().toISOString()
      };
    case 'learning':
      return {
        resources: [
          { id: '1', title: 'Machine Learning Course', provider: 'Coursera' },
          { id: '2', title: 'Deep Learning Tutorial', provider: 'edX' },
        ]
      };
    default:
      return {};
  }
};

export const useHubStore = create<HubStore>()(
  subscribeWithSelector((set, get) => ({
    // Initial state
    widgets: [],
    layout: [],
    selectedWidgetId: null,
    isEditing: false,
    isLoading: false,
    error: null,
    lastRefresh: null,
    autoRefresh: DEFAULT_HUB_SETTINGS.autoRefresh,
    refreshInterval: DEFAULT_HUB_SETTINGS.refreshInterval,
    settings: DEFAULT_HUB_SETTINGS,

    // Actions
    initializeWidgets: () => {
      // For now, start with empty widgets array
      // In a real app, this would load from localStorage or API
      const savedWidgets = localStorage.getItem('claudia-hub-widgets');
      if (savedWidgets) {
        try {
          const parsed = JSON.parse(savedWidgets);
          set({ widgets: parsed });
        } catch (error) {
          console.error('Failed to parse saved widgets:', error);
        }
      }
      
      // Load settings
      const savedSettings = localStorage.getItem('claudia-hub-settings');
      if (savedSettings) {
        try {
          const parsed = JSON.parse(savedSettings);
          set({ 
            settings: { ...DEFAULT_HUB_SETTINGS, ...parsed },
            autoRefresh: parsed.autoRefresh ?? DEFAULT_HUB_SETTINGS.autoRefresh,
            refreshInterval: parsed.refreshInterval ?? DEFAULT_HUB_SETTINGS.refreshInterval,
          });
        } catch (error) {
          console.error('Failed to parse saved settings:', error);
        }
      }
    },

    addWidget: (type: HubWidget['type']) => {
      const newWidget = createDefaultWidget(type);
      const currentWidgets = get().widgets;
      
      // Position the widget in the grid
      // Simple positioning logic - will be enhanced in Phase 4
      const existingWidgets = currentWidgets.length;
      const cols = 4; // Based on xl:grid-cols-4
      const row = Math.floor(existingWidgets / cols);
      const col = existingWidgets % cols;
      
      newWidget.position = {
        x: col,
        y: row,
        w: newWidget.size.minW,
        h: newWidget.size.minH,
      };
      
      const updatedWidgets = [...currentWidgets, newWidget];
      set({ widgets: updatedWidgets });
      
      // Save to localStorage
      localStorage.setItem('claudia-hub-widgets', JSON.stringify(updatedWidgets));
      
      return newWidget.id;
    },

    removeWidget: (id: string) => {
      const updatedWidgets = get().widgets.filter(w => w.id !== id);
      set({ widgets: updatedWidgets });
      
      // Clear cache for this widget
      widgetCache.delete(id);
      
      // Save to localStorage
      localStorage.setItem('claudia-hub-widgets', JSON.stringify(updatedWidgets));
    },

    updateWidget: (id: string, updates: Partial<HubWidget>) => {
      const updatedWidgets = get().widgets.map(w => 
        w.id === id ? { ...w, ...updates } : w
      );
      set({ widgets: updatedWidgets });
      
      // Save to localStorage
      localStorage.setItem('claudia-hub-widgets', JSON.stringify(updatedWidgets));
    },

    refreshWidget: async (id: string) => {
      const { widgets } = get();
      const widget = widgets.find(w => w.id === id);
      if (!widget) return;

      try {
        set({ isLoading: true, error: null });
        
        const data = await fetchWidgetData(widget);
        
        // Cache the data
        get().setCachedData(id, data);
        
        // Update last updated time
        get().updateWidget(id, { lastUpdated: new Date() });
        
        set({ isLoading: false, lastRefresh: new Date() });
      } catch (error) {
        console.error(`Failed to refresh widget ${id}:`, error);
        set({ 
          isLoading: false, 
          error: `Failed to refresh ${widget.title}` 
        });
      }
    },

    refreshAllWidgets: async () => {
      const { widgets } = get();
      if (widgets.length === 0) return;

      try {
        set({ isLoading: true, error: null });
        
        // Refresh all enabled widgets in parallel
        const enabledWidgets = widgets.filter(w => w.isEnabled);
        await Promise.allSettled(
          enabledWidgets.map(widget => get().refreshWidget(widget.id))
        );
        
        set({ isLoading: false, lastRefresh: new Date() });
      } catch (error) {
        console.error('Failed to refresh widgets:', error);
        set({ 
          isLoading: false, 
          error: 'Failed to refresh widgets' 
        });
      }
    },

    toggleAutoRefresh: () => {
      const newAutoRefresh = !get().autoRefresh;
      set({ autoRefresh: newAutoRefresh });
      
      // Update settings
      const updatedSettings = { ...get().settings, autoRefresh: newAutoRefresh };
      set({ settings: updatedSettings });
      localStorage.setItem('claudia-hub-settings', JSON.stringify(updatedSettings));
    },

    setRefreshInterval: (interval: number) => {
      set({ refreshInterval: interval });
      
      // Update settings
      const updatedSettings = { ...get().settings, refreshInterval: interval };
      set({ settings: updatedSettings });
      localStorage.setItem('claudia-hub-settings', JSON.stringify(updatedSettings));
    },

    setLoading: (loading: boolean) => set({ isLoading: loading }),
    
    setError: (error: string | null) => set({ error }),
    
    clearError: () => set({ error: null }),

    updateSettings: (updates: Partial<HubSettings>) => {
      const updatedSettings = { ...get().settings, ...updates };
      set({ settings: updatedSettings });
      localStorage.setItem('claudia-hub-settings', JSON.stringify(updatedSettings));
    },

    // Cache management
    getCachedData: (widgetId: string) => {
      const cached = widgetCache.get(widgetId);
      if (!cached) return null;
      
      if (Date.now() > cached.expiry) {
        widgetCache.delete(widgetId);
        return null;
      }
      
      return cached.data;
    },

    setCachedData: (widgetId: string, data: any, ttl: number = 30 * 60 * 1000) => {
      widgetCache.set(widgetId, {
        data,
        expiry: Date.now() + ttl,
      });
    },

    clearCache: (widgetId?: string) => {
      if (widgetId) {
        widgetCache.delete(widgetId);
      } else {
        widgetCache.clear();
      }
    },
  }))
);