# Marketplace Component Library Documentation

## Overview

This documentation covers the complete React component library for marketplace functionality in Claudia. The marketplace allows users to discover, install, and manage community-created agents.

## Architecture

### Core Components

1. **MarketplaceBrowser** - Main marketplace interface
2. **AgentMarketplaceCard** - Individual agent display component
3. **AgentDetailsModal** - Detailed agent information modal
4. **InstallButton** - Agent installation management
5. **CategoryFilter** - Category navigation sidebar
6. **MarketplaceStats** - Analytics dashboard
7. **AgentCollections** - Curated agent collections
8. **RatingSystem** - Rating and review system

### State Management

The marketplace uses Zustand for state management with the `marketplaceStore` containing:
- Agent data and metadata
- Search and filtering state
- Installation status tracking
- Modal and UI state
- Caching and performance optimizations

## Components

### MarketplaceBrowser

**Location:** `src/components/marketplace/MarketplaceBrowser.tsx`

The main marketplace interface component.

**Props:**
```typescript
interface MarketplaceBrowserProps {
  onBack: () => void;
  className?: string;
}
```

**Features:**
- Search and filtering
- Grid/list view toggle
- Category and collection filtering
- Pagination
- Real-time agent data
- Responsive design

**Usage:**
```jsx
<MarketplaceBrowser 
  onBack={() => setView('home')} 
  className="custom-class"
/>
```

### AgentMarketplaceCard

**Location:** `src/components/marketplace/AgentMarketplaceCard.tsx`

Displays individual agents in both grid and list formats.

**Props:**
```typescript
interface AgentMarketplaceCardProps {
  agent: MarketplaceAgent;
  viewMode: 'grid' | 'list';
  index: number;
}
```

**Features:**
- Responsive card layout
- Agent metadata display
- Installation status indicators
- Rating and download counts
- Click-to-view details

### AgentDetailsModal

**Location:** `src/components/marketplace/AgentDetailsModal.tsx`

Modal for detailed agent information.

**Props:**
```typescript
interface AgentDetailsModalProps {
  isOpen: boolean;
  agent: MarketplaceAgent | null;
  onClose: () => void;
}
```

**Features:**
- Tabbed interface (Overview, Reviews, Dependencies, Changelog)
- Full agent description and screenshots
- Dependency management
- Version history
- Review system integration

### InstallButton

**Location:** `src/components/marketplace/InstallButton.tsx`

Smart installation button with state management.

**Props:**
```typescript
interface InstallButtonProps {
  agent: MarketplaceAgent;
  onClick?: (e: React.MouseEvent) => void;
  className?: string;
  showDropdown?: boolean;
}
```

**Features:**
- Installation state tracking
- Update notifications
- Uninstall functionality
- Loading states
- Error handling

### CategoryFilter

**Location:** `src/components/marketplace/CategoryFilter.tsx`

Sidebar component for category navigation.

**Features:**
- Category list with agent counts
- Active category highlighting
- Smooth animations
- Category icons

### MarketplaceStats

**Location:** `src/components/marketplace/MarketplaceStats.tsx`

Dashboard component showing marketplace metrics.

**Features:**
- Total agents, downloads, ratings
- Verified and featured agent counts
- Installation statistics
- Visual metric cards

### AgentCollections

**Location:** `src/components/marketplace/AgentCollections.tsx`

Component for curated agent collections.

**Features:**
- Official and community collections
- Collection previews
- Curator information
- Collection filtering

### RatingSystem

**Location:** `src/components/marketplace/RatingSystem.tsx`

Complete rating and review system.

**Props:**
```typescript
interface RatingSystemProps {
  agent: MarketplaceAgent;
  reviews: MarketplaceReview[];
}
```

**Features:**
- Interactive star ratings
- Review submission
- Review display and management
- Rating statistics

## Data Types

### MarketplaceAgent

```typescript
interface MarketplaceAgent {
  id: string;
  name: string;
  description: string;
  longDescription?: string;
  version: string;
  author: string;
  category: string;
  tags: string[];
  icon: string;
  downloadCount: number;
  rating: number;
  reviewCount: number;
  lastUpdated: string;
  size: number;
  dependencies: string[];
  screenshots?: string[];
  documentation?: string;
  sourceUrl?: string;
  licenseType: string;
  isVerified: boolean;
  isFeatured: boolean;
  installUrl: string;
  isInstalled?: boolean;
  installedVersion?: string;
}
```

### MarketplaceReview

```typescript
interface MarketplaceReview {
  id: string;
  agentId: string;
  userId: string;
  userName: string;
  rating: number;
  review: string;
  createdAt: string;
  isVerified: boolean;
}
```

### MarketplaceCategory

```typescript
interface MarketplaceCategory {
  id: string;
  name: string;
  description: string;
  icon: string;
  agentCount: number;
}
```

## Store Usage

### Marketplace Store

The marketplace store provides all necessary state management:

```typescript
import { useMarketplaceStore } from '@/stores/marketplaceStore';

// In your component
const {
  agents,
  categories,
  searchQuery,
  setSearchQuery,
  fetchAgents,
  installAgent,
  openDetailsModal
} = useMarketplaceStore();
```

### Key Store Actions

- `fetchAgents()` - Load agents from API
- `installAgent(agentId)` - Install an agent
- `uninstallAgent(agentId)` - Uninstall an agent
- `setSearchQuery(query)` - Update search
- `setSelectedCategory(categoryId)` - Filter by category
- `openDetailsModal(agent)` - Show agent details

## Integration

### Tab System Integration

The marketplace is integrated into the tab system:

1. **Tab Type**: Added `'marketplace'` to Tab interface
2. **Tab Creation**: `createMarketplaceTab()` function in useTabState
3. **Tab Content**: MarketplaceBrowser rendered in TabContent
4. **Navigation**: Marketplace button in Topbar

### Navigation Integration

Added to Topbar component:

```jsx
<Button onClick={() => createMarketplaceTab()}>
  <ShoppingBag className="mr-2 h-3 w-3" />
  Marketplace
</Button>
```

## Styling and Theming

### Design System Compliance

- Uses established shadcn/ui components
- Follows existing color schemes and spacing
- Responsive design patterns
- Consistent animation patterns with Framer Motion

### Animation Patterns

- Staggered entrance animations
- Smooth transitions between views
- Loading state animations
- Modal and dropdown animations

## Performance Optimizations

### Caching Strategy

- 5-minute cache for agent data
- Local storage for filter preferences
- Debounced search functionality
- Lazy loading of heavy components

### Virtual Scrolling

Ready for implementation when needed:
- Large agent lists
- Infinite scrolling support
- Memory optimization

## Error Handling

### Error States

- Network failure handling
- Installation error recovery
- Search error feedback
- Graceful degradation

### User Feedback

- Toast notifications for actions
- Loading spinners
- Error messages with retry options
- Success confirmations

## Accessibility

### Features Implemented

- Keyboard navigation support
- ARIA labels and roles
- Focus management
- Screen reader compatibility
- High contrast support

## Testing Strategy

### Component Testing

Each component should be tested for:
- Rendering with different props
- User interactions
- State changes
- Error conditions

### Integration Testing

- Store interactions
- Navigation flows
- Installation processes
- Search and filtering

## Future Enhancements

### Planned Features

1. **Advanced Search**
   - Semantic search capabilities
   - Saved searches
   - Search history

2. **Social Features**
   - Agent sharing
   - User profiles
   - Community ratings

3. **Enterprise Features**
   - Private repositories
   - Organization management
   - Bulk operations

### Performance Improvements

1. **Virtual Scrolling** for large lists
2. **Service Worker** for offline functionality
3. **Image Optimization** for agent screenshots
4. **Bundle Splitting** for better loading

## Development Guidelines

### Adding New Components

1. Follow existing patterns in `src/components/marketplace/`
2. Use TypeScript interfaces for props
3. Implement proper error boundaries
4. Add comprehensive JSDoc documentation
5. Follow animation patterns with Framer Motion

### State Management

1. Add new state to marketplaceStore
2. Use selectors for derived state
3. Implement proper caching strategies
4. Handle loading and error states

### API Integration

1. Mock data is currently used
2. Replace with actual API endpoints
3. Implement proper error handling
4. Add request retries and timeouts

## Conclusion

The marketplace component library provides a comprehensive, production-ready solution for agent discovery and management. It follows established patterns from the existing Claudia codebase while providing modern UX patterns and performance optimizations.

All components are fully integrated with the existing tab system and design patterns, making them ready for immediate use in the application.