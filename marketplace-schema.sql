-- Marketplace Database Schema Extensions for Claudia
-- Follows existing SQLite patterns and naming conventions

-- 1. Marketplace Agents table - extends existing agents functionality
CREATE TABLE IF NOT EXISTS marketplace_agents (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    remote_id TEXT NOT NULL UNIQUE, -- Unique identifier from marketplace/GitHub
    name TEXT NOT NULL,
    icon TEXT NOT NULL,
    system_prompt TEXT NOT NULL,
    default_task TEXT,
    model TEXT NOT NULL DEFAULT 'sonnet',
    enable_file_read BOOLEAN NOT NULL DEFAULT 1,
    enable_file_write BOOLEAN NOT NULL DEFAULT 1,
    enable_network BOOLEAN NOT NULL DEFAULT 0,
    hooks TEXT, -- JSON string of hooks configuration
    
    -- Marketplace specific fields
    author TEXT NOT NULL,
    author_url TEXT,
    description TEXT NOT NULL,
    long_description TEXT,
    version TEXT NOT NULL DEFAULT '1.0.0',
    license TEXT DEFAULT 'MIT',
    tags TEXT, -- JSON array of tags
    category_id INTEGER,
    
    -- Statistics
    download_count INTEGER DEFAULT 0,
    rating_average REAL DEFAULT 0.0,
    rating_count INTEGER DEFAULT 0,
    
    -- URLs and metadata
    source_url TEXT, -- GitHub URL or marketplace URL
    download_url TEXT NOT NULL,
    documentation_url TEXT,
    homepage_url TEXT,
    sha TEXT, -- File hash for integrity verification
    file_size INTEGER DEFAULT 0,
    
    -- Installation tracking
    is_installed BOOLEAN DEFAULT 0,
    installed_at TEXT, -- ISO datetime
    installation_source TEXT, -- 'github', 'marketplace', 'manual'
    
    -- Timestamps
    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    published_at TEXT, -- When published to marketplace
    last_synced_at TEXT, -- Last time synced from remote
    
    -- Constraints
    FOREIGN KEY (category_id) REFERENCES agent_categories(id) ON DELETE SET NULL,
    
    -- Indexes for performance
    UNIQUE(remote_id),
    INDEX idx_marketplace_agents_category (category_id),
    INDEX idx_marketplace_agents_author (author),
    INDEX idx_marketplace_agents_rating (rating_average DESC, rating_count DESC),
    INDEX idx_marketplace_agents_downloads (download_count DESC),
    INDEX idx_marketplace_agents_updated (updated_at DESC),
    INDEX idx_marketplace_agents_installed (is_installed, installed_at DESC)
);

-- 2. Agent Categories for classification
CREATE TABLE IF NOT EXISTS agent_categories (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE,
    slug TEXT NOT NULL UNIQUE,
    description TEXT,
    icon TEXT, -- Icon identifier for UI
    parent_id INTEGER, -- For hierarchical categories
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT 1,
    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (parent_id) REFERENCES agent_categories(id) ON DELETE CASCADE,
    INDEX idx_categories_parent (parent_id),
    INDEX idx_categories_sort (sort_order, name)
);

-- 3. Agent Ratings and Reviews
CREATE TABLE IF NOT EXISTS agent_ratings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    marketplace_agent_id INTEGER NOT NULL,
    user_identifier TEXT, -- Anonymous identifier (hashed)
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    review_title TEXT,
    review_content TEXT,
    is_verified BOOLEAN DEFAULT 0, -- For verified downloads
    
    -- Helpful votes
    helpful_votes INTEGER DEFAULT 0,
    total_votes INTEGER DEFAULT 0,
    
    -- Timestamps
    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (marketplace_agent_id) REFERENCES marketplace_agents(id) ON DELETE CASCADE,
    
    -- One rating per user per agent
    UNIQUE(marketplace_agent_id, user_identifier),
    INDEX idx_ratings_agent (marketplace_agent_id, rating DESC),
    INDEX idx_ratings_helpful (helpful_votes DESC)
);

-- 4. User Preferences for marketplace settings
CREATE TABLE IF NOT EXISTS user_preferences (
    key TEXT PRIMARY KEY,
    value TEXT NOT NULL,
    value_type TEXT DEFAULT 'string', -- 'string', 'number', 'boolean', 'json'
    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 5. Marketplace Cache for offline capability
CREATE TABLE IF NOT EXISTS marketplace_cache (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    cache_key TEXT NOT NULL UNIQUE,
    cache_type TEXT NOT NULL, -- 'agent_list', 'agent_detail', 'category_list'
    data TEXT NOT NULL, -- JSON data
    expires_at TEXT NOT NULL,
    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_cache_key (cache_key),
    INDEX idx_cache_type_expires (cache_type, expires_at)
);

-- 6. Agent Downloads tracking
CREATE TABLE IF NOT EXISTS agent_downloads (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    marketplace_agent_id INTEGER NOT NULL,
    user_identifier TEXT, -- Anonymous identifier
    download_source TEXT, -- 'marketplace', 'github'
    download_url TEXT,
    success BOOLEAN DEFAULT 1,
    error_message TEXT,
    downloaded_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (marketplace_agent_id) REFERENCES marketplace_agents(id) ON DELETE CASCADE,
    INDEX idx_downloads_agent_date (marketplace_agent_id, downloaded_at DESC),
    INDEX idx_downloads_success (success, downloaded_at DESC)
);

-- 7. Agent Dependencies (for agents that depend on other agents or tools)
CREATE TABLE IF NOT EXISTS agent_dependencies (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    marketplace_agent_id INTEGER NOT NULL,
    dependency_type TEXT NOT NULL, -- 'agent', 'tool', 'binary', 'npm_package'
    dependency_name TEXT NOT NULL,
    dependency_version TEXT, -- Semver constraint like "^1.0.0"
    is_required BOOLEAN DEFAULT 1,
    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (marketplace_agent_id) REFERENCES marketplace_agents(id) ON DELETE CASCADE,
    UNIQUE(marketplace_agent_id, dependency_type, dependency_name),
    INDEX idx_dependencies_agent (marketplace_agent_id)
);

-- 8. Agent Collections (curated lists)
CREATE TABLE IF NOT EXISTS agent_collections (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    description TEXT,
    curator TEXT, -- Who created this collection
    is_featured BOOLEAN DEFAULT 0,
    is_public BOOLEAN DEFAULT 1,
    sort_order INTEGER DEFAULT 0,
    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_collections_featured (is_featured, sort_order),
    INDEX idx_collections_public (is_public, updated_at DESC)
);

-- 9. Agent Collection Items (many-to-many relationship)
CREATE TABLE IF NOT EXISTS agent_collection_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    collection_id INTEGER NOT NULL,
    marketplace_agent_id INTEGER NOT NULL,
    sort_order INTEGER DEFAULT 0,
    added_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (collection_id) REFERENCES agent_collections(id) ON DELETE CASCADE,
    FOREIGN KEY (marketplace_agent_id) REFERENCES marketplace_agents(id) ON DELETE CASCADE,
    UNIQUE(collection_id, marketplace_agent_id),
    INDEX idx_collection_items_collection (collection_id, sort_order),
    INDEX idx_collection_items_agent (marketplace_agent_id)
);

-- Triggers for maintaining data integrity and updating timestamps

-- Update marketplace_agents.updated_at trigger
CREATE TRIGGER IF NOT EXISTS update_marketplace_agent_timestamp 
AFTER UPDATE ON marketplace_agents 
FOR EACH ROW
BEGIN
    UPDATE marketplace_agents SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- Update agent_ratings.updated_at trigger
CREATE TRIGGER IF NOT EXISTS update_agent_rating_timestamp 
AFTER UPDATE ON agent_ratings 
FOR EACH ROW
BEGIN
    UPDATE agent_ratings SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- Update user_preferences.updated_at trigger
CREATE TRIGGER IF NOT EXISTS update_user_preferences_timestamp 
AFTER UPDATE ON user_preferences 
FOR EACH ROW
BEGIN
    UPDATE user_preferences SET updated_at = CURRENT_TIMESTAMP WHERE key = NEW.key;
END;

-- Update agent_collections.updated_at trigger
CREATE TRIGGER IF NOT EXISTS update_agent_collection_timestamp 
AFTER UPDATE ON agent_collections 
FOR EACH ROW
BEGIN
    UPDATE agent_collections SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- Trigger to update rating statistics when ratings change
CREATE TRIGGER IF NOT EXISTS update_agent_rating_stats_insert
AFTER INSERT ON agent_ratings
FOR EACH ROW
BEGIN
    UPDATE marketplace_agents 
    SET rating_average = (
        SELECT AVG(CAST(rating AS REAL)) 
        FROM agent_ratings 
        WHERE marketplace_agent_id = NEW.marketplace_agent_id
    ),
    rating_count = (
        SELECT COUNT(*) 
        FROM agent_ratings 
        WHERE marketplace_agent_id = NEW.marketplace_agent_id
    )
    WHERE id = NEW.marketplace_agent_id;
END;

CREATE TRIGGER IF NOT EXISTS update_agent_rating_stats_update
AFTER UPDATE ON agent_ratings
FOR EACH ROW
BEGIN
    UPDATE marketplace_agents 
    SET rating_average = (
        SELECT AVG(CAST(rating AS REAL)) 
        FROM agent_ratings 
        WHERE marketplace_agent_id = NEW.marketplace_agent_id
    ),
    rating_count = (
        SELECT COUNT(*) 
        FROM agent_ratings 
        WHERE marketplace_agent_id = NEW.marketplace_agent_id
    )
    WHERE id = NEW.marketplace_agent_id;
END;

CREATE TRIGGER IF NOT EXISTS update_agent_rating_stats_delete
AFTER DELETE ON agent_ratings
FOR EACH ROW
BEGIN
    UPDATE marketplace_agents 
    SET rating_average = (
        SELECT COALESCE(AVG(CAST(rating AS REAL)), 0.0) 
        FROM agent_ratings 
        WHERE marketplace_agent_id = OLD.marketplace_agent_id
    ),
    rating_count = (
        SELECT COUNT(*) 
        FROM agent_ratings 
        WHERE marketplace_agent_id = OLD.marketplace_agent_id
    )
    WHERE id = OLD.marketplace_agent_id;
END;

-- Insert default categories
INSERT OR IGNORE INTO agent_categories (name, slug, description, icon, sort_order) VALUES
('Development Tools', 'development-tools', 'Agents for code development, testing, and debugging', '🔧', 1),
('Code Review', 'code-review', 'Agents specialized in code analysis and review', '🔍', 2),
('Documentation', 'documentation', 'Agents for generating and maintaining documentation', '📚', 3),
('Testing', 'testing', 'Agents for automated testing and quality assurance', '🧪', 4),
('Deployment', 'deployment', 'Agents for CI/CD and deployment automation', '🚀', 5),
('Data Analysis', 'data-analysis', 'Agents for data processing and analysis', '📊', 6),
('Security', 'security', 'Agents for security scanning and vulnerability assessment', '🔒', 7),
('Performance', 'performance', 'Agents for performance optimization and monitoring', '⚡', 8),
('Content Creation', 'content-creation', 'Agents for generating content and creative work', '✍️', 9),
('Learning & Education', 'learning-education', 'Agents for educational content and tutorials', '🎓', 10),
('Utilities', 'utilities', 'General-purpose utility agents', '🛠️', 11),
('Experimental', 'experimental', 'Cutting-edge and experimental agents', '🔬', 12);

-- Insert default user preferences
INSERT OR IGNORE INTO user_preferences (key, value, value_type) VALUES
('marketplace_enabled', 'true', 'boolean'),
('auto_update_agents', 'false', 'boolean'),
('download_confirmations', 'true', 'boolean'),
('preferred_categories', '[]', 'json'),
('cache_expiry_hours', '24', 'number'),
('show_experimental', 'false', 'boolean'),
('default_sort_order', 'downloads', 'string'), -- 'downloads', 'rating', 'updated', 'alphabetical'
('anonymous_analytics', 'true', 'boolean');