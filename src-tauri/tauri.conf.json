{"$schema": "https://schema.tauri.app/config/2", "productName": "<PERSON>", "version": "0.1.0", "identifier": "claudia.asterisk.so", "build": {"beforeDevCommand": "bun run dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "bun run build", "frontendDist": "../dist"}, "app": {"windows": [{"title": "<PERSON>", "width": 800, "height": 600}], "security": {"csp": "default-src 'self'; img-src 'self' asset: https://asset.localhost blob: data:; style-src 'self' 'unsafe-inline'; script-src 'self' 'unsafe-eval'; connect-src 'self' ipc: https://ipc.localhost", "assetProtocol": {"enable": true, "scope": ["**"]}}}, "plugins": {"fs": {"scope": ["$HOME/**"], "allow": ["readFile", "writeFile", "readDir", "copyFile", "createDir", "removeDir", "removeFile", "renameFile", "exists"]}, "shell": {"open": true}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.png"], "externalBin": ["binaries/claude-code"]}}