use serde::{Deserialize, Serialize};
use tauri::State;
use rusqlite::{params, Connection};
use std::collections::HashMap;
use crate::commands::agents::AgentDb;

#[derive(Debug, Serialize, Deserialize)]
pub struct UserInteraction {
    pub session_id: Option<String>,
    pub user_session_hash: String,
    pub project_id: Option<String>,
    pub interaction_type: String,
    pub feature_id: String,
    pub feature_category: String,
    pub context_data: Option<String>, // JSON string
    pub success_indicator: Option<bool>,
    pub duration_seconds: Option<f64>,
    pub token_usage: Option<i32>,
    pub user_rating: Option<i32>,
    pub timestamp: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct BehaviorPattern {
    pub pattern_type: String,
    pub pattern_data: String, // JSON string
    pub frequency_score: f64,
    pub success_score: f64,
    pub efficiency_score: f64,
    pub confidence_level: f64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Recommendation {
    pub id: Option<i64>,
    pub user_profile_hash: String,
    pub recommendation_type: String,
    pub item_id: String,
    pub item_category: Option<String>,
    pub relevance_score: f64,
    pub confidence_score: f64,
    pub priority_score: f64,
    pub reasoning_data: Option<String>, // JSON string
    pub context_triggers: Option<String>, // JSON string
    pub expected_benefit: Option<String>, // JSON string
    pub algorithm_version: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct FeatureUsageStats {
    pub feature_id: String,
    pub feature_type: String,
    pub total_uses: i32,
    pub unique_users: i32,
    pub success_rate: f64,
    pub average_duration: f64,
    pub average_rating: f64,
}

/// Bulk insert user interactions for efficient batch processing
#[tauri::command]
pub async fn bulk_insert_user_interactions(
    db: State<'_, AgentDb>,
    interactions: Vec<UserInteraction>,
) -> Result<(), String> {
    let conn = db.0.lock().map_err(|e| e.to_string())?;
    
    let mut stmt = conn.prepare(
        "INSERT INTO user_interactions (
            session_id, user_session_hash, project_id, interaction_type,
            feature_id, feature_category, context_data, success_indicator,
            duration_seconds, token_usage, user_rating, timestamp
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)"
    ).map_err(|e| format!("Failed to prepare statement: {}", e))?;
    
    for interaction in interactions {
        stmt.execute(params![
            interaction.session_id,
            interaction.user_session_hash,
            interaction.project_id,
            interaction.interaction_type,
            interaction.feature_id,
            interaction.feature_category,
            interaction.context_data,
            interaction.success_indicator,
            interaction.duration_seconds,
            interaction.token_usage,
            interaction.user_rating,
            interaction.timestamp.unwrap_or_else(|| chrono::Utc::now().format("%Y-%m-%d %H:%M:%S%.3f").to_string())
        ]).map_err(|e| format!("Failed to insert interaction: {}", e))?;
    }
    
    Ok(())
}

/// Generate user profile hash based on behavior patterns
#[tauri::command]
pub async fn generate_user_profile(
    db: State<'_, AgentDb>,
    user_session_hash: String,
    lookback_days: i32,
) -> Result<String, String> {
    let conn = db.0.lock().map_err(|e| e.to_string())?;
    
    // Get recent interaction patterns
    let mut stmt = conn.prepare(
        "SELECT interaction_type, feature_category, COUNT(*) as frequency,
                AVG(CASE WHEN success_indicator THEN 1.0 ELSE 0.0 END) as success_rate,
                AVG(duration_seconds) as avg_duration
         FROM user_interactions 
         WHERE user_session_hash = ? 
           AND datetime(timestamp) > datetime('now', '-' || ? || ' days')
         GROUP BY interaction_type, feature_category
         ORDER BY frequency DESC"
    ).map_err(|e| format!("Failed to prepare profile query: {}", e))?;
    
    let mut profile_data = HashMap::new();
    let rows = stmt.query_map(params![user_session_hash, lookback_days], |row| {
        Ok((
            row.get::<_, String>(0)?,
            row.get::<_, String>(1)?,
            row.get::<_, i32>(2)?,
            row.get::<_, f64>(3)?,
            row.get::<_, Option<f64>>(4)?
        ))
    }).map_err(|e| format!("Failed to query profile data: {}", e))?;
    
    for row in rows {
        let (interaction_type, feature_category, frequency, success_rate, avg_duration) = 
            row.map_err(|e| format!("Failed to process row: {}", e))?;
        
        profile_data.insert(
            format!("{}_{}", interaction_type, feature_category),
            serde_json::json!({
                "frequency": frequency,
                "success_rate": success_rate,
                "avg_duration": avg_duration
            })
        );
    }
    
    // Generate deterministic profile hash
    let profile_json = serde_json::to_string(&profile_data)
        .map_err(|e| format!("Failed to serialize profile: {}", e))?;
    
    use std::collections::hash_map::DefaultHasher;
    use std::hash::{Hash, Hasher};
    
    let mut hasher = DefaultHasher::new();
    profile_json.hash(&mut hasher);
    let profile_hash = format!("profile_{:x}", hasher.finish());
    
    Ok(profile_hash)
}

/// Analyze user behavior patterns
#[tauri::command]
pub async fn analyze_user_behavior_patterns(
    db: State<'_, AgentDb>,
    user_session_hash: String,
    analysis_depth: String,
) -> Result<Vec<BehaviorPattern>, String> {
    let conn = db.0.lock().map_err(|e| e.to_string())?;
    
    let mut patterns = Vec::new();
    
    // Analyze workflow sequences
    let sequence_patterns = analyze_workflow_sequences(&conn, &user_session_hash)
        .map_err(|e| format!("Failed to analyze workflow sequences: {}", e))?;
    patterns.extend(sequence_patterns);
    
    // Analyze feature combinations
    let combination_patterns = analyze_feature_combinations(&conn, &user_session_hash)
        .map_err(|e| format!("Failed to analyze feature combinations: {}", e))?;
    patterns.extend(combination_patterns);
    
    // Analyze time-based patterns
    if analysis_depth == "deep" {
        let time_patterns = analyze_time_based_patterns(&conn, &user_session_hash)
            .map_err(|e| format!("Failed to analyze time patterns: {}", e))?;
        patterns.extend(time_patterns);
    }
    
    Ok(patterns)
}

/// Get content-based recommendations for agents
#[tauri::command]
pub async fn get_content_based_recommendations(
    db: State<'_, AgentDb>,
    user_profile_hash: String,
    project_context: Option<String>, // JSON string
    limit: Option<i32>,
) -> Result<Vec<Recommendation>, String> {
    let conn = db.0.lock().map_err(|e| e.to_string())?;
    
    let limit = limit.unwrap_or(10);
    let mut recommendations = Vec::new();
    
    // Parse project context
    let context: HashMap<String, serde_json::Value> = project_context
        .as_ref()
        .and_then(|ctx| serde_json::from_str(&ctx).ok())
        .unwrap_or_default();
    
    // Get project type from context
    let project_type = context.get("projectType")
        .and_then(|v| v.as_str())
        .unwrap_or("general");
    
    // Find agents matching project type and user patterns
    let mut stmt = conn.prepare(
        "SELECT ma.remote_id, ma.name, ma.description, ma.category_id, ma.rating_average,
                ma.download_count, ac.name as category_name
         FROM marketplace_agents ma
         LEFT JOIN agent_categories ac ON ma.category_id = ac.id
         WHERE ma.is_installed = 0 
           AND ma.rating_average > 3.0
           AND (ac.slug LIKE ? OR ma.tags LIKE ? OR ma.description LIKE ?)
         ORDER BY ma.rating_average DESC, ma.download_count DESC
         LIMIT ?"
    ).map_err(|e| format!("Failed to prepare recommendation query: {}", e))?;
    
    let project_pattern = format!("%{}%", project_type);
    let rows = stmt.query_map(params![project_pattern, project_pattern, project_pattern, limit], |row| {
        Ok((
            row.get::<_, String>(0)?,
            row.get::<_, String>(1)?,
            row.get::<_, String>(2)?,
            row.get::<_, Option<i32>>(3)?,
            row.get::<_, f64>(4)?,
            row.get::<_, i32>(5)?,
            row.get::<_, Option<String>>(6)?
        ))
    }).map_err(|e| format!("Failed to query recommendations: {}", e))?;
    
    for row in rows {
        let (remote_id, _name, _description, _category_id, rating, downloads, category_name) = 
            row.map_err(|e| format!("Failed to process recommendation row: {}", e))?;
        
        // Calculate relevance score based on rating, downloads, and context match
        let base_score = (rating / 5.0) * 0.7 + (downloads as f64 / 1000.0).min(1.0) * 0.3;
        let context_boost = if _description.to_lowercase().contains(project_type) { 0.2 } else { 0.0 };
        let relevance_score = (base_score + context_boost).min(1.0);
        
        recommendations.push(Recommendation {
            id: None,
            user_profile_hash: user_profile_hash.clone(),
            recommendation_type: "agent".to_string(),
            item_id: remote_id,
            item_category: category_name,
            relevance_score,
            confidence_score: (rating / 5.0) * 0.8,
            priority_score: relevance_score,
            reasoning_data: Some(serde_json::json!({
                "match_type": "content_based",
                "project_type": project_type,
                "agent_rating": rating,
                "download_count": downloads,
                "context_match": context_boost > 0.0
            }).to_string()),
            context_triggers: project_context.clone(),
            expected_benefit: Some(serde_json::json!({
                "efficiency_gain": "high",
                "learning_curve": "low",
                "task_automation": true
            }).to_string()),
            algorithm_version: Some("content_v1.0".to_string()),
        });
    }
    
    Ok(recommendations)
}

/// Get collaborative filtering recommendations
#[tauri::command]
pub async fn get_collaborative_recommendations(
    db: State<'_, AgentDb>,
    user_profile_hash: String,
    limit: Option<i32>,
) -> Result<Vec<Recommendation>, String> {
    let conn = db.0.lock().map_err(|e| e.to_string())?;
    
    let limit = limit.unwrap_or(10);
    let mut recommendations = Vec::new();
    
    // Find similar users based on behavior patterns
    let similar_users = find_similar_users(&conn, &user_profile_hash)
        .map_err(|e| format!("Failed to find similar users: {}", e))?;
    
    if similar_users.is_empty() {
        return Ok(recommendations);
    }
    
    // Get agents used by similar users that current user hasn't used
    let similar_user_hashes: Vec<String> = similar_users.into_iter().map(|(hash, _)| hash).collect();
    let placeholders = similar_user_hashes.iter().map(|_| "?").collect::<Vec<_>>().join(",");
    
    let query = format!(
        "SELECT ui.feature_id, COUNT(*) as usage_count,
                AVG(CASE WHEN ui.success_indicator THEN 1.0 ELSE 0.0 END) as success_rate,
                ma.name, ma.description, ma.rating_average
         FROM user_interactions ui
         JOIN marketplace_agents ma ON ui.feature_id = ma.remote_id
         WHERE ui.user_session_hash IN ({})
           AND ui.interaction_type = 'agent_execute'
           AND ui.feature_id NOT IN (
               SELECT feature_id FROM user_interactions 
               WHERE user_session_hash = ? AND interaction_type = 'agent_execute'
           )
         GROUP BY ui.feature_id
         HAVING usage_count >= 2 AND success_rate > 0.7
         ORDER BY usage_count DESC, success_rate DESC
         LIMIT ?",
        placeholders
    );
    
    let mut params: Vec<&dyn rusqlite::ToSql> = similar_user_hashes.iter()
        .map(|h| h as &dyn rusqlite::ToSql)
        .collect();
    params.push(&user_profile_hash);
    params.push(&limit);
    
    let mut stmt = conn.prepare(&query)
        .map_err(|e| format!("Failed to prepare collaborative query: {}", e))?;
    
    let rows = stmt.query_map(params.as_slice(), |row| {
        Ok((
            row.get::<_, String>(0)?,
            row.get::<_, i32>(1)?,
            row.get::<_, f64>(2)?,
            row.get::<_, String>(3)?,
            row.get::<_, String>(4)?,
            row.get::<_, f64>(5)?
        ))
    }).map_err(|e| format!("Failed to query collaborative recommendations: {}", e))?;
    
    for row in rows {
        let (feature_id, usage_count, success_rate, _name, _description, rating) = 
            row.map_err(|e| format!("Failed to process collaborative row: {}", e))?;
        
        let relevance_score = (success_rate * 0.6 + (usage_count as f64 / 10.0).min(1.0) * 0.4).min(1.0);
        
        recommendations.push(Recommendation {
            id: None,
            user_profile_hash: user_profile_hash.clone(),
            recommendation_type: "agent".to_string(),
            item_id: feature_id,
            item_category: Some("collaborative".to_string()),
            relevance_score,
            confidence_score: success_rate * 0.9,
            priority_score: relevance_score * 1.1, // Boost collaborative recommendations
            reasoning_data: Some(serde_json::json!({
                "match_type": "collaborative_filtering",
                "similar_users_count": similar_user_hashes.len(),
                "usage_by_similar_users": usage_count,
                "success_rate": success_rate,
                "agent_rating": rating
            }).to_string()),
            context_triggers: None,
            expected_benefit: Some(serde_json::json!({
                "social_proof": "high",
                "proven_success": true,
                "community_validated": true
            }).to_string()),
            algorithm_version: Some("collaborative_v1.0".to_string()),
        });
    }
    
    Ok(recommendations)
}

/// Cache recommendations for a user
#[tauri::command]
pub async fn cache_recommendations(
    db: State<'_, AgentDb>,
    recommendations: Vec<Recommendation>,
) -> Result<(), String> {
    let conn = db.0.lock().map_err(|e| e.to_string())?;
    
    let mut stmt = conn.prepare(
        "INSERT OR REPLACE INTO recommendations (
            user_profile_hash, recommendation_type, item_id, item_category,
            relevance_score, confidence_score, priority_score,
            reasoning_data, context_triggers, expected_benefit,
            algorithm_version, generated_at, expires_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)"
    ).map_err(|e| format!("Failed to prepare cache statement: {}", e))?;
    
    let expires_at = chrono::Utc::now() + chrono::Duration::hours(24);
    
    for rec in recommendations {
        stmt.execute(params![
            rec.user_profile_hash,
            rec.recommendation_type,
            rec.item_id,
            rec.item_category,
            rec.relevance_score,
            rec.confidence_score,
            rec.priority_score,
            rec.reasoning_data,
            rec.context_triggers,
            rec.expected_benefit,
            rec.algorithm_version,
            chrono::Utc::now().format("%Y-%m-%d %H:%M:%S%.3f").to_string(),
            expires_at.format("%Y-%m-%d %H:%M:%S%.3f").to_string()
        ]).map_err(|e| format!("Failed to cache recommendation: {}", e))?;
    }
    
    Ok(())
}

/// Update recommendation feedback
#[tauri::command]
pub async fn update_recommendation_feedback(
    db: State<'_, AgentDb>,
    recommendation_id: i64,
    action: String,
    rating: Option<i32>,
    feedback_text: Option<String>,
    _context: Option<HashMap<String, serde_json::Value>>,
) -> Result<(), String> {
    let conn = db.0.lock().map_err(|e| e.to_string())?;
    
    let now = chrono::Utc::now().format("%Y-%m-%d %H:%M:%S%.3f").to_string();
    
    match action.as_str() {
        "shown" => {
            conn.execute(
                "UPDATE recommendations SET shown_to_user = 1, shown_at = ? WHERE id = ?",
                params![now, recommendation_id]
            ).map_err(|e| format!("Failed to update shown status: {}", e))?;
        },
        "clicked" => {
            conn.execute(
                "UPDATE recommendations SET user_clicked = 1, clicked_at = ? WHERE id = ?",
                params![now, recommendation_id]
            ).map_err(|e| format!("Failed to update clicked status: {}", e))?;
        },
        "adopted" => {
            conn.execute(
                "UPDATE recommendations SET user_adopted = 1, adopted_at = ?, outcome_success = 1 WHERE id = ?",
                params![now, recommendation_id]
            ).map_err(|e| format!("Failed to update adopted status: {}", e))?;
        },
        "dismissed" => {
            conn.execute(
                "UPDATE recommendations SET user_dismissed = 1, dismissed_at = ? WHERE id = ?",
                params![now, recommendation_id]
            ).map_err(|e| format!("Failed to update dismissed status: {}", e))?;
        },
        "rated" => {
            conn.execute(
                "UPDATE recommendations SET user_feedback_rating = ?, user_feedback_text = ? WHERE id = ?",
                params![rating, feedback_text, recommendation_id]
            ).map_err(|e| format!("Failed to update rating: {}", e))?;
        },
        _ => return Err(format!("Unknown action: {}", action)),
    }
    
    Ok(())
}

/// Check if recommendation system migrations have been run
#[tauri::command]
pub async fn check_recommendation_migrations(
    db: State<'_, AgentDb>,
) -> Result<bool, String> {
    let conn = db.0.lock().map_err(|e| e.to_string())?;
    
    let mut stmt = conn.prepare(
        "SELECT name FROM sqlite_master WHERE type='table' AND name='user_interactions'"
    ).map_err(|e| format!("Failed to check migrations: {}", e))?;
    
    let exists = stmt.exists([]).map_err(|e| format!("Failed to check table existence: {}", e))?;
    
    Ok(exists)
}

/// Run recommendation system database migrations
#[tauri::command]
pub async fn run_recommendation_migrations(
    db: State<'_, AgentDb>,
) -> Result<(), String> {
    let conn = db.0.lock().map_err(|e| e.to_string())?;
    
    // Read and execute the migration SQL
    let migration_sql = include_str!("../../../migrations/002_recommendation_system.sql");
    
    // Execute the migration in a transaction
    conn.execute("BEGIN TRANSACTION", [])
        .map_err(|e| format!("Failed to begin transaction: {}", e))?;
    
    // Split the SQL by statement and execute each one
    for statement in migration_sql.split(';') {
        let statement = statement.trim();
        if !statement.is_empty() && !statement.starts_with("--") {
            conn.execute(statement, [])
                .map_err(|e| format!("Failed to execute migration statement: {} - SQL: {}", e, statement))?;
        }
    }
    
    conn.execute("COMMIT", [])
        .map_err(|e| format!("Failed to commit transaction: {}", e))?;
    
    Ok(())
}

// Helper functions

fn analyze_workflow_sequences(conn: &Connection, user_hash: &str) -> Result<Vec<BehaviorPattern>, String> {
    let mut patterns = Vec::new();
    
    // Analyze common sequences of interactions
    let mut stmt = conn.prepare(
        "SELECT feature_id, interaction_type, timestamp
         FROM user_interactions 
         WHERE user_session_hash = ?
           AND datetime(timestamp) > datetime('now', '-7 days')
         ORDER BY timestamp"
    ).map_err(|e| format!("Failed to prepare workflow sequence query: {}", e))?;
    
    let rows = stmt.query_map(params![user_hash], |row| {
        Ok((
            row.get::<_, String>(0)?,
            row.get::<_, String>(1)?,
            row.get::<_, String>(2)?
        ))
    }).map_err(|e| format!("Failed to query workflow sequences: {}", e))?;
    
    let mut sequence = Vec::new();
    for row in rows {
        let (feature_id, interaction_type, _timestamp) = row.map_err(|e| format!("Failed to process workflow sequence row: {}", e))?;
        sequence.push(format!("{}:{}", interaction_type, feature_id));
        
        // Analyze sequences of length 3-5
        if sequence.len() >= 3 {
            let pattern_data = serde_json::json!({
                "sequence": sequence.clone(),
                "length": sequence.len()
            });
            
            patterns.push(BehaviorPattern {
                pattern_type: "workflow_sequence".to_string(),
                pattern_data: pattern_data.to_string(),
                frequency_score: 1.0, // Would calculate actual frequency
                success_score: 0.8,   // Would calculate from success indicators
                efficiency_score: 0.7,
                confidence_level: 0.6,
            });
            
            if sequence.len() > 5 {
                sequence.remove(0); // Keep window size manageable
            }
        }
    }
    
    Ok(patterns)
}

fn analyze_feature_combinations(conn: &Connection, user_hash: &str) -> Result<Vec<BehaviorPattern>, String> {
    let mut patterns = Vec::new();
    
    // Find features commonly used together
    let mut stmt = conn.prepare(
        "SELECT feature_id, feature_category, COUNT(*) as frequency
         FROM user_interactions 
         WHERE user_session_hash = ?
           AND datetime(timestamp) > datetime('now', '-30 days')
         GROUP BY feature_id, feature_category
         HAVING frequency > 2
         ORDER BY frequency DESC"
    ).map_err(|e| format!("Failed to prepare feature combinations query: {}", e))?;
    
    let rows = stmt.query_map(params![user_hash], |row| {
        Ok((
            row.get::<_, String>(0)?,
            row.get::<_, String>(1)?,
            row.get::<_, i32>(2)?
        ))
    }).map_err(|e| format!("Failed to query feature combinations: {}", e))?;
    
    for row in rows {
        let (feature_id, feature_category, frequency) = row.map_err(|e| format!("Failed to process feature combination row: {}", e))?;
        
        let pattern_data = serde_json::json!({
            "feature_id": feature_id,
            "feature_category": feature_category,
            "usage_frequency": frequency
        });
        
        patterns.push(BehaviorPattern {
            pattern_type: "feature_combination".to_string(),
            pattern_data: pattern_data.to_string(),
            frequency_score: (frequency as f64 / 10.0).min(1.0),
            success_score: 0.8,
            efficiency_score: 0.7,
            confidence_level: 0.8,
        });
    }
    
    Ok(patterns)
}

fn analyze_time_based_patterns(conn: &Connection, user_hash: &str) -> Result<Vec<BehaviorPattern>, String> {
    let mut patterns = Vec::new();
    
    // Analyze usage patterns by time of day
    let mut stmt = conn.prepare(
        "SELECT strftime('%H', timestamp) as hour, COUNT(*) as count
         FROM user_interactions 
         WHERE user_session_hash = ?
           AND datetime(timestamp) > datetime('now', '-30 days')
         GROUP BY hour
         ORDER BY count DESC
         LIMIT 5"
    ).map_err(|e| format!("Failed to prepare time-based patterns query: {}", e))?;
    
    let rows = stmt.query_map(params![user_hash], |row| {
        Ok((
            row.get::<_, String>(0)?,
            row.get::<_, i32>(1)?
        ))
    }).map_err(|e| format!("Failed to query time-based patterns: {}", e))?;
    
    for row in rows {
        let (hour, count) = row.map_err(|e| format!("Failed to process time-based pattern row: {}", e))?;
        
        let pattern_data = serde_json::json!({
            "peak_hour": hour,
            "usage_count": count,
            "pattern_type": "time_of_day"
        });
        
        patterns.push(BehaviorPattern {
            pattern_type: "time_based".to_string(),
            pattern_data: pattern_data.to_string(),
            frequency_score: (count as f64 / 20.0).min(1.0),
            success_score: 0.7,
            efficiency_score: 0.8,
            confidence_level: 0.6,
        });
    }
    
    Ok(patterns)
}

fn find_similar_users(conn: &Connection, user_hash: &str) -> Result<Vec<(String, f64)>, String> {
    // Simple similarity based on shared feature usage
    let mut stmt = conn.prepare(
        "SELECT ui2.user_session_hash, 
                COUNT(DISTINCT ui1.feature_id) as shared_features,
                COUNT(DISTINCT ui2.feature_id) as user2_features
         FROM user_interactions ui1
         JOIN user_interactions ui2 ON ui1.feature_id = ui2.feature_id
         WHERE ui1.user_session_hash = ?
           AND ui2.user_session_hash != ?
           AND ui1.interaction_type = ui2.interaction_type
           AND datetime(ui1.timestamp) > datetime('now', '-30 days')
           AND datetime(ui2.timestamp) > datetime('now', '-30 days')
         GROUP BY ui2.user_session_hash
         HAVING shared_features >= 3
         ORDER BY shared_features DESC
         LIMIT 10"
    ).map_err(|e| format!("Failed to prepare similar users query: {}", e))?;
    
    let rows = stmt.query_map(params![user_hash, user_hash], |row| {
        Ok((
            row.get::<_, String>(0)?,
            row.get::<_, i32>(1)?,
            row.get::<_, i32>(2)?
        ))
    }).map_err(|e| format!("Failed to query similar users: {}", e))?;
    
    let mut similar_users = Vec::new();
    for row in rows {
        let (other_user_hash, shared_features, _user2_features) = 
            row.map_err(|e| format!("Failed to process similar user row: {}", e))?;
        let similarity_score = (shared_features as f64 / 10.0).min(1.0);
        similar_users.push((other_user_hash, similarity_score));
    }
    
    Ok(similar_users)
}