use rusqlite::params;
use log::info;
use crate::commands::marketplace::MarketplaceDb;
use tauri::State;

/// Seed the marketplace database with sample agents for development/demo
#[tauri::command]
pub async fn marketplace_seed_sample_data(
    db: State<'_, MarketplaceDb>,
) -> Result<String, String> {
    let conn = db.0.lock().map_err(|e| e.to_string())?;
    
    // Check if we already have agents
    let existing_count: i64 = conn
        .query_row("SELECT COUNT(*) FROM marketplace_agents", [], |row| row.get(0))
        .unwrap_or(0);
    
    if existing_count > 0 {
        return Ok(format!("Database already has {} agents, skipping seed", existing_count));
    }
    
    // Sample agents data
    let sample_agents = vec![
        (
            "git-commit-bot",
            "Git Commit Bot",
            "git-commit",
            "You are a Git commit message generator. Analyze code changes and generate meaningful, conventional commit messages.",
            "Generate semantic commit messages",
            "sonnet",
            "Claudia Team",
            "https://github.com/getAsterisk/claudia",
            "Automatically generates meaningful commit messages based on code changes",
            "This agent analyzes your staged changes and generates semantic, meaningful commit messages following conventional commit standards. It helps maintain a clean git history and improves team collaboration.",
            "1.2.0",
            "MIT",
            r#"["git", "automation", "productivity", "development"]"#,
            1, // development category
            "https://raw.githubusercontent.com/getAsterisk/claudia/main/cc_agents/git-commit-bot.claudia.json",
            "https://github.com/getAsterisk/claudia/blob/main/cc_agents/git-commit-bot.claudia.json",
            50 * 1024, // 50KB
            15420,
            4.8,
            342
        ),
        (
            "code-review-agent",
            "Code Review Agent",
            "code",
            "You are a code review assistant. Analyze code for best practices, potential bugs, security issues, and performance improvements.",
            "Review code and provide suggestions",
            "sonnet",
            "DevTools Inc",
            "https://github.com/devtools/code-review-agent",
            "Provides comprehensive code reviews with suggestions for improvement",
            "An intelligent code review agent that analyzes your code for best practices, potential bugs, security issues, and performance improvements. Integrates with popular version control systems.",
            "2.1.3",
            "Apache-2.0",
            r#"["code-review", "quality", "security", "best-practices", "development"]"#,
            1, // development category
            "https://raw.githubusercontent.com/getAsterisk/claudia/main/cc_agents/code-review-agent.claudia.json",
            "https://github.com/getAsterisk/claudia/blob/main/cc_agents/code-review-agent.claudia.json",
            120 * 1024, // 120KB
            8932,
            4.6,
            189
        ),
        (
            "ui-component-generator",
            "UI Component Generator",
            "component",
            "You are a React component generator. Create TypeScript React components with styled-components and comprehensive documentation.",
            "Generate React components",
            "sonnet",
            "Frontend Masters",
            "https://github.com/frontend-masters/ui-generator",
            "Generate React components with TypeScript and styled-components",
            "Rapidly generate React components with TypeScript definitions, styled-components, and comprehensive documentation. Supports various UI frameworks and design systems.",
            "1.0.5",
            "BSD-3-Clause",
            r#"["react", "typescript", "components", "ui", "frontend"]"#,
            2, // content category (we'll map this appropriately)
            "https://raw.githubusercontent.com/getAsterisk/claudia/main/cc_agents/ui-component-generator.claudia.json",
            "https://github.com/getAsterisk/claudia/blob/main/cc_agents/ui-component-generator.claudia.json",
            80 * 1024, // 80KB
            5673,
            4.4,
            92
        ),
        (
            "security-scanner",
            "Security Vulnerability Scanner",
            "shield",
            "You are a security scanner. Identify OWASP Top 10 vulnerabilities, insecure coding patterns, and provide automated fixes.",
            "Scan code for security vulnerabilities",
            "sonnet",
            "SecureCode Labs",
            "https://github.com/securecode/scanner",
            "Scans code for security vulnerabilities and provides fixes",
            "Comprehensive security scanner that identifies OWASP Top 10 vulnerabilities, insecure coding patterns, and provides automated fixes with detailed explanations.",
            "3.1.0",
            "GPL-3.0",
            r#"["security", "vulnerabilities", "scanning", "owasp", "development"]"#,
            1, // development category
            "https://raw.githubusercontent.com/getAsterisk/claudia/main/cc_agents/security-scanner.claudia.json",
            "https://github.com/getAsterisk/claudia/blob/main/cc_agents/security-scanner.claudia.json",
            200 * 1024, // 200KB
            12890,
            4.8,
            278
        ),
        (
            "api-documentation-bot",
            "API Documentation Bot",
            "file-text",
            "You are an API documentation generator. Scan codebases and generate beautiful, interactive API documentation.",
            "Generate API documentation",
            "sonnet",
            "DocGen Solutions",
            "https://github.com/docgen/api-bot",
            "Automatically generates comprehensive API documentation from code",
            "Scans your codebase and generates beautiful, interactive API documentation. Supports OpenAPI/Swagger, GraphQL, and REST APIs with automatic schema detection.",
            "1.4.2",
            "MIT",
            r#"["documentation", "api", "swagger", "openapi", "development"]"#,
            1, // development category
            "https://raw.githubusercontent.com/getAsterisk/claudia/main/cc_agents/api-documentation-bot.claudia.json",
            "https://github.com/getAsterisk/claudia/blob/main/cc_agents/api-documentation-bot.claudia.json",
            95 * 1024, // 95KB
            3421,
            4.7,
            67
        ),
        (
            "database-optimizer",
            "Database Performance Optimizer",
            "database",
            "You are a database optimization expert. Analyze SQL queries, suggest indexes, identify bottlenecks, and provide performance improvements.",
            "Optimize database performance",
            "sonnet",
            "DB Experts",
            "https://github.com/dbexperts/optimizer",
            "Analyzes and optimizes database queries for better performance",
            "Advanced database optimization agent that analyzes SQL queries, suggests indexes, identifies bottlenecks, and provides performance improvement recommendations for various database systems.",
            "2.0.1",
            "Commercial",
            r#"["database", "performance", "sql", "optimization", "development"]"#,
            1, // development category
            "https://raw.githubusercontent.com/getAsterisk/claudia/main/cc_agents/database-optimizer.claudia.json",
            "https://github.com/getAsterisk/claudia/blob/main/cc_agents/database-optimizer.claudia.json",
            150 * 1024, // 150KB
            7234,
            4.9,
            156
        ),
    ];
    
    let mut agents_added = 0;
    
    for (remote_id, name, icon, system_prompt, default_task, model, author, author_url, description, long_description, version, license, tags, category_id, download_url, source_url, file_size, download_count, rating_average, rating_count) in sample_agents {
        let result = conn.execute(
            "INSERT INTO marketplace_agents (
                remote_id, name, icon, system_prompt, default_task, model, 
                author, author_url, description, long_description, version, license, 
                tags, category_id, download_url, source_url, file_size, 
                download_count, rating_average, rating_count, is_installed,
                enable_file_read, enable_file_write, enable_network
            ) VALUES (
                ?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8, ?9, ?10, ?11, ?12, ?13, ?14, ?15, ?16, ?17, ?18, ?19, ?20, 0, 1, 1, 0
            )",
            params![
                remote_id, name, icon, system_prompt, default_task, model,
                author, author_url, description, long_description, version, license,
                tags, category_id, download_url, source_url, file_size,
                download_count, rating_average, rating_count
            ],
        );
        
        match result {
            Ok(_) => {
                agents_added += 1;
                info!("Added sample agent: {}", name);
            }
            Err(e) => {
                eprintln!("Failed to add agent {}: {}", name, e);
            }
        }
    }
    
    info!("Seeded marketplace with {} sample agents", agents_added);
    Ok(format!("Successfully seeded marketplace with {} sample agents", agents_added))
}