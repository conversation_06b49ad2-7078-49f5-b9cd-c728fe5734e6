use anyhow;
use reqwest;
use serde::{Deserialize, Serialize};
use std::time::Duration;
use tauri::command;

#[derive(Debug, Serialize, Deserialize)]
pub struct FetchUrlResponse {
    pub success: bool,
    pub data: Option<String>,
    pub error: Option<String>,
}

/// Fetch content from a URL with proper error handling and rate limiting
#[command]
pub async fn fetch_url(url: String) -> Result<String, tauri::Error> {
    async fn fetch_url_impl(url: String) -> anyhow::Result<String> {
        // Create a client with timeout and user agent
        let client = reqwest::Client::builder()
            .timeout(Duration::from_secs(30))
            .user_agent("Claudia AI News Aggregator/1.0")
            .build()
            .map_err(|e| anyhow::anyhow!("Failed to create HTTP client: {}", e))?;

        // Validate URL
        if !url.starts_with("http://") && !url.starts_with("https://") {
            anyhow::bail!("Invalid URL scheme: {}", url);
        }

        // Make the request
        let response = client
            .get(&url)
            .send()
            .await
            .map_err(|e| anyhow::anyhow!("Failed to fetch URL: {}", e))?;

        // Check if the response was successful
        if !response.status().is_success() {
            anyhow::bail!("HTTP error: {}", response.status());
        }

        // Get the content type to ensure we're dealing with text
        let content_type = response
            .headers()
            .get("content-type")
            .and_then(|ct| ct.to_str().ok())
            .unwrap_or("");

        if !content_type.contains("text") && 
           !content_type.contains("json") && 
           !content_type.contains("xml") &&
           !content_type.contains("application/rss") {
            anyhow::bail!("Unsupported content type");
        }

        // Get the text content
        let text = response
            .text()
            .await
            .map_err(|e| anyhow::anyhow!("Failed to read response body: {}", e))?;

        // Limit response size to prevent memory issues (5MB max)
        if text.len() > 5 * 1024 * 1024 {
            anyhow::bail!("Response too large");
        }

        Ok(text)
    }

    fetch_url_impl(url).await.map_err(tauri::Error::from)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_fetch_url_invalid_scheme() {
        let result = fetch_url("ftp://example.com".to_string()).await;
        assert!(result.is_err());
    }

    #[tokio::test]
    async fn test_fetch_url_valid_https() {
        let result = fetch_url("https://httpbin.org/json".to_string()).await;
        assert!(result.is_ok());
    }
}