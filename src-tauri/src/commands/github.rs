use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize)]
pub struct GitHubSearchResponse {
    pub items: Vec<serde_json::Value>,
    pub total_count: u32,
}

#[tauri::command]
pub async fn fetch_github_trending(
    query: String,
    sort: String,
    order: String,
    per_page: u32,
) -> Result<GitHubSearchResponse, String> {
    let client = reqwest::Client::new();
    
    let url = format!(
        "https://api.github.com/search/repositories?q={}&sort={}&order={}&per_page={}",
        urlencoding::encode(&query),
        sort,
        order,
        per_page
    );
    
    let response = client
        .get(&url)
        .header("User-Agent", "Claudia-App")
        .header("Accept", "application/vnd.github.v3+json")
        .send()
        .await
        .map_err(|e| format!("Failed to fetch from GitHub: {}", e))?;
    
    if !response.status().is_success() {
        return Err(format!("GitHub API error: {}", response.status()));
    }
    
    let data: GitHubSearchResponse = response
        .json()
        .await
        .map_err(|e| format!("Failed to parse response: {}", e))?;
    
    Ok(data)
}

#[tauri::command]
pub async fn fetch_github_users(
    query: String,
    sort: String,
    order: String,
    per_page: u32,
) -> Result<GitHubSearchResponse, String> {
    let client = reqwest::Client::new();
    
    let url = format!(
        "https://api.github.com/search/users?q={}&sort={}&order={}&per_page={}",
        urlencoding::encode(&query),
        sort,
        order,
        per_page
    );
    
    let response = client
        .get(&url)
        .header("User-Agent", "Claudia-App")
        .header("Accept", "application/vnd.github.v3+json")
        .send()
        .await
        .map_err(|e| format!("Failed to fetch from GitHub: {}", e))?;
    
    if !response.status().is_success() {
        return Err(format!("GitHub API error: {}", response.status()));
    }
    
    let data: GitHubSearchResponse = response
        .json()
        .await
        .map_err(|e| format!("Failed to parse response: {}", e))?;
    
    Ok(data)
}