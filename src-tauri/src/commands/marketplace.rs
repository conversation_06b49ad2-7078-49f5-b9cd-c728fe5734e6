use anyhow::Result;
use chrono;
use log::{debug, error, info};
use reqwest;
use rusqlite::{params, Connection, Result as SqliteResult, Row};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Mutex;
use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Manager, State};

/// Represents a marketplace agent with extended metadata
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct MarketplaceAgent {
    pub id: Option<i64>,
    pub remote_id: String,
    pub name: String,
    pub icon: String,
    pub system_prompt: String,
    pub default_task: Option<String>,
    pub model: String,
    pub author: String,
    pub author_url: Option<String>,
    pub description: String,
    pub long_description: Option<String>,
    pub version: String,
    pub license: Option<String>,
    pub tags: Option<String>, // JSON array as string
    pub category_id: Option<i64>,
    pub download_url: String,
    pub source_url: Option<String>,
    pub documentation_url: Option<String>,
    pub homepage_url: Option<String>,
    pub sha: Option<String>,
    pub file_size: i64,
    pub download_count: i64,
    pub rating_average: f64,
    pub rating_count: i64,
    pub is_installed: bool,
    pub installed_at: Option<String>,
    pub installation_source: Option<String>, // 'github', 'marketplace', 'manual'
    pub enable_file_read: bool,
    pub enable_file_write: bool,
    pub enable_network: bool,
    pub hooks: Option<String>,
    pub published_at: Option<String>,
    pub last_synced_at: String,
    pub created_at: String,
    pub updated_at: String,
}

/// Agent category for classification
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct AgentCategory {
    pub id: i64,
    pub name: String,
    pub slug: String,
    pub description: Option<String>,
    pub icon: Option<String>,
    pub parent_id: Option<i64>,
    pub sort_order: i32,
    pub is_active: bool,
    pub created_at: String,
    pub agent_count: Option<i64>,
}

/// Agent rating and review
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct AgentRating {
    pub id: i64,
    pub marketplace_agent_id: i64,
    pub user_identifier: Option<String>,
    pub rating: i32, // 1-5
    pub review_title: Option<String>,
    pub review_content: Option<String>,
    pub is_verified: bool,
    pub helpful_votes: i32,
    pub total_votes: i32,
    pub created_at: String,
    pub updated_at: String,
}

/// Agent dependency
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct AgentDependency {
    pub id: i64,
    pub marketplace_agent_id: i64,
    pub dependency_type: String, // 'agent', 'tool', 'binary', 'npm_package'
    pub dependency_name: String,
    pub dependency_version: Option<String>,
    pub is_required: bool,
    pub created_at: String,
    pub is_satisfied: Option<bool>,
    pub installed_version: Option<String>,
}

/// Agent collection (curated lists)
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct AgentCollection {
    pub id: i64,
    pub name: String,
    pub description: Option<String>,
    pub curator: Option<String>,
    pub is_featured: bool,
    pub is_public: bool,
    pub sort_order: i32,
    pub created_at: String,
    pub updated_at: String,
    pub agent_count: Option<i64>,
}

/// User preferences for marketplace
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct UserPreference {
    pub key: String,
    pub value: String,
    pub value_type: String, // 'string', 'number', 'boolean', 'json'
    pub created_at: String,
    pub updated_at: String,
}

/// Marketplace configuration
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct MarketplaceConfig {
    pub enabled: bool,
    pub auto_update_agents: bool,
    pub download_confirmations: bool,
    pub preferred_categories: String, // JSON array
    pub cache_expiry_hours: i32,
    pub show_experimental: bool,
    pub default_sort_order: String,
    pub anonymous_analytics: bool,
    pub github_token: Option<String>,
    pub github_repo: String,
    pub github_branch: String,
    pub marketplace_api_url: Option<String>,
    pub marketplace_api_key: Option<String>,
}

/// Marketplace cache entry
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct MarketplaceCache {
    pub id: i64,
    pub cache_key: String,
    pub cache_type: String,
    pub data: String, // JSON data
    pub expires_at: String,
    pub created_at: String,
}

/// Search parameters for marketplace agents
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct MarketplaceSearchParams {
    pub query: Option<String>,
    pub category_id: Option<i64>,
    pub tags: Option<Vec<String>>,
    pub author: Option<String>,
    pub sort_by: Option<String>, // 'downloads', 'rating', 'updated', 'alphabetical', 'newest'
    pub sort_order: Option<String>, // 'asc', 'desc'
    pub include_installed: Option<bool>,
    pub include_experimental: Option<bool>,
    pub min_rating: Option<i32>,
    pub page: Option<i32>,
    pub limit: Option<i32>,
}

/// Search results
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct MarketplaceSearchResult {
    pub agents: Vec<MarketplaceAgent>,
    pub total_count: i64,
    pub page: i32,
    pub limit: i32,
    pub total_pages: i32,
    pub filters_applied: MarketplaceSearchParams,
}

/// Agent installation request
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct AgentInstallRequest {
    pub remote_id: String,
    pub source: String, // 'github', 'marketplace'
    pub force_reinstall: Option<bool>,
    pub install_dependencies: Option<bool>,
}

/// Agent installation result
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct AgentInstallResult {
    pub success: bool,
    pub agent: Option<crate::commands::agents::Agent>, // Local agent after conversion
    pub message: String,
    pub errors: Option<Vec<String>>,
    pub warnings: Option<Vec<String>>,
    pub installed_dependencies: Option<Vec<String>>,
    pub skipped_dependencies: Option<Vec<String>>,
}

/// Agent rating submission
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct AgentRatingSubmission {
    pub marketplace_agent_id: i64,
    pub rating: i32, // 1-5
    pub review_title: Option<String>,
    pub review_content: Option<String>,
}

/// Agent sync request
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct AgentSyncRequest {
    pub source: String, // 'github', 'marketplace'
    pub force_refresh: Option<bool>,
    pub categories: Option<Vec<String>>,
}

/// Agent sync result
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct AgentSyncResult {
    pub success: bool,
    pub agents_updated: i32,
    pub agents_added: i32,
    pub agents_removed: i32,
    pub errors: Vec<String>,
    pub warnings: Vec<String>,
    pub sync_duration_ms: i64,
}

/// Enhanced GitHub agent file
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct EnhancedGitHubAgentFile {
    pub name: String,
    pub path: String,
    pub download_url: String,
    pub size: i64,
    pub sha: String,
    pub parsed_metadata: Option<serde_json::Value>,
    pub is_installed: Option<bool>,
    pub local_agent_id: Option<i64>,
    pub needs_update: Option<bool>,
    pub is_valid: Option<bool>,
    pub validation_errors: Option<Vec<String>>,
}

/// Marketplace statistics
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct MarketplaceStats {
    pub total_agents: i64,
    pub total_downloads: i64,
    pub total_ratings: i64,
    pub average_rating: f64,
    pub categories_count: i64,
    pub active_users_count: i64,
    pub popular_tags: Vec<serde_json::Value>,
    pub top_authors: Vec<serde_json::Value>,
    pub recent_activity: serde_json::Value,
}

/// Agent comparison data
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct AgentComparison {
    pub agents: Vec<MarketplaceAgent>,
    pub comparison_fields: Vec<serde_json::Value>,
    pub similarities: Vec<String>,
    pub differences: Vec<String>,
}

/// Database connection state for marketplace
pub struct MarketplaceDb(pub Mutex<Connection>);

/// Initialize the marketplace database schema
pub fn init_marketplace_database(app: &AppHandle) -> SqliteResult<Connection> {
    let app_dir = app
        .path()
        .app_data_dir()
        .expect("Failed to get app data dir");
    std::fs::create_dir_all(&app_dir).expect("Failed to create app data dir");

    let db_path = app_dir.join("marketplace.db");
    let conn = Connection::open(db_path)?;

    // Create marketplace agents table
    conn.execute(
        "CREATE TABLE IF NOT EXISTS marketplace_agents (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            remote_id TEXT NOT NULL UNIQUE,
            name TEXT NOT NULL,
            icon TEXT NOT NULL,
            system_prompt TEXT NOT NULL,
            default_task TEXT,
            model TEXT NOT NULL DEFAULT 'sonnet',
            author TEXT NOT NULL,
            author_url TEXT,
            description TEXT NOT NULL,
            long_description TEXT,
            version TEXT NOT NULL,
            license TEXT,
            tags TEXT, -- JSON array
            category_id INTEGER,
            download_url TEXT NOT NULL,
            source_url TEXT,
            documentation_url TEXT,
            homepage_url TEXT,
            sha TEXT,
            file_size INTEGER NOT NULL DEFAULT 0,
            download_count INTEGER NOT NULL DEFAULT 0,
            rating_average REAL NOT NULL DEFAULT 0.0,
            rating_count INTEGER NOT NULL DEFAULT 0,
            is_installed BOOLEAN NOT NULL DEFAULT 0,
            installed_at TEXT,
            installation_source TEXT,
            enable_file_read BOOLEAN NOT NULL DEFAULT 1,
            enable_file_write BOOLEAN NOT NULL DEFAULT 1,
            enable_network BOOLEAN NOT NULL DEFAULT 0,
            hooks TEXT,
            published_at TEXT,
            last_synced_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
            created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (category_id) REFERENCES agent_categories(id)
        )",
        [],
    )?;

    // Create agent categories table
    conn.execute(
        "CREATE TABLE IF NOT EXISTS agent_categories (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL UNIQUE,
            slug TEXT NOT NULL UNIQUE,
            description TEXT,
            icon TEXT,
            parent_id INTEGER,
            sort_order INTEGER NOT NULL DEFAULT 0,
            is_active BOOLEAN NOT NULL DEFAULT 1,
            created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (parent_id) REFERENCES agent_categories(id)
        )",
        [],
    )?;

    // Create agent ratings table
    conn.execute(
        "CREATE TABLE IF NOT EXISTS agent_ratings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            marketplace_agent_id INTEGER NOT NULL,
            user_identifier TEXT,
            rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
            review_title TEXT,
            review_content TEXT,
            is_verified BOOLEAN NOT NULL DEFAULT 0,
            helpful_votes INTEGER NOT NULL DEFAULT 0,
            total_votes INTEGER NOT NULL DEFAULT 0,
            created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (marketplace_agent_id) REFERENCES marketplace_agents(id) ON DELETE CASCADE
        )",
        [],
    )?;

    // Create agent dependencies table
    conn.execute(
        "CREATE TABLE IF NOT EXISTS agent_dependencies (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            marketplace_agent_id INTEGER NOT NULL,
            dependency_type TEXT NOT NULL CHECK (dependency_type IN ('agent', 'tool', 'binary', 'npm_package')),
            dependency_name TEXT NOT NULL,
            dependency_version TEXT,
            is_required BOOLEAN NOT NULL DEFAULT 1,
            created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (marketplace_agent_id) REFERENCES marketplace_agents(id) ON DELETE CASCADE
        )",
        [],
    )?;

    // Create agent collections table
    conn.execute(
        "CREATE TABLE IF NOT EXISTS agent_collections (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            description TEXT,
            curator TEXT,
            is_featured BOOLEAN NOT NULL DEFAULT 0,
            is_public BOOLEAN NOT NULL DEFAULT 1,
            sort_order INTEGER NOT NULL DEFAULT 0,
            created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
        )",
        [],
    )?;

    // Create agent collection items junction table
    conn.execute(
        "CREATE TABLE IF NOT EXISTS agent_collection_items (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            collection_id INTEGER NOT NULL,
            marketplace_agent_id INTEGER NOT NULL,
            sort_order INTEGER NOT NULL DEFAULT 0,
            added_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (collection_id) REFERENCES agent_collections(id) ON DELETE CASCADE,
            FOREIGN KEY (marketplace_agent_id) REFERENCES marketplace_agents(id) ON DELETE CASCADE,
            UNIQUE(collection_id, marketplace_agent_id)
        )",
        [],
    )?;

    // Create user preferences table
    conn.execute(
        "CREATE TABLE IF NOT EXISTS user_preferences (
            key TEXT PRIMARY KEY,
            value TEXT NOT NULL,
            value_type TEXT NOT NULL DEFAULT 'string' CHECK (value_type IN ('string', 'number', 'boolean', 'json')),
            created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
        )",
        [],
    )?;

    // Create marketplace cache table
    conn.execute(
        "CREATE TABLE IF NOT EXISTS marketplace_cache (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            cache_key TEXT NOT NULL UNIQUE,
            cache_type TEXT NOT NULL,
            data TEXT NOT NULL,
            expires_at TEXT NOT NULL,
            created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
        )",
        [],
    )?;

    // Create agent downloads tracking table
    conn.execute(
        "CREATE TABLE IF NOT EXISTS agent_downloads (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            marketplace_agent_id INTEGER NOT NULL,
            user_identifier TEXT,
            download_source TEXT NOT NULL CHECK (download_source IN ('marketplace', 'github')),
            download_url TEXT,
            success BOOLEAN NOT NULL DEFAULT 1,
            error_message TEXT,
            downloaded_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (marketplace_agent_id) REFERENCES marketplace_agents(id) ON DELETE CASCADE
        )",
        [],
    )?;

    // Create indexes for performance
    conn.execute("CREATE INDEX IF NOT EXISTS idx_marketplace_agents_remote_id ON marketplace_agents(remote_id)", [])?;
    conn.execute("CREATE INDEX IF NOT EXISTS idx_marketplace_agents_category ON marketplace_agents(category_id)", [])?;
    conn.execute("CREATE INDEX IF NOT EXISTS idx_marketplace_agents_author ON marketplace_agents(author)", [])?;
    conn.execute("CREATE INDEX IF NOT EXISTS idx_marketplace_agents_installed ON marketplace_agents(is_installed)", [])?;
    conn.execute("CREATE INDEX IF NOT EXISTS idx_marketplace_agents_rating ON marketplace_agents(rating_average)", [])?;
    conn.execute("CREATE INDEX IF NOT EXISTS idx_marketplace_agents_downloads ON marketplace_agents(download_count)", [])?;
    conn.execute("CREATE INDEX IF NOT EXISTS idx_agent_ratings_agent_id ON agent_ratings(marketplace_agent_id)", [])?;
    conn.execute("CREATE INDEX IF NOT EXISTS idx_agent_dependencies_agent_id ON agent_dependencies(marketplace_agent_id)", [])?;
    conn.execute("CREATE INDEX IF NOT EXISTS idx_marketplace_cache_key ON marketplace_cache(cache_key)", [])?;
    conn.execute("CREATE INDEX IF NOT EXISTS idx_marketplace_cache_expires ON marketplace_cache(expires_at)", [])?;

    // Create triggers for updating timestamps
    conn.execute(
        "CREATE TRIGGER IF NOT EXISTS update_marketplace_agents_timestamp 
         AFTER UPDATE ON marketplace_agents 
         FOR EACH ROW
         BEGIN
             UPDATE marketplace_agents SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
         END",
        [],
    )?;

    conn.execute(
        "CREATE TRIGGER IF NOT EXISTS update_agent_ratings_timestamp 
         AFTER UPDATE ON agent_ratings 
         FOR EACH ROW
         BEGIN
             UPDATE agent_ratings SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
         END",
        [],
    )?;

    conn.execute(
        "CREATE TRIGGER IF NOT EXISTS update_agent_collections_timestamp 
         AFTER UPDATE ON agent_collections 
         FOR EACH ROW
         BEGIN
             UPDATE agent_collections SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
         END",
        [],
    )?;

    conn.execute(
        "CREATE TRIGGER IF NOT EXISTS update_user_preferences_timestamp 
         AFTER UPDATE ON user_preferences 
         FOR EACH ROW
         BEGIN
             UPDATE user_preferences SET updated_at = CURRENT_TIMESTAMP WHERE key = NEW.key;
         END",
        [],
    )?;

    // Insert default categories if they don't exist
    let default_categories = vec![
        ("Development", "development", "Tools for software development and coding tasks", "💻"),
        ("Content", "content", "Agents for writing, editing, and content creation", "✏️"),
        ("Analysis", "analysis", "Data analysis and research assistants", "📊"),
        ("Automation", "automation", "Task automation and workflow optimization", "🤖"),
        ("Communication", "communication", "Email, messaging, and communication helpers", "💬"),
        ("Productivity", "productivity", "Personal and business productivity tools", "⚡"),
        ("Education", "education", "Learning and educational assistants", "🎓"),
        ("Creative", "creative", "Design, art, and creative project assistance", "🎨"),
        ("Business", "business", "Business operations and management tools", "💼"),
        ("Research", "research", "Research and information gathering agents", "🔬"),
    ];

    for (name, slug, description, icon) in default_categories {
        conn.execute(
            "INSERT OR IGNORE INTO agent_categories (name, slug, description, icon, sort_order) VALUES (?1, ?2, ?3, ?4, ?5)",
            params![name, slug, description, icon, 0],
        )?;
    }

    // Insert default configuration if it doesn't exist
    conn.execute(
        "INSERT OR IGNORE INTO user_preferences (key, value, value_type) VALUES 
         ('marketplace_enabled', 'true', 'boolean'),
         ('auto_update_agents', 'false', 'boolean'),
         ('download_confirmations', 'true', 'boolean'),
         ('cache_expiry_hours', '24', 'number'),
         ('show_experimental', 'false', 'boolean'),
         ('default_sort_order', 'downloads', 'string'),
         ('anonymous_analytics', 'true', 'boolean'),
         ('github_repo', 'getAsterisk/claudia', 'string'),
         ('github_branch', 'main', 'string')",
        [],
    )?;

    info!("Initialized marketplace database schema");
    Ok(conn)
}

/// Helper function to map database row to MarketplaceAgent
fn row_to_marketplace_agent(row: &Row) -> rusqlite::Result<MarketplaceAgent> {
    Ok(MarketplaceAgent {
        id: Some(row.get(0)?),
        remote_id: row.get(1)?,
        name: row.get(2)?,
        icon: row.get(3)?,
        system_prompt: row.get(4)?,
        default_task: row.get(5)?,
        model: row.get(6)?,
        author: row.get(7)?,
        author_url: row.get(8)?,
        description: row.get(9)?,
        long_description: row.get(10)?,
        version: row.get(11)?,
        license: row.get(12)?,
        tags: row.get(13)?,
        category_id: row.get(14)?,
        download_url: row.get(15)?,
        source_url: row.get(16)?,
        documentation_url: row.get(17)?,
        homepage_url: row.get(18)?,
        sha: row.get(19)?,
        file_size: row.get(20)?,
        download_count: row.get(21)?,
        rating_average: row.get(22)?,
        rating_count: row.get(23)?,
        is_installed: row.get(24)?,
        installed_at: row.get(25)?,
        installation_source: row.get(26)?,
        enable_file_read: row.get(27)?,
        enable_file_write: row.get(28)?,
        enable_network: row.get(29)?,
        hooks: row.get(30)?,
        published_at: row.get(31)?,
        last_synced_at: row.get(32)?,
        created_at: row.get(33)?,
        updated_at: row.get(34)?,
    })
}

// ============================================================================
// Agent Discovery and Search Commands
// ============================================================================

/// Search marketplace agents with filtering and sorting
#[tauri::command]
pub async fn marketplace_search_agents(
    db: State<'_, MarketplaceDb>,
    params: MarketplaceSearchParams,
) -> Result<MarketplaceSearchResult, String> {
    let conn = db.0.lock().map_err(|e| e.to_string())?;
    
    let page = params.page.unwrap_or(1).max(1);
    let limit = params.limit.unwrap_or(20).min(100).max(1);
    let offset = (page - 1) * limit;
    
    // Build dynamic query string
    let mut base_query = "SELECT id, remote_id, name, icon, system_prompt, default_task, model, author, author_url, description, long_description, version, license, tags, category_id, download_url, source_url, documentation_url, homepage_url, sha, file_size, download_count, rating_average, rating_count, is_installed, installed_at, installation_source, enable_file_read, enable_file_write, enable_network, hooks, published_at, last_synced_at, created_at, updated_at FROM marketplace_agents".to_string();
    
    let mut where_conditions = Vec::new();
    let mut query_params = Vec::new();
    
    // Add WHERE conditions
    if let Some(query) = &params.query {
        if !query.trim().is_empty() {
            where_conditions.push("(name LIKE ?1 OR description LIKE ?1 OR author LIKE ?1 OR tags LIKE ?1)".to_string());
            query_params.push(format!("%{}%", query.trim()));
        }
    }
    
    if let Some(category_id) = params.category_id {
        where_conditions.push(format!("category_id = ?{}", query_params.len() + 1));
        query_params.push(category_id.to_string());
    }
    
    if let Some(author) = &params.author {
        if !author.trim().is_empty() {
            where_conditions.push(format!("author LIKE ?{}", query_params.len() + 1));
            query_params.push(format!("%{}%", author.trim()));
        }
    }
    
    if let Some(min_rating) = params.min_rating {
        where_conditions.push(format!("rating_average >= ?{}", query_params.len() + 1));
        query_params.push(min_rating.to_string());
    }
    
    if let Some(include_installed) = params.include_installed {
        if !include_installed {
            where_conditions.push("is_installed = 0".to_string());
        }
    }
    
    // Add WHERE clause if we have conditions
    if !where_conditions.is_empty() {
        base_query.push_str(" WHERE ");
        base_query.push_str(&where_conditions.join(" AND "));
    }
    
    // Add ORDER BY
    let sort_by = params.sort_by.as_deref().unwrap_or("downloads");
    let sort_order = params.sort_order.as_deref().unwrap_or("desc");
    
    let order_column = match sort_by {
        "rating" => "rating_average",
        "updated" => "updated_at",
        "newest" => "created_at",
        "alphabetical" => "name",
        _ => "download_count", // default to downloads
    };
    
    base_query.push_str(&format!(" ORDER BY {} {}", order_column, sort_order.to_uppercase()));
    base_query.push_str(&format!(" LIMIT {} OFFSET {}", limit, offset));
    
    debug!("Executing marketplace search query: {}", base_query);
    
    let mut stmt = conn.prepare(&base_query).map_err(|e| e.to_string())?;
    
    // Convert params to rusqlite format
    let rusqlite_params: Vec<&dyn rusqlite::ToSql> = query_params.iter().map(|s| s as &dyn rusqlite::ToSql).collect();
    
    let agents = stmt
        .query_map(&rusqlite_params[..], row_to_marketplace_agent)
        .map_err(|e| e.to_string())?
        .collect::<Result<Vec<_>, _>>()
        .map_err(|e| e.to_string())?;
    
    // Get total count for pagination
    let mut count_query = "SELECT COUNT(*) FROM marketplace_agents".to_string();
    if !where_conditions.is_empty() {
        count_query.push_str(" WHERE ");
        count_query.push_str(&where_conditions.join(" AND "));
    }
    let total_count: i64 = conn
        .query_row(&count_query, &rusqlite_params[..], |row| row.get(0))
        .unwrap_or(0);
    
    let total_pages = ((total_count as f64) / (limit as f64)).ceil() as i32;
    
    Ok(MarketplaceSearchResult {
        agents,
        total_count,
        page,
        limit,
        total_pages,
        filters_applied: params,
    })
}

/// Get featured marketplace agents
#[tauri::command]
pub async fn marketplace_get_featured_agents(
    db: State<'_, MarketplaceDb>,
    limit: i32,
) -> Result<Vec<MarketplaceAgent>, String> {
    let conn = db.0.lock().map_err(|e| e.to_string())?;
    let limit = limit.min(50).max(1);
    
    let mut stmt = conn
        .prepare(
            "SELECT id, remote_id, name, icon, system_prompt, default_task, model, author, author_url, description, long_description, version, license, tags, category_id, download_url, source_url, documentation_url, homepage_url, sha, file_size, download_count, rating_average, rating_count, is_installed, installed_at, installation_source, enable_file_read, enable_file_write, enable_network, hooks, published_at, last_synced_at, created_at, updated_at 
             FROM marketplace_agents 
             WHERE rating_average >= 4.0 AND rating_count >= 5 
             ORDER BY (rating_average * 0.7 + (download_count / 100.0) * 0.3) DESC 
             LIMIT ?1"
        )
        .map_err(|e| e.to_string())?;
    
    let agents = stmt
        .query_map(params![limit], row_to_marketplace_agent)
        .map_err(|e| e.to_string())?
        .collect::<Result<Vec<_>, _>>()
        .map_err(|e| e.to_string())?;
    
    Ok(agents)
}

/// Get marketplace agent by remote ID
#[tauri::command]
pub async fn marketplace_get_agent(
    db: State<'_, MarketplaceDb>,
    remote_id: String,
) -> Result<MarketplaceAgent, String> {
    let conn = db.0.lock().map_err(|e| e.to_string())?;
    
    let agent = conn
        .query_row(
            "SELECT id, remote_id, name, icon, system_prompt, default_task, model, author, author_url, description, long_description, version, license, tags, category_id, download_url, source_url, documentation_url, homepage_url, sha, file_size, download_count, rating_average, rating_count, is_installed, installed_at, installation_source, enable_file_read, enable_file_write, enable_network, hooks, published_at, last_synced_at, created_at, updated_at 
             FROM marketplace_agents WHERE remote_id = ?1",
            params![remote_id],
            row_to_marketplace_agent,
        )
        .map_err(|e| format!("Agent not found: {}", e))?;
    
    Ok(agent)
}

/// Get agent suggestions based on installed agents
#[tauri::command]
pub async fn marketplace_get_suggestions(
    db: State<'_, MarketplaceDb>,
    limit: i32,
) -> Result<Vec<MarketplaceAgent>, String> {
    let limit = limit.min(20).max(1);
    
    // Get categories of installed agents - collect all data before any awaits
    let (installed_categories, has_categories) = {
        let conn = db.0.lock().map_err(|e| e.to_string())?;
        
        let installed_categories: Vec<i64> = conn
            .prepare("SELECT DISTINCT category_id FROM marketplace_agents WHERE is_installed = 1 AND category_id IS NOT NULL")
            .map_err(|e| e.to_string())?
            .query_map([], |row| row.get::<_, i64>(0))
            .map_err(|e| e.to_string())?
            .collect::<Result<Vec<_>, _>>()
            .map_err(|e| e.to_string())?;
        
        let has_categories = !installed_categories.is_empty();
        (installed_categories, has_categories)
    }; // Lock is dropped here
    
    if !has_categories {
        // If no installed agents with categories, return general popular agents
        return marketplace_get_featured_agents(db, limit).await;
    }
    
    // Now get suggestions based on categories - acquire lock again
    let agents = {
        let conn = db.0.lock().map_err(|e| e.to_string())?;
        
        // Build query for suggestions
        let placeholders: Vec<String> = installed_categories.iter().map(|_| "?".to_string()).collect();
        let category_filter = format!("category_id IN ({})", placeholders.join(","));
        
        let query = format!(
            "SELECT id, remote_id, name, icon, system_prompt, default_task, model, author, author_url, description, long_description, version, license, tags, category_id, download_url, source_url, documentation_url, homepage_url, sha, file_size, download_count, rating_average, rating_count, is_installed, installed_at, installation_source, enable_file_read, enable_file_write, enable_network, hooks, published_at, last_synced_at, created_at, updated_at 
             FROM marketplace_agents 
             WHERE {} AND is_installed = 0 AND rating_average >= 3.5 
             ORDER BY (rating_average * 0.6 + (download_count / 50.0) * 0.4) DESC 
             LIMIT ?{}",
            category_filter,
            installed_categories.len() + 1
        );
        
        let mut stmt = conn.prepare(&query).map_err(|e| e.to_string())?;
        
        // Build parameters
        let mut rusqlite_params: Vec<&dyn rusqlite::ToSql> = installed_categories
            .iter()
            .map(|id| id as &dyn rusqlite::ToSql)
            .collect();
        rusqlite_params.push(&limit as &dyn rusqlite::ToSql);
        
        // Collect the results before the statement and connection are dropped
        let agents_result = stmt
            .query_map(&rusqlite_params[..], row_to_marketplace_agent)
            .map_err(|e| e.to_string())?
            .collect::<Result<Vec<_>, _>>()
            .map_err(|e| e.to_string())?;
        
        agents_result
    }; // Lock is dropped here
    
    Ok(agents)
}

// ============================================================================
// Agent Installation and Management Commands
// ============================================================================

/// Install an agent from the marketplace
#[tauri::command]
pub async fn marketplace_install_agent(
    _app: AppHandle,
    marketplace_db: State<'_, MarketplaceDb>,
    agents_db: State<'_, crate::commands::agents::AgentDb>,
    request: AgentInstallRequest,
) -> Result<AgentInstallResult, String> {
    info!("Installing marketplace agent: {} from {}", request.remote_id, request.source);
    
    // Get the marketplace agent
    let marketplace_agent = marketplace_get_agent(marketplace_db.clone(), request.remote_id.clone()).await?;
    
    // Check if agent is already installed (unless force_reinstall is true)
    if marketplace_agent.is_installed && !request.force_reinstall.unwrap_or(false) {
        return Ok(AgentInstallResult {
            success: false,
            agent: None,
            message: "Agent is already installed. Use force_reinstall to reinstall.".to_string(),
            errors: Some(vec!["ALREADY_INSTALLED".to_string()]),
            warnings: None,
            installed_dependencies: None,
            skipped_dependencies: None,
        });
    }
    
    // Download and validate agent content
    let agent_content = match download_agent_content(&marketplace_agent.download_url).await {
        Ok(content) => content,
        Err(e) => {
            return Ok(AgentInstallResult {
                success: false,
                agent: None,
                message: format!("Failed to download agent: {}", e),
                errors: Some(vec![e.to_string()]),
                warnings: None,
                installed_dependencies: None,
                skipped_dependencies: None,
            });
        }
    };
    
    // Parse and validate agent export format
    let agent_export: serde_json::Value = match serde_json::from_str(&agent_content) {
        Ok(content) => content,
        Err(e) => {
            return Ok(AgentInstallResult {
                success: false,
                agent: None,
                message: format!("Invalid agent format: {}", e),
                errors: Some(vec!["INVALID_FORMAT".to_string()]),
                warnings: None,
                installed_dependencies: None,
                skipped_dependencies: None,
            });
        }
    };
    
    // Extract agent data from export
    let agent_data = agent_export.get("agent").ok_or("Missing agent data in export")?;
    
    // Create local agent from marketplace agent
    let local_agent = crate::commands::agents::Agent {
        id: None,
        name: agent_data.get("name")
            .and_then(|v| v.as_str())
            .unwrap_or(&marketplace_agent.name)
            .to_string(),
        icon: agent_data.get("icon")
            .and_then(|v| v.as_str())
            .unwrap_or(&marketplace_agent.icon)
            .to_string(),
        system_prompt: agent_data.get("system_prompt")
            .and_then(|v| v.as_str())
            .unwrap_or(&marketplace_agent.system_prompt)
            .to_string(),
        default_task: agent_data.get("default_task")
            .and_then(|v| v.as_str())
            .map(|s| s.to_string())
            .or(marketplace_agent.default_task.clone()),
        model: agent_data.get("model")
            .and_then(|v| v.as_str())
            .unwrap_or(&marketplace_agent.model)
            .to_string(),
        enable_file_read: marketplace_agent.enable_file_read,
        enable_file_write: marketplace_agent.enable_file_write,
        enable_network: marketplace_agent.enable_network,
        hooks: agent_data.get("hooks")
            .and_then(|v| v.as_str())
            .map(|s| s.to_string())
            .or(marketplace_agent.hooks.clone()),
        created_at: chrono::Utc::now().to_rfc3339(),
        updated_at: chrono::Utc::now().to_rfc3339(),
    };
    
    // Create the local agent using the existing agents API
    let agents_conn = agents_db.0.lock().map_err(|e| e.to_string())?;
    
    // Check if agent with same name already exists
    let existing_agent_count: i64 = agents_conn
        .query_row(
            "SELECT COUNT(*) FROM agents WHERE name = ?1",
            params![local_agent.name],
            |row| row.get(0),
        )
        .unwrap_or(0);
    
    let final_name = if existing_agent_count > 0 {
        format!("{} (Marketplace)", local_agent.name)
    } else {
        local_agent.name.clone()
    };
    
    // Insert the local agent
    agents_conn.execute(
        "INSERT INTO agents (name, icon, system_prompt, default_task, model, enable_file_read, enable_file_write, enable_network, hooks) VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8, ?9)",
        params![
            final_name,
            local_agent.icon,
            local_agent.system_prompt,
            local_agent.default_task,
            local_agent.model,
            local_agent.enable_file_read,
            local_agent.enable_file_write,
            local_agent.enable_network,
            local_agent.hooks
        ],
    ).map_err(|e| format!("Failed to create local agent: {}", e))?;
    
    let local_agent_id = agents_conn.last_insert_rowid();
    
    // Fetch the created agent
    let created_agent = agents_conn
        .query_row(
            "SELECT id, name, icon, system_prompt, default_task, model, enable_file_read, enable_file_write, enable_network, hooks, created_at, updated_at FROM agents WHERE id = ?1",
            params![local_agent_id],
            |row| {
                Ok(crate::commands::agents::Agent {
                    id: Some(row.get(0)?),
                    name: row.get(1)?,
                    icon: row.get(2)?,
                    system_prompt: row.get(3)?,
                    default_task: row.get(4)?,
                    model: row.get(5)?,
                    enable_file_read: row.get(6)?,
                    enable_file_write: row.get(7)?,
                    enable_network: row.get(8)?,
                    hooks: row.get(9)?,
                    created_at: row.get(10)?,
                    updated_at: row.get(11)?,
                })
            },
        )
        .map_err(|e| format!("Failed to fetch created agent: {}", e))?;
    
    drop(agents_conn);
    
    // Update marketplace agent as installed
    let marketplace_conn = marketplace_db.0.lock().map_err(|e| e.to_string())?;
    marketplace_conn.execute(
        "UPDATE marketplace_agents SET is_installed = 1, installed_at = CURRENT_TIMESTAMP, installation_source = ?1 WHERE remote_id = ?2",
        params![request.source, request.remote_id],
    ).map_err(|e| format!("Failed to update installation status: {}", e))?;
    
    // Record download statistics
    record_agent_download(&marketplace_conn, &marketplace_agent, &request.source, true, None)?;
    
    info!("Successfully installed marketplace agent: {}", marketplace_agent.name);
    
    Ok(AgentInstallResult {
        success: true,
        agent: Some(created_agent),
        message: format!("Successfully installed agent: {}", final_name),
        errors: None,
        warnings: None,
        installed_dependencies: None,
        skipped_dependencies: None,
    })
}

/// Uninstall a marketplace agent
#[tauri::command]
pub async fn marketplace_uninstall_agent(
    marketplace_db: State<'_, MarketplaceDb>,
    agents_db: State<'_, crate::commands::agents::AgentDb>,
    remote_id: String,
    _remove_data: bool,
) -> Result<serde_json::Value, String> {
    info!("Uninstalling marketplace agent: {}", remote_id);
    
    // Get the marketplace agent
    let marketplace_agent = marketplace_get_agent(marketplace_db.clone(), remote_id.clone()).await?;
    
    if !marketplace_agent.is_installed {
        return Ok(serde_json::json!({
            "success": false,
            "message": "Agent is not installed"
        }));
    }
    
    // Find corresponding local agent by name (approximate match)
    let agents_conn = agents_db.0.lock().map_err(|e| e.to_string())?;
    
    // Try exact name match first, then with " (Marketplace)" suffix
    let possible_names = vec![
        marketplace_agent.name.clone(),
        format!("{} (Marketplace)", marketplace_agent.name),
    ];
    
    let mut local_agent_id: Option<i64> = None;
    for name in possible_names {
        if let Ok(id) = agents_conn.query_row(
            "SELECT id FROM agents WHERE name = ?1",
            params![name],
            |row| row.get::<_, i64>(0),
        ) {
            local_agent_id = Some(id);
            break;
        }
    }
    
    if let Some(agent_id) = local_agent_id {
        // Delete the local agent
        agents_conn.execute(
            "DELETE FROM agents WHERE id = ?1",
            params![agent_id],
        ).map_err(|e| format!("Failed to delete local agent: {}", e))?;
        
        info!("Deleted local agent with ID: {}", agent_id);
    }
    
    drop(agents_conn);
    
    // Update marketplace agent as uninstalled
    let marketplace_conn = marketplace_db.0.lock().map_err(|e| e.to_string())?;
    marketplace_conn.execute(
        "UPDATE marketplace_agents SET is_installed = 0, installed_at = NULL, installation_source = NULL WHERE remote_id = ?1",
        params![remote_id],
    ).map_err(|e| format!("Failed to update installation status: {}", e))?;
    
    info!("Successfully uninstalled marketplace agent: {}", marketplace_agent.name);
    
    Ok(serde_json::json!({
        "success": true,
        "message": format!("Successfully uninstalled agent: {}", marketplace_agent.name)
    }))
}

// ============================================================================
// Category Management Commands
// ============================================================================

/// Get all agent categories
#[tauri::command]
pub async fn marketplace_get_categories(
    db: State<'_, MarketplaceDb>,
    include_inactive: bool,
) -> Result<Vec<AgentCategory>, String> {
    let conn = db.0.lock().map_err(|e| e.to_string())?;
    
    let query = if include_inactive {
        "SELECT c.id, c.name, c.slug, c.description, c.icon, c.parent_id, c.sort_order, c.is_active, c.created_at, COUNT(ma.id) as agent_count
         FROM agent_categories c
         LEFT JOIN marketplace_agents ma ON c.id = ma.category_id
         GROUP BY c.id
         ORDER BY c.sort_order, c.name"
    } else {
        "SELECT c.id, c.name, c.slug, c.description, c.icon, c.parent_id, c.sort_order, c.is_active, c.created_at, COUNT(ma.id) as agent_count
         FROM agent_categories c
         LEFT JOIN marketplace_agents ma ON c.id = ma.category_id
         WHERE c.is_active = 1
         GROUP BY c.id
         ORDER BY c.sort_order, c.name"
    };
    
    let mut stmt = conn.prepare(query).map_err(|e| e.to_string())?;
    
    let categories = stmt
        .query_map([], |row| {
            Ok(AgentCategory {
                id: row.get(0)?,
                name: row.get(1)?,
                slug: row.get(2)?,
                description: row.get(3)?,
                icon: row.get(4)?,
                parent_id: row.get(5)?,
                sort_order: row.get(6)?,
                is_active: row.get(7)?,
                created_at: row.get(8)?,
                agent_count: Some(row.get(9)?),
            })
        })
        .map_err(|e| e.to_string())?
        .collect::<Result<Vec<_>, _>>()
        .map_err(|e| e.to_string())?;
    
    Ok(categories)
}

/// Get category by ID
#[tauri::command]
pub async fn marketplace_get_category(
    db: State<'_, MarketplaceDb>,
    id: i64,
) -> Result<AgentCategory, String> {
    let conn = db.0.lock().map_err(|e| e.to_string())?;
    
    let category = conn
        .query_row(
            "SELECT c.id, c.name, c.slug, c.description, c.icon, c.parent_id, c.sort_order, c.is_active, c.created_at, COUNT(ma.id) as agent_count
             FROM agent_categories c
             LEFT JOIN marketplace_agents ma ON c.id = ma.category_id
             WHERE c.id = ?1
             GROUP BY c.id",
            params![id],
            |row| {
                Ok(AgentCategory {
                    id: row.get(0)?,
                    name: row.get(1)?,
                    slug: row.get(2)?,
                    description: row.get(3)?,
                    icon: row.get(4)?,
                    parent_id: row.get(5)?,
                    sort_order: row.get(6)?,
                    is_active: row.get(7)?,
                    created_at: row.get(8)?,
                    agent_count: Some(row.get(9)?),
                })
            },
        )
        .map_err(|e| format!("Category not found: {}", e))?;
    
    Ok(category)
}

// ============================================================================
// Configuration and Preferences Commands
// ============================================================================

/// Get marketplace configuration
#[tauri::command]
pub async fn marketplace_get_config(
    db: State<'_, MarketplaceDb>,
) -> Result<MarketplaceConfig, String> {
    let conn = db.0.lock().map_err(|e| e.to_string())?;
    
    // Get all preferences and build config
    let mut stmt = conn
        .prepare("SELECT key, value, value_type FROM user_preferences")
        .map_err(|e| e.to_string())?;
    
    let mut config_map: HashMap<String, String> = HashMap::new();
    
    let preferences = stmt
        .query_map([], |row| {
            Ok((
                row.get::<_, String>(0)?,
                row.get::<_, String>(1)?,
                row.get::<_, String>(2)?,
            ))
        })
        .map_err(|e| e.to_string())?;
    
    for pref in preferences {
        let (key, value, _value_type) = pref.map_err(|e| e.to_string())?;
        config_map.insert(key, value);
    }
    
    // Build config with defaults
    let config = MarketplaceConfig {
        enabled: config_map.get("marketplace_enabled")
            .and_then(|v| v.parse().ok())
            .unwrap_or(true),
        auto_update_agents: config_map.get("auto_update_agents")
            .and_then(|v| v.parse().ok())
            .unwrap_or(false),
        download_confirmations: config_map.get("download_confirmations")
            .and_then(|v| v.parse().ok())
            .unwrap_or(true),
        preferred_categories: config_map.get("preferred_categories")
            .cloned()
            .unwrap_or_else(|| "[]".to_string()),
        cache_expiry_hours: config_map.get("cache_expiry_hours")
            .and_then(|v| v.parse().ok())
            .unwrap_or(24),
        show_experimental: config_map.get("show_experimental")
            .and_then(|v| v.parse().ok())
            .unwrap_or(false),
        default_sort_order: config_map.get("default_sort_order")
            .cloned()
            .unwrap_or_else(|| "downloads".to_string()),
        anonymous_analytics: config_map.get("anonymous_analytics")
            .and_then(|v| v.parse().ok())
            .unwrap_or(true),
        github_token: config_map.get("github_token").cloned(),
        github_repo: config_map.get("github_repo")
            .cloned()
            .unwrap_or_else(|| "getAsterisk/claudia".to_string()),
        github_branch: config_map.get("github_branch")
            .cloned()
            .unwrap_or_else(|| "main".to_string()),
        marketplace_api_url: config_map.get("marketplace_api_url").cloned(),
        marketplace_api_key: config_map.get("marketplace_api_key").cloned(),
    };
    
    Ok(config)
}

/// Update marketplace configuration
#[tauri::command]
pub async fn marketplace_update_config(
    db: State<'_, MarketplaceDb>,
    config: serde_json::Value,
) -> Result<MarketplaceConfig, String> {
    // Do all database updates first, then get the config
    {
        let conn = db.0.lock().map_err(|e| e.to_string())?;
        
        // Update each configuration value
        if let Some(enabled) = config.get("enabled").and_then(|v| v.as_bool()) {
            conn.execute(
                "INSERT OR REPLACE INTO user_preferences (key, value, value_type) VALUES ('marketplace_enabled', ?1, 'boolean')",
                params![enabled.to_string()],
            ).map_err(|e| e.to_string())?;
        }
        
        if let Some(auto_update) = config.get("auto_update_agents").and_then(|v| v.as_bool()) {
            conn.execute(
                "INSERT OR REPLACE INTO user_preferences (key, value, value_type) VALUES ('auto_update_agents', ?1, 'boolean')",
                params![auto_update.to_string()],
            ).map_err(|e| e.to_string())?;
        }
        
        if let Some(confirmations) = config.get("download_confirmations").and_then(|v| v.as_bool()) {
            conn.execute(
                "INSERT OR REPLACE INTO user_preferences (key, value, value_type) VALUES ('download_confirmations', ?1, 'boolean')",
                params![confirmations.to_string()],
            ).map_err(|e| e.to_string())?;
        }
        
        if let Some(categories) = config.get("preferred_categories") {
            conn.execute(
                "INSERT OR REPLACE INTO user_preferences (key, value, value_type) VALUES ('preferred_categories', ?1, 'json')",
                params![categories.to_string()],
            ).map_err(|e| e.to_string())?;
        }
        
        if let Some(expiry) = config.get("cache_expiry_hours").and_then(|v| v.as_i64()) {
            conn.execute(
                "INSERT OR REPLACE INTO user_preferences (key, value, value_type) VALUES ('cache_expiry_hours', ?1, 'number')",
                params![expiry.to_string()],
            ).map_err(|e| e.to_string())?;
        }
        
        if let Some(experimental) = config.get("show_experimental").and_then(|v| v.as_bool()) {
            conn.execute(
                "INSERT OR REPLACE INTO user_preferences (key, value, value_type) VALUES ('show_experimental', ?1, 'boolean')",
                params![experimental.to_string()],
            ).map_err(|e| e.to_string())?;
        }
        
        if let Some(sort_order) = config.get("default_sort_order").and_then(|v| v.as_str()) {
            conn.execute(
                "INSERT OR REPLACE INTO user_preferences (key, value, value_type) VALUES ('default_sort_order', ?1, 'string')",
                params![sort_order],
            ).map_err(|e| e.to_string())?;
        }
        
        if let Some(analytics) = config.get("anonymous_analytics").and_then(|v| v.as_bool()) {
            conn.execute(
                "INSERT OR REPLACE INTO user_preferences (key, value, value_type) VALUES ('anonymous_analytics', ?1, 'boolean')",
                params![analytics.to_string()],
            ).map_err(|e| e.to_string())?;
        }
        
        if let Some(token) = config.get("github_token").and_then(|v| v.as_str()) {
            conn.execute(
                "INSERT OR REPLACE INTO user_preferences (key, value, value_type) VALUES ('github_token', ?1, 'string')",
                params![token],
            ).map_err(|e| e.to_string())?;
        }
        
        if let Some(repo) = config.get("github_repo").and_then(|v| v.as_str()) {
            conn.execute(
                "INSERT OR REPLACE INTO user_preferences (key, value, value_type) VALUES ('github_repo', ?1, 'string')",
                params![repo],
            ).map_err(|e| e.to_string())?;
        }
        
        if let Some(branch) = config.get("github_branch").and_then(|v| v.as_str()) {
            conn.execute(
                "INSERT OR REPLACE INTO user_preferences (key, value, value_type) VALUES ('github_branch', ?1, 'string')",
                params![branch],
            ).map_err(|e| e.to_string())?;
        }
        
        if let Some(api_url) = config.get("marketplace_api_url").and_then(|v| v.as_str()) {
            conn.execute(
                "INSERT OR REPLACE INTO user_preferences (key, value, value_type) VALUES ('marketplace_api_url', ?1, 'string')",
                params![api_url],
            ).map_err(|e| e.to_string())?;
        }
        
        if let Some(api_key) = config.get("marketplace_api_key").and_then(|v| v.as_str()) {
            conn.execute(
                "INSERT OR REPLACE INTO user_preferences (key, value, value_type) VALUES ('marketplace_api_key', ?1, 'string')",
                params![api_key],
            ).map_err(|e| e.to_string())?;
        }
    } // Lock is dropped here
    
    // Return updated configuration
    marketplace_get_config(db).await
}

// ============================================================================
// GitHub Integration Commands
// ============================================================================

/// Sync agents from GitHub repository
#[tauri::command]
pub async fn marketplace_sync_github(
    db: State<'_, MarketplaceDb>,
    request: AgentSyncRequest,
) -> Result<AgentSyncResult, String> {
    info!("Starting GitHub sync for marketplace agents");
    let start_time = std::time::Instant::now();
    
    let mut result = AgentSyncResult {
        success: false,
        agents_updated: 0,
        agents_added: 0,
        agents_removed: 0,
        errors: Vec::new(),
        warnings: Vec::new(),
        sync_duration_ms: 0,
    };
    
    // Get GitHub configuration
    let config = marketplace_get_config(db.clone()).await?;
    
    // Fetch agents from GitHub
    match fetch_github_agents(&config).await {
        Ok(github_agents) => {
            result.success = true;
            let conn = db.0.lock().map_err(|e| e.to_string())?;
            
            for github_agent in github_agents {
                match process_github_agent(&conn, &github_agent, request.force_refresh.unwrap_or(false)) {
                    Ok(ProcessResult::Added) => result.agents_added += 1,
                    Ok(ProcessResult::Updated) => result.agents_updated += 1,
                    Ok(ProcessResult::Skipped) => {}, // No change to counters
                    Err(e) => {
                        result.errors.push(format!("Failed to process {}: {}", github_agent.name, e));
                    }
                }
            }
            
            info!("GitHub sync completed: {} added, {} updated", result.agents_added, result.agents_updated);
        }
        Err(e) => {
            result.errors.push(format!("Failed to fetch from GitHub: {}", e));
            error!("GitHub sync failed: {}", e);
        }
    }
    
    result.sync_duration_ms = start_time.elapsed().as_millis() as i64;
    Ok(result)
}

/// Fetch enhanced GitHub agent files with metadata
#[tauri::command]
pub async fn marketplace_fetch_enhanced_github_agents(
    db: State<'_, MarketplaceDb>,
) -> Result<Vec<EnhancedGitHubAgentFile>, String> {
    let config = marketplace_get_config(db.clone()).await?;
    let github_agents = fetch_github_agents(&config).await?;
    
    let mut enhanced_agents = Vec::new();
    
    for github_agent in github_agents {
        let mut enhanced = EnhancedGitHubAgentFile {
            name: github_agent.name.clone(),
            path: github_agent.path.clone(),
            download_url: github_agent.download_url.clone(),
            size: github_agent.size,
            sha: github_agent.sha.clone(),
            parsed_metadata: None,
            is_installed: None,
            local_agent_id: None,
            needs_update: None,
            is_valid: None,
            validation_errors: None,
        };
        
        // Try to get parsed metadata by downloading and parsing the agent
        if let Ok(content) = download_agent_content(&github_agent.download_url).await {
            if let Ok(agent_export) = serde_json::from_str::<serde_json::Value>(&content) {
                if let Some(agent_data) = agent_export.get("agent") {
                    enhanced.parsed_metadata = Some(agent_data.clone());
                    enhanced.is_valid = Some(true);
                } else {
                    enhanced.is_valid = Some(false);
                    enhanced.validation_errors = Some(vec!["Missing agent data in export".to_string()]);
                }
            } else {
                enhanced.is_valid = Some(false);
                enhanced.validation_errors = Some(vec!["Invalid JSON format".to_string()]);
            }
        }
        
        // Check if already installed (do this separately to avoid holding locks across awaits)
        let remote_id = extract_remote_id(&github_agent.name);
        let is_installed = {
            let conn = db.0.lock().map_err(|e| e.to_string())?;
            conn.query_row(
                "SELECT id FROM marketplace_agents WHERE remote_id = ?1 AND is_installed = 1",
                params![remote_id],
                |row| row.get::<_, i64>(0),
            ).is_ok()
        };
        
        enhanced.is_installed = Some(is_installed);
        enhanced_agents.push(enhanced);
    }
    
    Ok(enhanced_agents)
}

// ============================================================================
// Statistics and Analytics Commands
// ============================================================================

/// Get marketplace statistics
#[tauri::command]
pub async fn marketplace_get_stats(
    db: State<'_, MarketplaceDb>,
) -> Result<MarketplaceStats, String> {
    let conn = db.0.lock().map_err(|e| e.to_string())?;
    
    // Get basic counts
    let total_agents: i64 = conn
        .query_row("SELECT COUNT(*) FROM marketplace_agents", [], |row| row.get(0))
        .unwrap_or(0);
    
    let total_downloads: i64 = conn
        .query_row("SELECT COALESCE(SUM(download_count), 0) FROM marketplace_agents", [], |row| row.get(0))
        .unwrap_or(0);
    
    let total_ratings: i64 = conn
        .query_row("SELECT COUNT(*) FROM agent_ratings", [], |row| row.get(0))
        .unwrap_or(0);
    
    let average_rating: f64 = conn
        .query_row("SELECT COALESCE(AVG(rating_average), 0.0) FROM marketplace_agents WHERE rating_count > 0", [], |row| row.get(0))
        .unwrap_or(0.0);
    
    let categories_count: i64 = conn
        .query_row("SELECT COUNT(*) FROM agent_categories WHERE is_active = 1", [], |row| row.get(0))
        .unwrap_or(0);
    
    // Get popular tags (extract from JSON tags field)
    let mut popular_tags = Vec::new();
    if let Ok(mut stmt) = conn.prepare("SELECT tags FROM marketplace_agents WHERE tags IS NOT NULL AND tags != ''") {
        if let Ok(rows) = stmt.query_map([], |row| row.get::<_, String>(0)) {
            let mut tag_counts: HashMap<String, i64> = HashMap::new();
            
            for row in rows.flatten() {
                if let Ok(tags) = serde_json::from_str::<Vec<String>>(&row) {
                    for tag in tags {
                        *tag_counts.entry(tag).or_insert(0) += 1;
                    }
                }
            }
            
            let mut sorted_tags: Vec<_> = tag_counts.into_iter().collect();
            sorted_tags.sort_by(|a, b| b.1.cmp(&a.1));
            
            for (tag, count) in sorted_tags.into_iter().take(10) {
                popular_tags.push(serde_json::json!({ "tag": tag, "count": count }));
            }
        }
    }
    
    // Get top authors
    let mut top_authors = Vec::new();
    if let Ok(mut stmt) = conn.prepare("SELECT author, COUNT(*) as agent_count FROM marketplace_agents GROUP BY author ORDER BY agent_count DESC LIMIT 10") {
        if let Ok(rows) = stmt.query_map([], |row| Ok((row.get::<_, String>(0)?, row.get::<_, i64>(1)?))) {
            for row in rows.flatten() {
                let (author, count) = row;
                top_authors.push(serde_json::json!({ "author": author, "agent_count": count }));
            }
        }
    }
    
    // Get recent activity (last 7 days)
    let seven_days_ago = chrono::Utc::now() - chrono::Duration::days(7);
    let seven_days_ago_str = seven_days_ago.to_rfc3339();
    
    let new_agents_this_week: i64 = conn
        .query_row("SELECT COUNT(*) FROM marketplace_agents WHERE created_at >= ?1", params![seven_days_ago_str], |row| row.get(0))
        .unwrap_or(0);
    
    let downloads_this_week: i64 = conn
        .query_row("SELECT COUNT(*) FROM agent_downloads WHERE downloaded_at >= ?1", params![seven_days_ago_str], |row| row.get(0))
        .unwrap_or(0);
    
    let ratings_this_week: i64 = conn
        .query_row("SELECT COUNT(*) FROM agent_ratings WHERE created_at >= ?1", params![seven_days_ago_str], |row| row.get(0))
        .unwrap_or(0);
    
    let recent_activity = serde_json::json!({
        "new_agents_this_week": new_agents_this_week,
        "downloads_this_week": downloads_this_week,
        "ratings_this_week": ratings_this_week
    });
    
    Ok(MarketplaceStats {
        total_agents,
        total_downloads,
        total_ratings,
        average_rating,
        categories_count,
        active_users_count: 0, // Not tracked currently
        popular_tags,
        top_authors,
        recent_activity,
    })
}

/// Clear marketplace cache
#[tauri::command]
pub async fn marketplace_clear_cache(
    db: State<'_, MarketplaceDb>,
    cache_type: Option<String>,
) -> Result<serde_json::Value, String> {
    let conn = db.0.lock().map_err(|e| e.to_string())?;
    
    let cleared_count = if let Some(cache_type) = cache_type {
        conn.execute("DELETE FROM marketplace_cache WHERE cache_type = ?1", params![cache_type])
    } else {
        conn.execute("DELETE FROM marketplace_cache", [])
    }.map_err(|e| e.to_string())?;
    
    info!("Cleared {} cache entries", cleared_count);
    
    Ok(serde_json::json!({
        "clearedCount": cleared_count
    }))
}

// ============================================================================
// Helper Functions
// ============================================================================

enum ProcessResult {
    Added,
    Updated,
    Skipped,
}

/// Download agent content from URL
async fn download_agent_content(url: &str) -> Result<String, String> {
    let client = reqwest::Client::new();
    
    let response = client
        .get(url)
        .header("Accept", "application/json")
        .header("User-Agent", "Claudia-Marketplace/1.0")
        .send()
        .await
        .map_err(|e| format!("Failed to download agent: {}", e))?;
    
    if !response.status().is_success() {
        return Err(format!("HTTP error {}: {}", response.status(), response.status().canonical_reason().unwrap_or("Unknown")));
    }
    
    let content = response
        .text()
        .await
        .map_err(|e| format!("Failed to read response: {}", e))?;
    
    Ok(content)
}

/// Fetch GitHub agent files
async fn fetch_github_agents(config: &MarketplaceConfig) -> Result<Vec<crate::commands::agents::GitHubAgentFile>, String> {
    let client = reqwest::Client::new();
    let url = format!("https://api.github.com/repos/{}/contents/cc_agents", config.github_repo);
    
    let mut request = client
        .get(&url)
        .header("Accept", "application/vnd.github+json")
        .header("User-Agent", "Claudia-Marketplace/1.0");
    
    // Add authentication if token is provided
    if let Some(token) = &config.github_token {
        request = request.header("Authorization", format!("token {}", token));
    }
    
    let response = request
        .send()
        .await
        .map_err(|e| format!("Failed to fetch from GitHub: {}", e))?;
    
    if !response.status().is_success() {
        let status = response.status();
        let error_text = response.text().await.unwrap_or_default();
        return Err(format!("GitHub API error ({}): {}", status, error_text));
    }
    
    #[derive(Deserialize)]
    struct GitHubApiResponse {
        name: String,
        path: String,
        sha: String,
        size: i64,
        download_url: Option<String>,
        #[serde(rename = "type")]
        file_type: String,
    }
    
    let api_files: Vec<GitHubApiResponse> = response
        .json()
        .await
        .map_err(|e| format!("Failed to parse GitHub response: {}", e))?;
    
    // Filter only .claudia.json files
    let agent_files: Vec<crate::commands::agents::GitHubAgentFile> = api_files
        .into_iter()
        .filter(|f| f.name.ends_with(".claudia.json") && f.file_type == "file")
        .filter_map(|f| {
            f.download_url.map(|download_url| crate::commands::agents::GitHubAgentFile {
                name: f.name,
                path: f.path,
                download_url,
                size: f.size,
                sha: f.sha,
            })
        })
        .collect();
    
    info!("Found {} agents on GitHub", agent_files.len());
    Ok(agent_files)
}

/// Process a GitHub agent file and update marketplace database
fn process_github_agent(
    conn: &Connection,
    github_agent: &crate::commands::agents::GitHubAgentFile,
    force_refresh: bool,
) -> Result<ProcessResult, String> {
    let remote_id = extract_remote_id(&github_agent.name);
    
    // Check if agent already exists
    let existing_sha: Option<String> = conn
        .query_row(
            "SELECT sha FROM marketplace_agents WHERE remote_id = ?1",
            params![remote_id],
            |row| row.get(0),
        )
        .ok();
    
    // Skip if SHA hasn't changed (unless force refresh)
    if !force_refresh && existing_sha.as_ref() == Some(&github_agent.sha) {
        return Ok(ProcessResult::Skipped);
    }
    
    // Download and parse agent content (this is a blocking operation in the sync context)
    let content = std::thread::spawn({
        let download_url = github_agent.download_url.clone();
        move || {
            tokio::runtime::Runtime::new()
                .unwrap()
                .block_on(download_agent_content(&download_url))
        }
    })
    .join()
    .map_err(|_| "Thread panic while downloading agent")?
    .map_err(|e| format!("Failed to download {}: {}", github_agent.name, e))?;
    
    // Parse agent export
    let agent_export: serde_json::Value = serde_json::from_str(&content)
        .map_err(|e| format!("Invalid JSON in {}: {}", github_agent.name, e))?;
    
    let agent_data = agent_export
        .get("agent")
        .ok_or_else(|| format!("Missing agent data in {}", github_agent.name))?;
    
    // Extract agent fields with defaults
    let name = agent_data
        .get("name")
        .and_then(|v| v.as_str())
        .unwrap_or("Unknown Agent")
        .to_string();
    
    let icon = agent_data
        .get("icon")
        .and_then(|v| v.as_str())
        .unwrap_or("🤖")
        .to_string();
    
    let system_prompt = agent_data
        .get("system_prompt")
        .and_then(|v| v.as_str())
        .unwrap_or("")
        .to_string();
    
    let author = agent_data
        .get("author")
        .and_then(|v| v.as_str())
        .unwrap_or("Unknown")
        .to_string();
    
    let description = agent_data
        .get("description")
        .and_then(|v| v.as_str())
        .unwrap_or("")
        .to_string();
    
    let version = agent_data
        .get("version")
        .and_then(|v| v.as_str())
        .unwrap_or("1.0.0")
        .to_string();
    
    let model = agent_data
        .get("model")
        .and_then(|v| v.as_str())
        .unwrap_or("sonnet")
        .to_string();
    
    // Build marketplace agent
    let now = chrono::Utc::now().to_rfc3339();
    
    let is_update = existing_sha.is_some();
    
    if is_update {
        // Update existing agent
        conn.execute(
            "UPDATE marketplace_agents SET 
             name = ?1, icon = ?2, system_prompt = ?3, author = ?4, description = ?5, 
             version = ?6, model = ?7, download_url = ?8, sha = ?9, file_size = ?10,
             last_synced_at = ?11, updated_at = ?11
             WHERE remote_id = ?12",
            params![
                name, icon, system_prompt, author, description, version, model,
                github_agent.download_url, github_agent.sha, github_agent.size,
                now, remote_id
            ],
        )
        .map_err(|e| format!("Failed to update agent {}: {}", name, e))?;
        
        Ok(ProcessResult::Updated)
    } else {
        // Insert new agent
        conn.execute(
            "INSERT INTO marketplace_agents (
                remote_id, name, icon, system_prompt, author, description, version, model,
                download_url, sha, file_size, last_synced_at, created_at, updated_at
             ) VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8, ?9, ?10, ?11, ?12, ?12, ?12)",
            params![
                remote_id, name, icon, system_prompt, author, description, version, model,
                github_agent.download_url, github_agent.sha, github_agent.size, now
            ],
        )
        .map_err(|e| format!("Failed to insert agent {}: {}", name, e))?;
        
        Ok(ProcessResult::Added)
    }
}

/// Extract remote ID from GitHub agent filename
fn extract_remote_id(filename: &str) -> String {
    filename
        .strip_suffix(".claudia.json")
        .unwrap_or(filename)
        .to_string()
}

/// Record agent download for analytics
fn record_agent_download(
    conn: &Connection,
    agent: &MarketplaceAgent,
    source: &str,
    success: bool,
    error_message: Option<&str>,
) -> Result<(), String> {
    if let Some(agent_id) = agent.id {
        conn.execute(
            "INSERT INTO agent_downloads (marketplace_agent_id, download_source, success, error_message) VALUES (?1, ?2, ?3, ?4)",
            params![agent_id, source, success, error_message],
        ).map_err(|e| format!("Failed to record download: {}", e))?;
        
        // Update download count on success
        if success {
            conn.execute(
                "UPDATE marketplace_agents SET download_count = download_count + 1 WHERE id = ?1",
                params![agent_id],
            ).map_err(|e| format!("Failed to update download count: {}", e))?;
        }
    }
    
    Ok(())
}