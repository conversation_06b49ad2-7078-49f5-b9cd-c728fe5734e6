#!/usr/bin/env node

import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import { z } from "zod";

/**
 * Weather MCP Server - TypeScript SDK Example
 * 
 * This server demonstrates:
 * - Tools: Get current weather and forecast
 * - Resources: Weather data for specific locations
 * - Prompts: Weather analysis templates
 * 
 * Features showcased:
 * - Input validation with Zod schemas
 * - Error handling
 * - Multiple tool types
 * - Resource templates
 * - Prompt definitions
 */

// Mock weather data (in a real implementation, you'd use a weather API)
interface WeatherData {
  location: string;
  temperature: number;
  condition: string;
  humidity: number;
  windSpeed: number;
  timestamp: string;
}

const mockWeatherData: Record<string, WeatherData> = {
  "new york": {
    location: "New York, NY",
    temperature: 22,
    condition: "Partly Cloudy",
    humidity: 65,
    windSpeed: 12,
    timestamp: new Date().toISOString()
  },
  "london": {
    location: "London, UK",
    temperature: 15,
    condition: "Rainy",
    humidity: 80,
    windSpeed: 8,
    timestamp: new Date().toISOString()
  },
  "tokyo": {
    location: "Tokyo, Japan",
    temperature: 28,
    condition: "Sunny",
    humidity: 55,
    windSpeed: 5,
    timestamp: new Date().toISOString()
  }
};

// Create the MCP server
const server = new McpServer({
  name: "weather-server",
  version: "1.0.0"
});

/**
 * TOOLS - Functions that can be called by the LLM
 */

// Tool 1: Get current weather
server.registerTool(
  "get_weather",
  {
    title: "Get Current Weather",
    description: "Get the current weather for a specific location",
    inputSchema: {
      location: z.string().describe("The city name (e.g., 'New York', 'London', 'Tokyo')")
    }
  },
  async ({ location }) => {
    const normalizedLocation = location.toLowerCase().trim();
    const weather = mockWeatherData[normalizedLocation];
    
    if (!weather) {
      return {
        content: [{
          type: "text",
          text: `Weather data not available for "${location}". Available locations: ${Object.keys(mockWeatherData).join(", ")}`
        }]
      };
    }
    
    return {
      content: [{
        type: "text",
        text: `Current weather in ${weather.location}:\n` +
              `Temperature: ${weather.temperature}°C\n` +
              `Condition: ${weather.condition}\n` +
              `Humidity: ${weather.humidity}%\n` +
              `Wind Speed: ${weather.windSpeed} km/h\n` +
              `Last Updated: ${weather.timestamp}`
      }]
    };
  }
);

// Tool 2: Get weather forecast
server.registerTool(
  "get_forecast",
  {
    title: "Get Weather Forecast",
    description: "Get a 3-day weather forecast for a location",
    inputSchema: {
      location: z.string().describe("The city name"),
      days: z.number().min(1).max(7).default(3).describe("Number of days to forecast (1-7)")
    }
  },
  async ({ location, days = 3 }) => {
    const normalizedLocation = location.toLowerCase().trim();
    const baseWeather = mockWeatherData[normalizedLocation];
    
    if (!baseWeather) {
      return {
        content: [{
          type: "text",
          text: `Forecast not available for "${location}". Available locations: ${Object.keys(mockWeatherData).join(", ")}`
        }]
      };
    }
    
    let forecast = `${days}-day forecast for ${baseWeather.location}:\n\n`;
    
    for (let i = 0; i < days; i++) {
      const date = new Date();
      date.setDate(date.getDate() + i);
      const dayName = date.toLocaleDateString('en-US', { weekday: 'long' });
      
      // Generate mock forecast data with some variation
      const tempVariation = Math.floor(Math.random() * 10) - 5;
      const temp = baseWeather.temperature + tempVariation;
      const conditions = ["Sunny", "Partly Cloudy", "Cloudy", "Rainy"];
      const condition = conditions[Math.floor(Math.random() * conditions.length)];
      
      forecast += `${dayName}: ${temp}°C, ${condition}\n`;
    }
    
    return {
      content: [{
        type: "text",
        text: forecast
      }]
    };
  }
);

// Tool 3: Compare weather between cities
server.registerTool(
  "compare_weather",
  {
    title: "Compare Weather",
    description: "Compare current weather between two cities",
    inputSchema: {
      city1: z.string().describe("First city name"),
      city2: z.string().describe("Second city name")
    }
  },
  async ({ city1, city2 }) => {
    const weather1 = mockWeatherData[city1.toLowerCase().trim()];
    const weather2 = mockWeatherData[city2.toLowerCase().trim()];
    
    if (!weather1 || !weather2) {
      return {
        content: [{
          type: "text",
          text: `Cannot compare weather. Available locations: ${Object.keys(mockWeatherData).join(", ")}`
        }]
      };
    }
    
    const tempDiff = Math.abs(weather1.temperature - weather2.temperature);
    const warmerCity = weather1.temperature > weather2.temperature ? weather1.location : weather2.location;
    
    return {
      content: [{
        type: "text",
        text: `Weather Comparison:\n\n` +
              `${weather1.location}: ${weather1.temperature}°C, ${weather1.condition}\n` +
              `${weather2.location}: ${weather2.temperature}°C, ${weather2.condition}\n\n` +
              `${warmerCity} is ${tempDiff}°C warmer.`
      }]
    };
  }
);

/**
 * RESOURCES - Data that can be read by the LLM
 */

// Static resource: Available locations
server.registerResource(
  "locations",
  "weather://locations",
  {
    title: "Available Weather Locations",
    description: "List of cities with available weather data",
    mimeType: "application/json"
  },
  async () => {
    const locations = Object.values(mockWeatherData).map(weather => ({
      name: weather.location,
      key: weather.location.toLowerCase().replace(/[^a-z]/g, '')
    }));
    
    return {
      contents: [{
        uri: "weather://locations",
        mimeType: "application/json",
        text: JSON.stringify(locations, null, 2)
      }]
    };
  }
);

// Dynamic resource: Weather data for specific location
server.registerResource(
  "weather-data",
  "weather://data/{location}",
  {
    title: "Weather Data",
    description: "Current weather data for a specific location",
    mimeType: "application/json"
  },
  async (uri) => {
    // Extract location from URI
    const match = uri.href.match(/weather:\/\/data\/(.+)/);
    if (!match) {
      throw new Error("Invalid weather data URI");
    }
    
    const location = decodeURIComponent(match[1]).toLowerCase();
    const weather = mockWeatherData[location];
    
    if (!weather) {
      throw new Error(`Weather data not found for location: ${location}`);
    }
    
    return {
      contents: [{
        uri: uri.href,
        mimeType: "application/json",
        text: JSON.stringify(weather, null, 2)
      }]
    };
  }
);

/**
 * PROMPTS - Reusable prompt templates
 */

// Prompt 1: Weather analysis
server.registerPrompt(
  "weather_analysis",
  {
    title: "Weather Analysis",
    description: "Analyze weather conditions and provide recommendations",
    arguments: [
      {
        name: "location",
        description: "City name to analyze",
        required: true
      },
      {
        name: "activity",
        description: "Planned activity (e.g., 'outdoor wedding', 'hiking', 'picnic')",
        required: false
      }
    ]
  },
  async (args) => {
    const location = args.location as string;
    const activity = args.activity as string || "general activities";
    
    return {
      messages: [
        {
          role: "user",
          content: {
            type: "text",
            text: `Please analyze the current weather conditions in ${location} and provide recommendations for ${activity}. ` +
                  `Consider factors like temperature, precipitation, wind, and overall comfort. ` +
                  `Include specific advice about clothing, timing, and any precautions to take.`
          }
        }
      ]
    };
  }
);

// Prompt 2: Travel weather planning
server.registerPrompt(
  "travel_weather",
  {
    title: "Travel Weather Planning",
    description: "Get weather-based travel recommendations",
    arguments: [
      {
        name: "destinations",
        description: "Comma-separated list of destination cities",
        required: true
      },
      {
        name: "travel_dates",
        description: "Travel dates or duration",
        required: false
      }
    ]
  },
  async (args) => {
    const destinations = args.destinations as string;
    const travelDates = args.travel_dates as string || "upcoming week";
    
    return {
      messages: [
        {
          role: "user",
          content: {
            type: "text",
            text: `I'm planning to travel to ${destinations} during ${travelDates}. ` +
                  `Please compare the weather conditions across these destinations and provide recommendations for: ` +
                  `1) Best destination based on weather, 2) What to pack, 3) Activities suited for the weather conditions, ` +
                  `4) Any weather-related travel considerations.`
          }
        }
      ]
    };
  }
);

/**
 * SERVER STARTUP
 */

async function main() {
  console.error("Weather MCP Server starting...");
  
  try {
    // Connect to stdio transport
    const transport = new StdioServerTransport();
    await server.connect(transport);
    
    console.error("Weather MCP Server connected and ready!");
    console.error("Available tools: get_weather, get_forecast, compare_weather");
    console.error("Available resources: weather://locations, weather://data/{location}");
    console.error("Available prompts: weather_analysis, travel_weather");
  } catch (error) {
    console.error("Failed to start server:", error);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
  console.error("Shutting down Weather MCP Server...");
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.error("Shutting down Weather MCP Server...");
  process.exit(0);
});

// Start the server
main().catch((error) => {
  console.error("Unhandled error:", error);
  process.exit(1);
});