# Weather MCP Server - TypeScript SDK Example

A comprehensive example of an MCP (Model Context Protocol) server built with the TypeScript SDK. This server demonstrates all major MCP features including tools, resources, and prompts.

## Features

### 🛠️ Tools
- **get_weather**: Get current weather for a specific location
- **get_forecast**: Get multi-day weather forecast
- **compare_weather**: Compare weather between two cities

### 📊 Resources
- **weather://locations**: List of available weather locations
- **weather://data/{location}**: Current weather data for specific locations

### 💬 Prompts
- **weather_analysis**: Analyze weather conditions with activity recommendations
- **travel_weather**: Travel planning based on weather conditions

## Installation

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Build the server:**
   ```bash
   npm run build
   ```

3. **Test the server:**
   ```bash
   npm start
   ```

## Development

- **Watch mode:** `npm run dev`
- **Inspector:** `npm run inspector` (opens MCP Inspector for testing)

## Adding to Your MCP Client

### Configuration for <PERSON>

Add to your `claude_desktop_config.json`:

```json
{
  "mcpServers": {
    "weather-server": {
      "command": "node",
      "args": ["/path/to/mcp-typescript-weather-server/build/index.js"]
    }
  }
}
```

### Configuration for Trae AI

1. Open MCP Manager in Trae AI
2. Click "Add Server"
3. Choose "stdio" transport
4. Configure:
   - **Name:** `weather-server`
   - **Command:** `node`
   - **Arguments:** `/path/to/mcp-typescript-weather-server/build/index.js`

## Usage Examples

### Using Tools

```typescript
// Get current weather
const weather = await callTool('get_weather', { location: 'New York' });

// Get 5-day forecast
const forecast = await callTool('get_forecast', { 
  location: 'London', 
  days: 5 
});

// Compare weather between cities
const comparison = await callTool('compare_weather', {
  city1: 'Tokyo',
  city2: 'New York'
});
```

### Using Resources

```typescript
// Get available locations
const locations = await readResource('weather://locations');

// Get weather data for specific location
const nyWeather = await readResource('weather://data/new york');
```

### Using Prompts

```typescript
// Weather analysis for outdoor activity
const analysis = await getPrompt('weather_analysis', {
  location: 'London',
  activity: 'outdoor wedding'
});

// Travel weather planning
const travelPlan = await getPrompt('travel_weather', {
  destinations: 'Paris, Rome, Barcelona',
  travel_dates: 'next month'
});
```

## Available Locations

The server includes mock data for:
- New York, NY
- London, UK
- Tokyo, Japan

## TypeScript SDK Features Demonstrated

### 1. **Input Validation with Zod**
```typescript
inputSchema: {
  location: z.string().describe("The city name"),
  days: z.number().min(1).max(7).default(3)
}
```

### 2. **Error Handling**
```typescript
if (!weather) {
  return {
    content: [{
      type: "text",
      text: `Weather data not available for "${location}"`
    }]
  };
}
```

### 3. **Resource Templates**
```typescript
server.registerResource(
  "weather-data",
  "weather://data/{location}",
  // ... configuration
);
```

### 4. **Prompt Arguments**
```typescript
arguments: [
  {
    name: "location",
    description: "City name to analyze",
    required: true
  }
]
```

## Extending the Server

### Adding New Tools

```typescript
server.registerTool(
  "your_tool_name",
  {
    title: "Your Tool Title",
    description: "Tool description",
    inputSchema: {
      param: z.string().describe("Parameter description")
    }
  },
  async ({ param }) => {
    // Tool implementation
    return {
      content: [{
        type: "text",
        text: "Tool response"
      }]
    };
  }
);
```

### Adding New Resources

```typescript
server.registerResource(
  "resource-name",
  "your://resource/uri",
  {
    title: "Resource Title",
    description: "Resource description",
    mimeType: "application/json"
  },
  async (uri) => {
    return {
      contents: [{
        uri: uri.href,
        mimeType: "application/json",
        text: JSON.stringify(data, null, 2)
      }]
    };
  }
);
```

## Real-World Integration

To make this a production-ready weather server:

1. **Replace mock data** with real weather API calls (OpenWeatherMap, WeatherAPI, etc.)
2. **Add authentication** for weather API services
3. **Implement caching** to reduce API calls
4. **Add more locations** and weather parameters
5. **Include weather alerts** and severe weather warnings

## Dependencies

- `@modelcontextprotocol/sdk`: Official MCP TypeScript SDK
- `zod`: Runtime type validation and parsing
- `typescript`: TypeScript compiler
- `@types/node`: Node.js type definitions

## License

MIT License - Feel free to use this as a starting point for your own MCP servers!