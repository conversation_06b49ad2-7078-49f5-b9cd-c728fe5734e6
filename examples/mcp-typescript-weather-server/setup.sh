#!/bin/bash

# Setup script for Weather MCP Server
# This script installs Node.js (if needed) and sets up the TypeScript MCP server

echo "🌤️  Setting up Weather MCP Server..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "📦 Node.js not found. Installing Node.js..."
    
    # Check if Homebrew is available (macOS)
    if command -v brew &> /dev/null; then
        echo "🍺 Installing Node.js via Homebrew..."
        brew install node
    else
        echo "❌ Homebrew not found. Please install Node.js manually:"
        echo "   Visit: https://nodejs.org/"
        echo "   Or install Homebrew first: /bin/bash -c \"\$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\""
        exit 1
    fi
else
    echo "✅ Node.js found: $(node --version)"
fi

# Check if npm is available
if ! command -v npm &> /dev/null; then
    echo "❌ npm not found. Please reinstall Node.js."
    exit 1
else
    echo "✅ npm found: $(npm --version)"
fi

# Install dependencies
echo "📦 Installing dependencies..."
npm install

if [ $? -ne 0 ]; then
    echo "❌ Failed to install dependencies"
    exit 1
fi

# Build the TypeScript server
echo "🔨 Building TypeScript server..."
npm run build

if [ $? -ne 0 ]; then
    echo "❌ Failed to build server"
    exit 1
fi

echo "✅ Weather MCP Server setup complete!"
echo ""
echo "🚀 Next steps:"
echo "1. Test the server: npm start"
echo "2. Use MCP Inspector: npm run inspector"
echo "3. Add to your MCP client configuration"
echo ""
echo "📍 Server location: $(pwd)/build/index.js"
echo "🔧 Configuration command: node $(pwd)/build/index.js"