{"name": "mcp-weather-server", "version": "1.0.0", "description": "A TypeScript MCP server that provides weather information", "type": "module", "bin": {"mcp-weather-server": "./build/index.js"}, "files": ["build"], "scripts": {"build": "tsc && chmod +x build/index.js", "dev": "tsc --watch", "start": "node build/index.js", "inspector": "npx @modelcontextprotocol/inspector build/index.js"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.16.0", "zod": "^3.22.4"}, "devDependencies": {"@types/node": "^20.11.24", "typescript": "^5.3.3"}}