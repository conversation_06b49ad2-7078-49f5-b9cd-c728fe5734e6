<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI News Widget Demo</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 32px;
        }
        .title {
            font-size: 2.5rem;
            font-weight: 700;
            color: #1a1a1a;
            margin-bottom: 8px;
        }
        .subtitle {
            font-size: 1.25rem;
            color: #666;
            margin-bottom: 16px;
        }
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
            margin-bottom: 32px;
        }
        .feature-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            border: 1px solid #e9ecef;
        }
        .feature-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #2563eb;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .feature-description {
            color: #666;
            font-size: 0.95rem;
        }
        .sources {
            background: #e7f3ff;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 24px;
        }
        .sources h3 {
            color: #1e40af;
            margin-bottom: 16px;
        }
        .source-list {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        .source-tag {
            background: #3b82f6;
            color: white;
            padding: 4px 12px;
            border-radius: 16px;
            font-size: 0.85rem;
            font-weight: 500;
        }
        .implementation {
            background: #f0fdf4;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 24px;
        }
        .code-block {
            background: #1f2937;
            color: #e5e7eb;
            border-radius: 8px;
            padding: 16px;
            overflow-x: auto;
            font-family: 'Monaco', 'Consolas', monospace;
            font-size: 0.9rem;
            line-height: 1.5;
        }
        .api-features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 16px;
            margin-bottom: 24px;
        }
        .api-feature {
            background: #fef3c7;
            border-radius: 6px;
            padding: 12px;
            font-size: 0.9rem;
        }
        .api-feature strong {
            color: #92400e;
        }
        .footer {
            text-align: center;
            padding-top: 24px;
            border-top: 1px solid #e9ecef;
            color: #666;
        }
        .emoji {
            font-size: 1.5rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🧠 AI News Intelligence Widget</h1>
            <p class="subtitle">Comprehensive AI/ML news aggregation for developers</p>
            <p>Stay updated with the latest AI research, breakthroughs, and industry developments</p>
        </div>

        <div class="sources">
            <h3>📡 Integrated News Sources</h3>
            <div class="source-list">
                <span class="source-tag">Hacker News</span>
                <span class="source-tag">arXiv Papers</span>
                <span class="source-tag">OpenAI Blog</span>
                <span class="source-tag">Anthropic News</span>
                <span class="source-tag">MIT Tech Review</span>
                <span class="source-tag">AI Weekly</span>
                <span class="source-tag">Hugging Face</span>
            </div>
        </div>

        <div class="features">
            <div class="feature-card">
                <div class="feature-title">
                    <span class="emoji">🎯</span>
                    Smart Content Filtering
                </div>
                <div class="feature-description">
                    AI-powered relevance scoring filters out noise and focuses on high-quality AI/ML content. Intelligent categorization into AI, ML, NLP, Computer Vision, and Robotics.
                </div>
            </div>

            <div class="feature-card">
                <div class="feature-title">
                    <span class="emoji">📊</span>
                    Multi-Source Aggregation
                </div>
                <div class="feature-description">
                    Combines news from Hacker News, arXiv research papers, industry blogs, and RSS feeds. Deduplicates content and ranks by authority and relevance.
                </div>
            </div>

            <div class="feature-card">
                <div class="feature-title">
                    <span class="emoji">🔖</span>
                    Bookmark & Reading List
                </div>
                <div class="feature-description">
                    Save interesting articles for later reading. Persistent bookmarks with easy filtering to view only saved content.
                </div>
            </div>

            <div class="feature-card">
                <div class="feature-title">
                    <span class="emoji">🔍</span>
                    Advanced Search & Filtering
                </div>
                <div class="feature-description">
                    Search by keywords, filter by categories, time ranges, and sources. Custom keyword alerts for tracking specific technologies or companies.
                </div>
            </div>

            <div class="feature-card">
                <div class="feature-title">
                    <span class="emoji">📱</span>
                    Responsive Design
                </div>
                <div class="feature-description">
                    Mobile-optimized interface with compact and detailed view modes. Adapts to different screen sizes and widget dimensions.
                </div>
            </div>

            <div class="feature-card">
                <div class="feature-title">
                    <span class="emoji">⚡</span>
                    Intelligent Caching
                </div>
                <div class="feature-description">
                    45-minute cache for news content, rate limiting, and fallback mechanisms. Optimized performance with minimal API requests.
                </div>
            </div>
        </div>

        <div class="implementation">
            <h3 style="color: #16a34a; margin-bottom: 16px;">💻 Implementation Details</h3>
            <div class="code-block">
// Key Features Implemented:

1. Multi-Source Data Fetching:
   - Hacker News Algolia API for AI-related stories
   - arXiv API for latest AI/ML research papers
   - RSS feeds from OpenAI, Anthropic, and other AI blogs
   - Intelligent content scoring and relevance filtering

2. Smart Content Processing:
   - AI keyword matching with weighted scoring
   - Automatic categorization (AI, ML, NLP, CV, Robotics)
   - Duplicate detection and removal
   - Tag extraction from content

3. User Experience Features:
   - Real-time search and filtering
   - Bookmark management with persistence
   - Configurable settings panel
   - Time range filtering (day, week, month)
   - Source-specific filtering

4. Technical Implementation:
   - React hooks for state management
   - Tauri commands for HTTP requests
   - TypeScript for type safety
   - Framer Motion for animations
   - Responsive Tailwind CSS styling
            </div>
        </div>

        <div class="api-features">
            <div class="api-feature">
                <strong>Rate Limiting:</strong> Intelligent request throttling prevents API quota exhaustion
            </div>
            <div class="api-feature">
                <strong>Error Handling:</strong> Graceful fallbacks when APIs fail or are unavailable
            </div>
            <div class="api-feature">
                <strong>Content Scoring:</strong> Relevance algorithm ranks articles by AI/ML importance
            </div>
            <div class="api-feature">
                <strong>Cache Strategy:</strong> 45-minute refresh cycle optimizes performance
            </div>
            <div class="api-feature">
                <strong>Mobile Support:</strong> Responsive design works on all screen sizes
            </div>
            <div class="api-feature">
                <strong>Accessibility:</strong> Screen reader support and keyboard navigation
            </div>
        </div>

        <div style="background: #ede9fe; border-radius: 8px; padding: 20px; margin-bottom: 24px;">
            <h3 style="color: #7c3aed; margin-bottom: 16px;">🎛️ Configuration Options</h3>
            <ul style="margin: 0; padding-left: 20px; color: #374151;">
                <li><strong>News Sources:</strong> Choose from Hacker News, arXiv, OpenAI Blog, Anthropic, AI Weekly</li>
                <li><strong>Categories:</strong> Filter by AI, ML, NLP, Computer Vision, Robotics, or General</li>
                <li><strong>Time Range:</strong> Show content from past day, week, or month</li>
                <li><strong>Article Limit:</strong> Configure number of articles to display (5-50)</li>
                <li><strong>Keywords:</strong> Custom keyword tracking for specific technologies</li>
                <li><strong>Auto-refresh:</strong> Configurable refresh intervals (15-120 minutes)</li>
            </ul>
        </div>

        <div style="background: #fef2f2; border-radius: 8px; padding: 20px; border: 1px solid #fecaca;">
            <h3 style="color: #dc2626; margin-bottom: 16px;">🔧 Technical Architecture</h3>
            <p style="margin: 0; color: #374151;">
                The AI News Widget uses a modular architecture with the <code>useAINews</code> hook handling all data operations, 
                the <code>AINewsWidget</code> component providing the UI, and Tauri commands managing secure HTTP requests. 
                Content is intelligently filtered using keyword matching algorithms and cached for optimal performance.
            </p>
        </div>

        <div class="footer">
            <p>Built with React, TypeScript, Tauri, and love for AI development 🚀</p>
            <p style="font-size: 0.9rem; margin-top: 8px;">
                Part of the Claudia Hub Dashboard • Keeping developers informed about AI advances
            </p>
        </div>
    </div>
</body>
</html>