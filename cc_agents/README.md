# 🤖 Claudia CC Agents

<div align="center">
  <p>
    <strong>Pre-built AI agents for <PERSON> powered by Claude Code</strong>
  </p>
  <p>
    <a href="#available-agents">Browse Agents</a> •
    <a href="#importing-agents">Import Guide</a> •
    <a href="#exporting-agents">Export Guide</a> •
    <a href="#contributing">Contribute</a>
  </p>
</div>

---

## 📦 Available Agents

| Agent | Model | Description | Default Task |
|-------|-------|-------------|--------------|
| **🎯 Git Commit Bot**<br/>🤖 `bot` | <img src="https://img.shields.io/badge/Sonnet-blue?style=flat-square" alt="Sonnet"> | **Enhanced Git workflow automation with intelligent commit messages**<br/><br/>Analyzes Git repository changes, generates detailed commit messages following Conventional Commits specification, and safely pushes changes with comprehensive error handling and tool validation. | "Push all changes." |
| **🛡️ Security Scanner**<br/>🛡️ `shield` | <img src="https://img.shields.io/badge/Opus-purple?style=flat-square" alt="Opus"> | **Advanced AI-powered Static Application Security Testing (SAST)**<br/><br/>Performs comprehensive security audits with enhanced error handling and tool validation. Features: codebase intelligence gathering, threat modeling (STRIDE), vulnerability scanning (OWASP Top 10, CWE), exploit validation, remediation design, and professional report generation. | "Review the codebase for security issues." |
| **🧪 Unit Tests Bot**<br/>💻 `code` | <img src="https://img.shields.io/badge/Sonnet-blue?style=flat-square" alt="Sonnet"> | **Optimized automated unit test generation for any codebase**<br/><br/>Analyzes codebase and generates comprehensive unit tests with enhanced framework detection and error handling. Features: code structure analysis, test planning, style-matching test generation, execution verification, and coverage optimization (>80% overall, 100% critical paths). | "Generate unit tests for this codebase." |
| **🌐 API Testing Agent**<br/>🌐 `globe` | <img src="https://img.shields.io/badge/Sonnet-blue?style=flat-square" alt="Sonnet"> | **Comprehensive REST and GraphQL API testing and validation**<br/><br/>Systematically tests API endpoints with functional testing, schema validation, security testing, performance analysis, and documentation generation. Supports OpenAPI/Swagger specifications and generates detailed test reports. | "Test and validate API endpoints comprehensively." |
| **👁️ Code Review Agent**<br/>💻 `code` | <img src="https://img.shields.io/badge/Sonnet-blue?style=flat-square" alt="Sonnet"> | **Automated comprehensive code review and quality assessment**<br/><br/>Performs systematic code reviews across multiple dimensions: functionality, security, performance, style, and maintainability. Provides actionable feedback with specific improvement suggestions and generates detailed review reports. | "Perform comprehensive code review and quality assessment." |
| **⚙️ Environment Setup Agent**<br/>🖥️ `terminal` | <img src="https://img.shields.io/badge/Haiku-green?style=flat-square" alt="Haiku"> | **Automated development environment configuration and setup**<br/><br/>Configures development environments across platforms, installs tools and dependencies, sets up project scaffolding, and creates reproducible development setups with comprehensive cross-platform support. | "Set up and configure development environment." |
| **♿ Accessibility Compliance**<br/>♿ `accessibility` | <img src="https://img.shields.io/badge/Sonnet-blue?style=flat-square" alt="Sonnet"> | **Web accessibility compliance analysis and improvement**<br/><br/>Analyzes web applications for WCAG 2.1 AA compliance, identifies accessibility barriers, and provides detailed remediation guidance with code examples for creating inclusive digital experiences. | "Analyze and improve accessibility compliance in web applications." |
| **🔄 CI/CD Pipeline**<br/>🌿 `git-branch` | <img src="https://img.shields.io/badge/Sonnet-blue?style=flat-square" alt="Sonnet"> | **Automated CI/CD pipeline design and implementation**<br/><br/>Designs and implements continuous integration and delivery pipelines with automated testing, security scanning, and deployment strategies across multiple platforms and cloud providers. | "Design and implement CI/CD pipelines for automated software delivery." |
| **📄 Code Documentation Bot**<br/>📄 `file-text` | <img src="https://img.shields.io/badge/Opus-purple?style=flat-square" alt="Opus"> | **Comprehensive codebase documentation generation**<br/><br/>Analyzes codebases and generates complete documentation including API docs, architecture diagrams, usage guides, and developer documentation with visual representations and examples. | "Generate comprehensive documentation for this codebase." |
| **🔧 Code Refactoring Bot**<br/>🔧 `tool` | <img src="https://img.shields.io/badge/Opus-purple?style=flat-square" alt="Opus"> | **Intelligent code refactoring and quality improvement**<br/><br/>Identifies code smells, anti-patterns, and refactoring opportunities. Implements improvements following SOLID principles while maintaining functionality and generating detailed refactoring reports. | "Analyze and refactor code to improve quality and maintainability." |
| **🗄️ Database Optimizer**<br/>🗄️ `database` | <img src="https://img.shields.io/badge/Sonnet-blue?style=flat-square" alt="Sonnet"> | **Database performance analysis and optimization**<br/><br/>Analyzes database performance, optimizes queries, improves schema design, configures indexes, and implements database-specific best practices for maximum efficiency and reliability. | "Analyze and optimize database performance." |
| **📦 Dependency Manager**<br/>📦 `package` | <img src="https://img.shields.io/badge/Sonnet-blue?style=flat-square" alt="Sonnet"> | **Project dependency analysis and management**<br/><br/>Analyzes project dependencies, identifies security vulnerabilities, manages updates safely, ensures compatibility, and maintains clean dependency trees across multiple package managers. | "Analyze and update project dependencies." |
| **🌍 Localization Agent**<br/>🌐 `globe` | <img src="https://img.shields.io/badge/Sonnet-blue?style=flat-square" alt="Sonnet"> | **Application internationalization and localization**<br/><br/>Implements comprehensive localization workflows, manages translations, handles cultural adaptations, and ensures applications work seamlessly across different languages and regions. | "Analyze and implement localization for applications across multiple languages and regions." |
| **⚡ Performance Optimizer**<br/>⚡ `zap` | <img src="https://img.shields.io/badge/Opus-purple?style=flat-square" alt="Opus"> | **Code performance analysis and optimization**<br/><br/>Identifies performance bottlenecks, implements optimizations, benchmarks improvements, and provides detailed performance analysis with measurable results and optimization strategies. | "Analyze and optimize code performance." |
| **🔄 UI Workflow Analyzer**<br/>🔄 `workflow` | <img src="https://img.shields.io/badge/Sonnet-blue?style=flat-square" alt="Sonnet"> | **End-to-end user interface workflow analysis**<br/><br/>Analyzes application workflows through automated UI interactions, documents complete user journeys, identifies bottlenecks, and provides comprehensive UX optimization recommendations. | "Analyze application workflows end-to-end through user interface interactions and document complete user journeys." |
| **🌐 Smart Research & Recommendation Agent**<br/>🌐 `globe` | <img src="https://img.shields.io/badge/Opus-purple?style=flat-square" alt="Opus"> | **Intelligent research and recommendation system with web search**<br/><br/>Leverages web search and documentation analysis to provide actionable recommendations. Analyzes project context, searches for best practices, fetches official documentation, and synthesizes information into prioritized recommendations with implementation guidance. | "Research best practices and provide smart recommendations for the current project." |
| **📄 Documentation Intelligence Agent**<br/>📄 `file-text` | <img src="https://img.shields.io/badge/Sonnet-blue?style=flat-square" alt="Sonnet"> | **Advanced documentation fetching and analysis**<br/><br/>Specializes in fetching, parsing, and synthesizing technical documentation from multiple sources. Extracts actionable insights from API docs, framework guides, and technical specifications to provide precise, up-to-date information with code examples. | "Fetch and analyze documentation to provide comprehensive technical insights." |
| **🔄 Agent Orchestrator**<br/>🔄 `workflow` | <img src="https://img.shields.io/badge/Opus-purple?style=flat-square" alt="Opus"> | **Meta-agent for coordinating multi-agent workflows**<br/><br/>Orchestrates collaboration between multiple specialized agents to solve complex problems. Analyzes tasks, delegates to appropriate agents, manages execution pipelines, and synthesizes results into cohesive solutions with optimal efficiency. | "Orchestrate multiple agents to solve complex problems efficiently." |

### Available Icons

Choose from these icon options when creating agents:
- `bot` - 🤖 General purpose
- `shield` - 🛡️ Security related
- `code` - 💻 Development
- `terminal` - 🖥️ System/CLI
- `database` - 🗄️ Data operations
- `globe` - 🌐 Network/Web
- `file-text` - 📄 Documentation
- `git-branch` - 🌿 Version control
- `accessibility` - ♿ Accessibility
- `tool` - 🔧 Refactoring/Tools
- `package` - 📦 Dependencies
- `zap` - ⚡ Performance
- `workflow` - 🔄 Workflows

---

## 📥 Importing Agents

### Method 1: Import from GitHub (Recommended)

1. In Claudia, navigate to **CC Agents**
2. Click the **Import** dropdown button
3. Select **From GitHub**
4. Browse available agents from the official repository
5. Preview agent details and click **Import Agent**

### Method 2: Import from Local File

1. Download a `.claudia.json` file from this repository
2. In Claudia, navigate to **CC Agents**
3. Click the **Import** dropdown button
4. Select **From File**
5. Choose the downloaded `.claudia.json` file

## 📤 Exporting Agents

### Export Your Custom Agents

1. In Claudia, navigate to **CC Agents**
2. Find your agent in the grid
3. Click the **Export** button
4. Choose where to save the `.claudia.json` file

### Agent File Format

All agents are stored in `.claudia.json` format with the following structure:

```json
{
  "version": 1,
  "exported_at": "2025-01-23T14:29:58.156063+00:00",
  "agent": {
    "name": "Your Agent Name",
    "icon": "bot",
    "model": "opus|sonnet|haiku",
    "system_prompt": "Your agent's instructions...",
    "default_task": "Default task description"
  }
}
```

## 🔧 Technical Implementation

### How Import/Export Works

The agent import/export system is built on a robust architecture:

#### Backend (Rust/Tauri)
- **Storage**: SQLite database stores agent configurations
- **Export**: Serializes agent data to JSON with version control
- **Import**: Validates and deduplicates agents on import
- **GitHub Integration**: Fetches agents via GitHub API

#### Frontend (React/TypeScript)
- **UI Components**: 
  - `CCAgents.tsx` - Main agent management interface
  - `GitHubAgentBrowser.tsx` - GitHub repository browser
  - `CreateAgent.tsx` - Agent creation/editing form
- **File Operations**: Native file dialogs for import/export
- **Real-time Updates**: Live agent status and execution monitoring

### Key Features

1. **Version Control**: Each agent export includes version metadata
2. **Duplicate Prevention**: Automatic naming conflict resolution
3. **Model Selection**: Choose between Opus, Sonnet, and Haiku models
4. **GitHub Integration**: Direct import from the official repository

## 🤝 Contributing

We welcome agent contributions! Here's how to add your agent:

### 1. Create Your Agent
Design and test your agent in Claudia with a clear, focused purpose.

### 2. Export Your Agent
Export your agent to a `.claudia.json` file with a descriptive name.

### 3. Submit a Pull Request
1. Fork this repository
2. Add your `.claudia.json` file to the `cc_agents` directory
3. Update this README with your agent's details
4. Submit a PR with a description of what your agent does

### Agent Guidelines

- **Single Purpose**: Each agent should excel at one specific task
- **Clear Documentation**: Write comprehensive system prompts
- **Model Choice**: Use Haiku for simple tasks, Sonnet for general purpose, Opus for complex reasoning
- **Naming**: Use descriptive names that clearly indicate the agent's function

## 📜 License

These agents are provided under the same license as the Claudia project. See the main LICENSE file for details.

---

<div align="center">
  <strong>Built with ❤️ by the Claudia community</strong>
</div> 
