<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multi-Claude Ensemble Sessions Demo</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        .pulse-animation {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        .typing-animation {
            border-right: 2px solid #3b82f6;
            animation: blink 1s infinite;
        }
        @keyframes blink {
            0%, 50% { border-color: #3b82f6; }
            51%, 100% { border-color: transparent; }
        }
    </style>
</head>
<body class="bg-gray-50">
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect } = React;

        // Mock data for demonstration
        const CLAUDE_PERSONALITIES = {
            architect: {
                name: "Architect Claude",
                role: "System Design & Architecture",
                color: "blue",
                icon: "🏗️",
                description: "Focuses on system design and architecture decisions"
            },
            reviewer: {
                name: "Reviewer Claude",
                role: "Code Quality & Security",
                color: "red",
                icon: "🛡️",
                description: "Specializes in code quality and security analysis"
            },
            optimizer: {
                name: "Optimizer Claude",
                role: "Performance & Efficiency",
                color: "yellow",
                icon: "⚡",
                description: "Concentrates on performance and efficiency improvements"
            },
            teacher: {
                name: "Teacher Claude",
                role: "Education & Mentorship",
                color: "green",
                icon: "🎓",
                description: "Provides educational explanations and mentorship"
            }
        };

        const DEMO_CONVERSATIONS = [
            {
                id: 1,
                personality: "architect",
                message: "I'll start by analyzing the system requirements. We need a scalable microservices architecture that can handle high traffic loads.",
                timestamp: "10:30 AM"
            },
            {
                id: 2,
                personality: "reviewer",
                message: "From a security perspective, we should implement OAuth 2.0 with JWT tokens and ensure all API endpoints are properly authenticated.",
                timestamp: "10:32 AM"
            },
            {
                id: 3,
                personality: "optimizer",
                message: "For performance, I recommend implementing Redis caching for frequently accessed data and using database connection pooling.",
                timestamp: "10:34 AM"
            },
            {
                id: 4,
                personality: "teacher",
                message: "Let me explain why microservices are beneficial here: they allow independent scaling, technology diversity, and fault isolation.",
                timestamp: "10:36 AM"
            }
        ];

        function PersonalityCard({ personality, data, isActive, onClick }) {
            const colorClasses = {
                blue: "border-blue-200 bg-blue-50 text-blue-800",
                red: "border-red-200 bg-red-50 text-red-800",
                yellow: "border-yellow-200 bg-yellow-50 text-yellow-800",
                green: "border-green-200 bg-green-50 text-green-800"
            };

            return (
                <div 
                    className={`p-4 rounded-lg border-2 cursor-pointer card-hover ${
                        isActive ? colorClasses[data.color] : 'border-gray-200 bg-white'
                    } ${isActive ? 'ring-2 ring-offset-2 ring-' + data.color + '-400' : ''}`}
                    onClick={() => onClick(personality)}
                >
                    <div className="flex items-center gap-3">
                        <div className="text-2xl">{data.icon}</div>
                        <div className="flex-1">
                            <h3 className="font-semibold text-sm">{data.name}</h3>
                            <p className="text-xs text-gray-600">{data.role}</p>
                        </div>
                        {isActive && (
                            <div className="w-3 h-3 bg-green-400 rounded-full pulse-animation"></div>
                        )}
                    </div>
                    <p className="text-xs mt-2 text-gray-700">{data.description}</p>
                </div>
            );
        }

        function ConversationMessage({ conversation, personality }) {
            const data = CLAUDE_PERSONALITIES[conversation.personality];
            
            return (
                <div className="flex gap-3 p-4 bg-white rounded-lg shadow-sm border">
                    <div className="text-xl">{data.icon}</div>
                    <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                            <span className="font-medium text-sm">{data.name}</span>
                            <span className="text-xs text-gray-500">{conversation.timestamp}</span>
                        </div>
                        <p className="text-sm text-gray-700">{conversation.message}</p>
                    </div>
                </div>
            );
        }

        function EnsembleDemo() {
            const [activePersonality, setActivePersonality] = useState(null);
            const [currentStep, setCurrentStep] = useState(0);
            const [isRunning, setIsRunning] = useState(false);
            const [visibleConversations, setVisibleConversations] = useState([]);
            const [typingPersonality, setTypingPersonality] = useState(null);

            const steps = [
                { phase: "Analysis", description: "Architect Claude analyzes the problem" },
                { phase: "Security Review", description: "Reviewer Claude evaluates security aspects" },
                { phase: "Optimization", description: "Optimizer Claude suggests performance improvements" },
                { phase: "Education", description: "Teacher Claude explains the concepts" },
                { phase: "Synthesis", description: "All Claudes collaborate on final solution" }
            ];

            useEffect(() => {
                if (isRunning && currentStep < DEMO_CONVERSATIONS.length) {
                    const conversation = DEMO_CONVERSATIONS[currentStep];
                    setTypingPersonality(conversation.personality);
                    setActivePersonality(conversation.personality);
                    
                    const timer = setTimeout(() => {
                        setVisibleConversations(prev => [...prev, conversation]);
                        setTypingPersonality(null);
                        setCurrentStep(prev => prev + 1);
                    }, 2000);

                    return () => clearTimeout(timer);
                }
            }, [isRunning, currentStep]);

            const startDemo = () => {
                setIsRunning(true);
                setCurrentStep(0);
                setVisibleConversations([]);
                setActivePersonality(null);
            };

            const resetDemo = () => {
                setIsRunning(false);
                setCurrentStep(0);
                setVisibleConversations([]);
                setActivePersonality(null);
                setTypingPersonality(null);
            };

            return (
                <div className="min-h-screen bg-gray-50">
                    {/* Header */}
                    <div className="gradient-bg text-white py-8">
                        <div className="max-w-6xl mx-auto px-6">
                            <h1 className="text-4xl font-bold mb-4">Multi-Claude Ensemble Sessions</h1>
                            <p className="text-xl opacity-90">
                                Experience collaborative AI problem-solving with multiple Claude personalities
                            </p>
                        </div>
                    </div>

                    <div className="max-w-6xl mx-auto px-6 py-8">
                        {/* Demo Controls */}
                        <div className="bg-white rounded-lg shadow-sm border p-6 mb-8">
                            <div className="flex items-center justify-between mb-4">
                                <h2 className="text-2xl font-semibold">Demo: E-commerce Platform Architecture</h2>
                                <div className="flex gap-3">
                                    <button 
                                        onClick={startDemo}
                                        disabled={isRunning}
                                        className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                                    >
                                        {isRunning ? 'Running...' : 'Start Demo'}
                                    </button>
                                    <button 
                                        onClick={resetDemo}
                                        className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
                                    >
                                        Reset
                                    </button>
                                </div>
                            </div>
                            
                            {/* Progress Steps */}
                            <div className="flex items-center gap-4 overflow-x-auto">
                                {steps.map((step, index) => (
                                    <div key={index} className="flex items-center gap-2 min-w-max">
                                        <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                                            index < currentStep ? 'bg-green-500 text-white' :
                                            index === currentStep && isRunning ? 'bg-blue-500 text-white pulse-animation' :
                                            'bg-gray-200 text-gray-600'
                                        }`}>
                                            {index + 1}
                                        </div>
                                        <div className="text-sm">
                                            <div className="font-medium">{step.phase}</div>
                                            <div className="text-gray-600 text-xs">{step.description}</div>
                                        </div>
                                        {index < steps.length - 1 && (
                                            <div className="w-8 h-px bg-gray-300 mx-2"></div>
                                        )}
                                    </div>
                                ))}
                            </div>
                        </div>

                        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                            {/* Claude Personalities */}
                            <div className="lg:col-span-1">
                                <h3 className="text-xl font-semibold mb-4">Claude Personalities</h3>
                                <div className="space-y-4">
                                    {Object.entries(CLAUDE_PERSONALITIES).map(([key, data]) => (
                                        <PersonalityCard
                                            key={key}
                                            personality={key}
                                            data={data}
                                            isActive={activePersonality === key}
                                            onClick={setActivePersonality}
                                        />
                                    ))}
                                </div>
                            </div>

                            {/* Conversation */}
                            <div className="lg:col-span-2">
                                <h3 className="text-xl font-semibold mb-4">Ensemble Conversation</h3>
                                <div className="bg-gray-100 rounded-lg p-4 min-h-96 max-h-96 overflow-y-auto">
                                    {visibleConversations.length === 0 && !isRunning && (
                                        <div className="flex items-center justify-center h-full text-gray-500">
                                            <div className="text-center">
                                                <div className="text-4xl mb-4">💬</div>
                                                <p>Click "Start Demo" to see the ensemble in action</p>
                                            </div>
                                        </div>
                                    )}
                                    
                                    <div className="space-y-4">
                                        {visibleConversations.map((conversation) => (
                                            <ConversationMessage
                                                key={conversation.id}
                                                conversation={conversation}
                                                personality={CLAUDE_PERSONALITIES[conversation.personality]}
                                            />
                                        ))}
                                        
                                        {/* Typing indicator */}
                                        {typingPersonality && (
                                            <div className="flex gap-3 p-4 bg-white rounded-lg shadow-sm border">
                                                <div className="text-xl">{CLAUDE_PERSONALITIES[typingPersonality].icon}</div>
                                                <div className="flex-1">
                                                    <div className="flex items-center gap-2 mb-1">
                                                        <span className="font-medium text-sm">{CLAUDE_PERSONALITIES[typingPersonality].name}</span>
                                                        <span className="text-xs text-gray-500">typing...</span>
                                                    </div>
                                                    <div className="flex gap-1">
                                                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                                                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                                                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                                                    </div>
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Features Overview */}
                        <div className="mt-12 bg-white rounded-lg shadow-sm border p-6">
                            <h3 className="text-xl font-semibold mb-6">Key Features</h3>
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                                <div className="text-center">
                                    <div className="text-3xl mb-3">🎯</div>
                                    <h4 className="font-medium mb-2">Specialized Roles</h4>
                                    <p className="text-sm text-gray-600">Each Claude has a specific expertise and perspective</p>
                                </div>
                                <div className="text-center">
                                    <div className="text-3xl mb-3">🤝</div>
                                    <h4 className="font-medium mb-2">Collaborative Problem-Solving</h4>
                                    <p className="text-sm text-gray-600">Multiple AI perspectives working together</p>
                                </div>
                                <div className="text-center">
                                    <div className="text-3xl mb-3">📊</div>
                                    <h4 className="font-medium mb-2">Comprehensive Analysis</h4>
                                    <p className="text-sm text-gray-600">Architecture, security, performance, and education</p>
                                </div>
                                <div className="text-center">
                                    <div className="text-3xl mb-3">⚡</div>
                                    <h4 className="font-medium mb-2">Efficient Workflow</h4>
                                    <p className="text-sm text-gray-600">Structured approach to complex problems</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            );
        }

        ReactDOM.render(<EnsembleDemo />, document.getElementById('root'));
    </script>
</body>
</html>