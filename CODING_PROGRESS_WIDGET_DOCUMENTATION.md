# Coding Progress Widget Documentation

## Overview

The Coding Progress Widget is a comprehensive developer analytics tool that provides insights into coding activity, productivity, and project statistics. It's designed to motivate developers and help them track their coding journey.

## Features

### 📊 Core Metrics Tracking
- **Session Statistics**: Active sessions, total session time, sessions per day/week
- **Project Activity**: Total projects, active projects, recent activity  
- **Code Statistics**: Lines of code (estimated), files modified, language usage
- **Productivity Metrics**: Average session duration, peak coding hours, weekly goals
- **Streak Tracking**: Consecutive coding days, longest streak

### 📈 Advanced Analytics
- **Time-based Analysis**: Daily, weekly, monthly, yearly views
- **Language Breakdown**: File extensions analysis with visual pie charts
- **Activity Charts**: Progress charts, activity heatmaps, productivity trends
- **Peak Hours Analysis**: Identifies most productive coding times
- **Efficiency Metrics**: Session duration patterns and productivity insights

### 🎯 Goal Setting & Motivation
- **Customizable Goals**: Daily, weekly, monthly coding time targets
- **Progress Tracking**: Visual progress bars and percentage completion
- **Achievement System**: Unlockable badges for various milestones
- **Streak Motivation**: Visual streak counters with personal bests

### 💎 Claude Integration
- **Usage Statistics**: Total cost, tokens used, sessions with Claude
- **Model Breakdown**: Usage patterns across different Claude models
- **Cost Analysis**: Average cost per session and total spending

## Technical Architecture

### Data Sources
The widget integrates with multiple data sources:
- **Claude API**: Projects and sessions from `api.listProjects()` and `api.getProjectSessions()`
- **Usage Statistics**: Claude usage data from `api.getUsageStats()`
- **File System Analysis**: Code file scanning and language detection
- **Local Storage**: Goals and preferences storage

### Components Structure

#### 1. `useCodingProgress` Hook (`src/hooks/useCodingProgress.ts`)
Custom React hook that handles:
- Data fetching and aggregation
- Caching and real-time updates
- Goal management
- Achievement calculation
- Error handling and retry logic

#### 2. `CodingProgressWidget` Component (`src/components/hub/widgets/CodingProgressWidget.tsx`)
Main widget component featuring:
- **Overview Tab**: Key metrics and today's progress
- **Activity Tab**: Charts and productivity analytics
- **Languages Tab**: Language breakdown with pie charts
- **Goals Tab**: Goal setting and progress tracking

#### 3. Sub-components
- **MetricCard**: Displays individual metrics with trends
- **AchievementBadge**: Shows earned achievements
- **LanguageBreakdown**: Pie chart and language statistics
- **ActivityChart**: Time-based activity visualization
- **GoalsSection**: Goal management interface

### Data Processing

#### Language Detection
Files are analyzed by extension using `LANGUAGE_MAP`:
```typescript
const LANGUAGE_MAP: Record<string, { name: string; color: string }> = {
  '.js': { name: 'JavaScript', color: '#f7df1e' },
  '.tsx': { name: 'React TypeScript', color: '#61dafb' },
  '.py': { name: 'Python', color: '#3776ab' },
  // ... 20+ languages supported
};
```

#### Lines of Code Estimation
Smart estimation based on file size and type:
- Average 50 characters per line
- Language-specific multipliers (JSON: 0.7x, TypeScript: 1.2x)
- Excludes documentation and data files appropriately

#### Achievement System
Dynamic achievement unlocking based on:
- Session milestones (10, 100, 500 sessions)
- Streak achievements (7, 30, 100 days)
- Language diversity (polyglot programmer)
- Consistency patterns (daily coder, weekend warrior)

## Usage

### Adding the Widget
```typescript
// The widget is automatically available in the hub
// Users can add it through the widget gallery
const widget = createDefaultWidget('coding-progress');
```

### Customization Options
```typescript
interface CodingProgressWidgetSettings {
  timeRange: 'today' | 'week' | 'month' | 'year';
  showGoals: boolean;
  showLanguageBreakdown: boolean;
  showActivityChart: boolean;
}
```

### Goal Management
Users can set custom goals via the Goals tab:
- Daily goal: Default 120 minutes (2 hours)
- Weekly goal: Default 840 minutes (14 hours) 
- Monthly goal: Default 3600 minutes (60 hours)

## Widget Views

### 1. Overview Tab
- **Quick Stats Grid**: Sessions, coding time, projects, languages
- **Today's Progress**: Daily goal progress with visual bar
- **Current Streak**: Streak counter with personal best
- **Recent Achievements**: Latest unlocked badges

### 2. Activity Tab  
- **Activity Timeline**: Area chart showing sessions and duration over time
- **Peak Productivity Hours**: Grid showing most productive times of day
- **Weekly Trends**: Historical productivity patterns

### 3. Languages Tab
- **Language Pie Chart**: Visual breakdown of code languages
- **Language List**: Detailed statistics with file counts and percentages
- **File Extensions**: Complete breakdown of file types

### 4. Goals Tab
- **Goal Cards**: Daily, weekly, monthly progress cards
- **Progress Bars**: Visual progress indicators
- **Goal Editing**: Inline goal modification (future enhancement)

## Performance Optimizations

### Caching Strategy
- **15-minute cache**: Balances real-time feel with performance
- **Incremental loading**: Large datasets loaded progressively
- **Memoized calculations**: Heavy computations cached with React.useMemo

### Data Efficiency
- **Lazy loading**: Charts rendered only when tabs are active
- **Virtual scrolling**: For large file lists (future enhancement)
- **Background refresh**: Data updates without blocking UI

## Error Handling

### Graceful Degradation
- **Fallback data**: Shows placeholder data when API fails
- **Error boundaries**: Prevents widget crashes from affecting dashboard
- **Retry mechanisms**: Automatic retry with exponential backoff

### User Feedback
- **Loading states**: Skeleton screens and progress indicators
- **Error messages**: Clear, actionable error descriptions
- **Retry buttons**: Manual retry options for failed operations

## Future Enhancements

### Phase 2 Planned Features
- **Git Integration**: Real commit tracking and diff analysis
- **IDE Integration**: Time tracking from actual coding sessions
- **Team Collaboration**: Shared goals and team statistics
- **Export Reports**: PDF/CSV progress reports

### Phase 3 Advanced Features
- **AI Insights**: Claude-powered productivity recommendations
- **Habit Tracking**: Coding habit formation and analysis
- **Goal Suggestions**: AI-suggested goals based on patterns
- **Social Features**: Coding challenges and leaderboards

## Configuration

### Environment Variables
```bash
# Optional: Custom goal defaults
CODING_DAILY_GOAL=120     # minutes
CODING_WEEKLY_GOAL=840    # minutes  
CODING_MONTHLY_GOAL=3600  # minutes
```

### LocalStorage Keys
- `coding-daily-goal`: User's daily goal in minutes
- `coding-weekly-goal`: User's weekly goal in minutes
- `coding-monthly-goal`: User's monthly goal in minutes

## Dependencies

### Required Packages
- `recharts`: Chart visualization library
- `framer-motion`: Smooth animations and transitions
- `lucide-react`: Icon library for UI elements

### Internal Dependencies
- `@/lib/api`: Claude API integration
- `@/components/ui/*`: Shared UI components
- `@/types/hub`: Widget type definitions

## Development

### Testing the Widget
1. Add the widget to your hub dashboard
2. Ensure you have existing projects and sessions
3. Check all four tabs render correctly
4. Verify charts display appropriate data
5. Test goal setting functionality

### Debugging
```typescript
// Enable debug logging in the hook
const { data, loading, error } = useCodingProgress({
  timeRange: 'week',
  autoRefresh: true,
  refreshInterval: 15
});

console.log('Coding Progress Data:', data);
```

### Performance Monitoring
- Monitor API call frequency (should respect 15-minute cache)
- Check component re-render counts with React DevTools
- Verify memory usage doesn't grow over time

## Accessibility

### Keyboard Navigation
- Full tab navigation support
- Arrow key navigation in charts
- Enter/Space for interactive elements

### Screen Reader Support
- Semantic HTML structure
- ARIA labels for charts and metrics
- Alt text for achievement icons

### Visual Accessibility
- High contrast color schemes
- Scalable text and icons
- Colorblind-friendly chart colors

## Contributing

### Adding New Metrics
1. Extend `CodingProgressData` interface
2. Add calculation logic in `useCodingProgress`
3. Create UI component for metric display
4. Update widget tabs as needed

### Adding New Achievements
1. Define achievement in `generateAchievements` function
2. Add achievement logic and conditions
3. Create appropriate icon and description
4. Test achievement unlocking

---

*This widget represents a comprehensive solution for developer productivity tracking, combining real-time data analysis with motivational features to enhance the coding experience.*