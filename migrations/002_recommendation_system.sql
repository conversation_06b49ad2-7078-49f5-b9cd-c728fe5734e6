-- Recommendation System Database Extensions for Claudia
-- Builds on existing marketplace schema to enable intelligent feature recommendations

-- 1. User Interaction Tracking
CREATE TABLE IF NOT EXISTS user_interactions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    
    -- Session and user context
    session_id TEXT, -- Claude session ID if available
    user_session_hash TEXT NOT NULL, -- Anonymous session identifier
    project_id TEXT, -- From existing Project interface
    
    -- Interaction details
    interaction_type TEXT NOT NULL, -- 'agent_execute', 'marketplace_browse', 'feature_use', 'agent_install', 'tab_create', 'tool_use'
    feature_id TEXT NOT NULL, -- agent_id, feature_name, tool_name, etc.
    feature_category TEXT, -- 'agent', 'ui_feature', 'marketplace', 'session_management'
    
    -- Context data (JSON)
    context_data TEXT, -- JSON: {project_type, file_extensions, model_used, task_type, etc}
    
    -- Outcome metrics
    success_indicator BOOLEAN DEFAULT 1, -- Whether interaction was successful
    duration_seconds REAL, -- How long interaction took
    token_usage INTEGER DEFAULT 0, -- Tokens consumed in interaction
    user_rating INTEGER, -- Optional explicit feedback (1-5)
    
    -- Metadata
    timestamp TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Indexes for efficient querying
    INDEX idx_interactions_session (user_session_hash, timestamp DESC),
    INDEX idx_interactions_type (interaction_type, timestamp DESC),
    INDEX idx_interactions_feature (feature_id, success_indicator),
    INDEX idx_interactions_project (project_id, interaction_type)
);

-- 2. User Behavior Patterns
CREATE TABLE IF NOT EXISTS user_behavior_patterns (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    
    -- User identification
    user_profile_hash TEXT NOT NULL, -- Derived from usage patterns, not personal data
    
    -- Pattern analysis
    pattern_type TEXT NOT NULL, -- 'workflow_sequence', 'feature_combination', 'time_based', 'project_based'
    pattern_data TEXT NOT NULL, -- JSON: feature sequences, combinations, usage times
    
    -- Pattern metrics
    frequency_score REAL DEFAULT 0.0, -- How often this pattern occurs
    success_score REAL DEFAULT 0.0, -- How successful this pattern is
    efficiency_score REAL DEFAULT 0.0, -- How efficient this pattern is
    confidence_level REAL DEFAULT 0.0, -- Statistical confidence in pattern
    
    -- Context
    project_context TEXT, -- JSON: file types, project characteristics
    time_context TEXT, -- JSON: time of day, day of week patterns
    
    -- Metadata
    first_observed TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_observed TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    observation_count INTEGER DEFAULT 1,
    
    -- Constraints and indexes
    UNIQUE(user_profile_hash, pattern_type, pattern_data),
    INDEX idx_patterns_user (user_profile_hash, success_score DESC),
    INDEX idx_patterns_type (pattern_type, frequency_score DESC),
    INDEX idx_patterns_confidence (confidence_level DESC, success_score DESC)
);

-- 3. Feature Usage Statistics
CREATE TABLE IF NOT EXISTS feature_usage_stats (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    
    -- Feature identification
    feature_id TEXT NOT NULL,
    feature_type TEXT NOT NULL, -- 'agent', 'ui_component', 'tool', 'command'
    feature_category TEXT, -- More specific categorization
    
    -- Aggregated statistics
    total_uses INTEGER DEFAULT 0,
    unique_users INTEGER DEFAULT 0,
    success_rate REAL DEFAULT 0.0,
    average_duration REAL DEFAULT 0.0,
    average_rating REAL DEFAULT 0.0,
    
    -- Usage context patterns
    common_contexts TEXT, -- JSON: most common usage contexts
    user_segments TEXT, -- JSON: types of users who use this feature
    
    -- Time-based metrics
    daily_usage INTEGER DEFAULT 0,
    weekly_usage INTEGER DEFAULT 0,
    monthly_usage INTEGER DEFAULT 0,
    peak_usage_times TEXT, -- JSON: time patterns
    
    -- Metadata
    last_updated TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints and indexes
    UNIQUE(feature_id, feature_type),
    INDEX idx_feature_stats_usage (total_uses DESC, success_rate DESC),
    INDEX idx_feature_stats_rating (average_rating DESC, total_uses DESC),
    INDEX idx_feature_stats_type (feature_type, feature_category)
);

-- 4. Recommendation Cache
CREATE TABLE IF NOT EXISTS recommendations (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    
    -- Target user
    user_profile_hash TEXT NOT NULL,
    
    -- Recommendation details
    recommendation_type TEXT NOT NULL, -- 'agent', 'feature', 'workflow', 'configuration'
    item_id TEXT NOT NULL, -- ID of recommended item
    item_category TEXT, -- Category of recommended item
    
    -- Scoring
    relevance_score REAL NOT NULL, -- How relevant this recommendation is (0-1)
    confidence_score REAL NOT NULL, -- How confident we are in this recommendation (0-1)
    priority_score REAL NOT NULL, -- Priority for showing this recommendation (0-1)
    
    -- Reasoning and context
    reasoning_data TEXT, -- JSON: why this was recommended
    context_triggers TEXT, -- JSON: what triggered this recommendation
    expected_benefit TEXT, -- JSON: expected user benefit
    
    -- Recommendation metadata
    algorithm_version TEXT DEFAULT 'v1.0',
    generated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    expires_at TEXT, -- When this recommendation expires
    
    -- User interaction with recommendation
    shown_to_user BOOLEAN DEFAULT 0,
    shown_at TEXT,
    user_clicked BOOLEAN DEFAULT 0,
    clicked_at TEXT,
    user_adopted BOOLEAN DEFAULT 0,
    adopted_at TEXT,
    user_dismissed BOOLEAN DEFAULT 0,
    dismissed_at TEXT,
    
    -- Feedback
    user_feedback_rating INTEGER, -- 1-5 rating if user provides feedback
    user_feedback_text TEXT,
    outcome_success BOOLEAN, -- Whether recommendation led to successful outcome
    
    -- Indexes
    INDEX idx_recommendations_user (user_profile_hash, priority_score DESC),
    INDEX idx_recommendations_type (recommendation_type, relevance_score DESC),
    INDEX idx_recommendations_active (expires_at, shown_to_user, user_dismissed),
    INDEX idx_recommendations_feedback (user_feedback_rating, outcome_success)
);

-- 5. Collaborative Filtering Data
CREATE TABLE IF NOT EXISTS user_similarity_matrix (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    
    -- User pairs
    user_a_hash TEXT NOT NULL,
    user_b_hash TEXT NOT NULL,
    
    -- Similarity scores
    behavior_similarity REAL DEFAULT 0.0, -- Similarity in behavior patterns
    preference_similarity REAL DEFAULT 0.0, -- Similarity in preferences
    project_similarity REAL DEFAULT 0.0, -- Similarity in project types
    overall_similarity REAL DEFAULT 0.0, -- Combined similarity score
    
    -- Metadata
    calculated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    data_points_count INTEGER DEFAULT 0, -- Number of data points used for calculation
    confidence_level REAL DEFAULT 0.0,
    
    -- Constraints and indexes
    UNIQUE(user_a_hash, user_b_hash),
    INDEX idx_similarity_score (overall_similarity DESC, confidence_level DESC),
    INDEX idx_similarity_user_a (user_a_hash, overall_similarity DESC),
    INDEX idx_similarity_user_b (user_b_hash, overall_similarity DESC)
);

-- 6. Feature Co-occurrence Matrix
CREATE TABLE IF NOT EXISTS feature_associations (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    
    -- Feature pair
    feature_a_id TEXT NOT NULL,
    feature_a_type TEXT NOT NULL,
    feature_b_id TEXT NOT NULL,
    feature_b_type TEXT NOT NULL,
    
    -- Association metrics
    co_occurrence_count INTEGER DEFAULT 0, -- How often used together
    support_score REAL DEFAULT 0.0, -- Support in association rule mining
    confidence_score REAL DEFAULT 0.0, -- Confidence A -> B
    lift_score REAL DEFAULT 0.0, -- Lift score for association
    
    -- Context
    common_contexts TEXT, -- JSON: contexts where this association occurs
    success_rate REAL DEFAULT 0.0, -- Success rate when used together
    
    -- Temporal patterns
    typical_sequence TEXT, -- 'sequential', 'simultaneous', 'either'
    average_time_gap REAL DEFAULT 0.0, -- Average time between usage
    
    -- Metadata
    last_updated TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints and indexes
    UNIQUE(feature_a_id, feature_a_type, feature_b_id, feature_b_type),
    INDEX idx_associations_feature_a (feature_a_id, confidence_score DESC),
    INDEX idx_associations_feature_b (feature_b_id, confidence_score DESC),
    INDEX idx_associations_strength (lift_score DESC, confidence_score DESC)
);

-- 7. A/B Testing Framework
CREATE TABLE IF NOT EXISTS recommendation_experiments (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    
    -- Experiment details
    experiment_name TEXT NOT NULL UNIQUE,
    experiment_type TEXT NOT NULL, -- 'algorithm_comparison', 'ui_variation', 'content_test'
    description TEXT,
    
    -- Configuration
    control_config TEXT, -- JSON: control group configuration
    treatment_configs TEXT, -- JSON: array of treatment configurations
    
    -- Targeting
    target_user_criteria TEXT, -- JSON: criteria for user inclusion
    sample_size_target INTEGER,
    
    -- Status and timing
    status TEXT DEFAULT 'draft', -- 'draft', 'active', 'paused', 'completed'
    start_date TEXT,
    end_date TEXT,
    
    -- Results tracking
    participants_count INTEGER DEFAULT 0,
    conversion_metrics TEXT, -- JSON: key metrics being tracked
    
    -- Metadata
    created_by TEXT,
    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_experiments_status (status, start_date),
    INDEX idx_experiments_active (status, end_date)
);

-- 8. Experiment Participations
CREATE TABLE IF NOT EXISTS experiment_participations (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    
    -- Experiment and user
    experiment_id INTEGER NOT NULL,
    user_profile_hash TEXT NOT NULL,
    
    -- Assignment
    treatment_group TEXT NOT NULL, -- 'control', 'treatment_a', 'treatment_b', etc.
    assigned_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Interaction tracking
    interactions_count INTEGER DEFAULT 0,
    conversions_count INTEGER DEFAULT 0,
    outcome_data TEXT, -- JSON: specific outcome metrics
    
    -- Status
    is_active BOOLEAN DEFAULT 1,
    completed_at TEXT,
    
    FOREIGN KEY (experiment_id) REFERENCES recommendation_experiments(id) ON DELETE CASCADE,
    UNIQUE(experiment_id, user_profile_hash),
    INDEX idx_participations_experiment (experiment_id, treatment_group),
    INDEX idx_participations_user (user_profile_hash, is_active)
);

-- 9. Real-time Feature Flags
CREATE TABLE IF NOT EXISTS recommendation_feature_flags (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    
    -- Flag details
    flag_name TEXT NOT NULL UNIQUE,
    flag_type TEXT DEFAULT 'boolean', -- 'boolean', 'string', 'number', 'json'
    description TEXT,
    
    -- Configuration
    default_value TEXT NOT NULL,
    user_overrides TEXT, -- JSON: user-specific overrides
    conditions TEXT, -- JSON: conditions for flag activation
    
    -- Rollout control
    rollout_percentage INTEGER DEFAULT 0, -- 0-100
    target_groups TEXT, -- JSON: specific user groups to target
    
    -- Status
    is_active BOOLEAN DEFAULT 1,
    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_flags_active (is_active, flag_name),
    INDEX idx_flags_rollout (rollout_percentage, is_active)
);

-- Triggers for maintaining data integrity and statistics

-- Update behavior patterns when new interaction is added
CREATE TRIGGER IF NOT EXISTS update_behavior_patterns_on_interaction
AFTER INSERT ON user_interactions
FOR EACH ROW
BEGIN
    -- Update or create behavior pattern
    INSERT OR REPLACE INTO user_behavior_patterns (
        user_profile_hash,
        pattern_type,
        pattern_data,
        frequency_score,
        success_score,
        last_observed,
        observation_count
    )
    SELECT 
        NEW.user_session_hash,
        'recent_activity',
        json_object('recent_interactions', json_array(NEW.feature_id)),
        COALESCE(frequency_score, 0) + 1,
        CASE WHEN NEW.success_indicator THEN 
            (COALESCE(success_score * observation_count, 0) + 1.0) / (COALESCE(observation_count, 0) + 1)
        ELSE 
            (COALESCE(success_score * observation_count, 0)) / (COALESCE(observation_count, 0) + 1)
        END,
        NEW.timestamp,
        COALESCE(observation_count, 0) + 1
    FROM user_behavior_patterns 
    WHERE user_profile_hash = NEW.user_session_hash 
      AND pattern_type = 'recent_activity'
    UNION ALL 
    SELECT 
        NEW.user_session_hash,
        'recent_activity',
        json_object('recent_interactions', json_array(NEW.feature_id)),
        1.0,
        CASE WHEN NEW.success_indicator THEN 1.0 ELSE 0.0 END,
        NEW.timestamp,
        1
    WHERE NOT EXISTS (
        SELECT 1 FROM user_behavior_patterns 
        WHERE user_profile_hash = NEW.user_session_hash 
          AND pattern_type = 'recent_activity'
    );
END;

-- Update feature usage statistics
CREATE TRIGGER IF NOT EXISTS update_feature_stats_on_interaction
AFTER INSERT ON user_interactions
FOR EACH ROW
BEGIN
    INSERT OR REPLACE INTO feature_usage_stats (
        feature_id,
        feature_type,
        feature_category,
        total_uses,
        success_rate,
        average_duration,
        last_updated
    )
    SELECT 
        NEW.feature_id,
        NEW.feature_category,
        NEW.feature_category,
        COALESCE(total_uses, 0) + 1,
        CASE WHEN total_uses > 0 THEN
            (COALESCE(success_rate * total_uses, 0) + CASE WHEN NEW.success_indicator THEN 1.0 ELSE 0.0 END) / (total_uses + 1)
        ELSE
            CASE WHEN NEW.success_indicator THEN 1.0 ELSE 0.0 END
        END,
        CASE WHEN total_uses > 0 AND NEW.duration_seconds IS NOT NULL THEN
            (COALESCE(average_duration * total_uses, 0) + NEW.duration_seconds) / (total_uses + 1)
        ELSE
            NEW.duration_seconds
        END,
        NEW.timestamp
    FROM feature_usage_stats 
    WHERE feature_id = NEW.feature_id AND feature_type = NEW.feature_category
    UNION ALL
    SELECT 
        NEW.feature_id,
        NEW.feature_category,
        NEW.feature_category,
        1,
        CASE WHEN NEW.success_indicator THEN 1.0 ELSE 0.0 END,
        NEW.duration_seconds,
        NEW.timestamp
    WHERE NOT EXISTS (
        SELECT 1 FROM feature_usage_stats 
        WHERE feature_id = NEW.feature_id AND feature_type = NEW.feature_category
    );
END;

-- Update recommendation feedback tracking
CREATE TRIGGER IF NOT EXISTS update_recommendation_feedback
AFTER UPDATE OF user_clicked, user_adopted, user_dismissed, user_feedback_rating ON recommendations
FOR EACH ROW
BEGIN
    -- Log the feedback event for analytics
    INSERT INTO user_interactions (
        user_session_hash,
        interaction_type,
        feature_id,
        feature_category,
        context_data,
        success_indicator,
        user_rating,
        timestamp
    )
    VALUES (
        NEW.user_profile_hash,
        'recommendation_feedback',
        NEW.item_id,
        NEW.recommendation_type,
        json_object(
            'recommendation_id', NEW.id,
            'action', CASE 
                WHEN NEW.user_clicked AND OLD.user_clicked = 0 THEN 'clicked'
                WHEN NEW.user_adopted AND OLD.user_adopted = 0 THEN 'adopted'
                WHEN NEW.user_dismissed AND OLD.user_dismissed = 0 THEN 'dismissed'
                ELSE 'rating_provided'
            END,
            'recommendation_score', NEW.relevance_score
        ),
        CASE 
            WHEN NEW.user_adopted THEN 1
            WHEN NEW.user_dismissed THEN 0
            ELSE NEW.user_clicked
        END,
        NEW.user_feedback_rating,
        CURRENT_TIMESTAMP
    );
END;

-- Insert some initial feature flags for recommendation system
INSERT OR IGNORE INTO recommendation_feature_flags (flag_name, flag_type, default_value, description, rollout_percentage, is_active) VALUES
('enable_agent_recommendations', 'boolean', 'true', 'Enable AI-powered agent recommendations', 100, 1),
('enable_workflow_suggestions', 'boolean', 'true', 'Enable workflow optimization suggestions', 50, 1),
('enable_collaborative_filtering', 'boolean', 'false', 'Enable collaborative filtering recommendations', 25, 1),
('recommendation_refresh_interval', 'number', '300', 'Seconds between recommendation refreshes', 100, 1),
('max_recommendations_per_session', 'number', '5', 'Maximum recommendations to show per session', 100, 1),
('enable_recommendation_explanations', 'boolean', 'true', 'Show explanations for why items are recommended', 75, 1),
('enable_feature_discovery_nudges', 'boolean', 'true', 'Show nudges for discovering new features', 80, 1);