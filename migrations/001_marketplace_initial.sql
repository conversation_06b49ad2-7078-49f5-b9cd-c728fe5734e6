-- Migration 001: Initial Marketplace Schema
-- Version: 1.0.0
-- Description: Adds marketplace functionality to existing Claudia database
-- Dependencies: Requires existing agents and agent_runs tables

-- Check if migration has already been applied
-- (This would be handled by a migration system, but included for reference)

-- Add marketplace-specific columns to existing agents table
-- (These allow existing agents to be "promoted" to marketplace status)
ALTER TABLE agents ADD COLUMN remote_id TEXT;
ALTER TABLE agents ADD COLUMN author TEXT;
ALTER TABLE agents ADD COLUMN author_url TEXT;
ALTER TABLE agents ADD COLUMN description TEXT;
ALTER TABLE agents ADD COLUMN long_description TEXT;
ALTER TABLE agents ADD COLUMN version TEXT DEFAULT '1.0.0';
ALTER TABLE agents ADD COLUMN license TEXT DEFAULT 'MIT';
ALTER TABLE agents ADD COLUMN tags TEXT; -- JSON array
ALTER TABLE agents ADD COLUMN category_id INTEGER;
ALTER TABLE agents ADD COLUMN download_count INTEGER DEFAULT 0;
ALTER TABLE agents ADD COLUMN rating_average REAL DEFAULT 0.0;
ALTER TABLE agents ADD COLUMN rating_count INTEGER DEFAULT 0;
ALTER TABLE agents ADD COLUMN source_url TEXT;
ALTER TABLE agents ADD COLUMN download_url TEXT;
ALTER TABLE agents ADD COLUMN documentation_url TEXT;
ALTER TABLE agents ADD COLUMN homepage_url TEXT;
ALTER TABLE agents ADD COLUMN sha TEXT;
ALTER TABLE agents ADD COLUMN file_size INTEGER DEFAULT 0;
ALTER TABLE agents ADD COLUMN is_installed BOOLEAN DEFAULT 1; -- Existing agents are considered installed
ALTER TABLE agents ADD COLUMN installed_at TEXT;
ALTER TABLE agents ADD COLUMN installation_source TEXT DEFAULT 'manual';
ALTER TABLE agents ADD COLUMN published_at TEXT;
ALTER TABLE agents ADD COLUMN last_synced_at TEXT;

-- Create indexes on new columns
CREATE INDEX IF NOT EXISTS idx_agents_remote_id ON agents(remote_id);
CREATE INDEX IF NOT EXISTS idx_agents_author ON agents(author);
CREATE INDEX IF NOT EXISTS idx_agents_category ON agents(category_id);
CREATE INDEX IF NOT EXISTS idx_agents_rating ON agents(rating_average DESC, rating_count DESC);
CREATE INDEX IF NOT EXISTS idx_agents_downloads ON agents(download_count DESC);
CREATE INDEX IF NOT EXISTS idx_agents_installed ON agents(is_installed, installed_at DESC);

-- Create the new marketplace-specific tables
-- (Content from marketplace-schema.sql, excluding the agents table modifications)

-- Agent Categories
CREATE TABLE IF NOT EXISTS agent_categories (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE,
    slug TEXT NOT NULL UNIQUE,
    description TEXT,
    icon TEXT,
    parent_id INTEGER,
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT 1,
    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (parent_id) REFERENCES agent_categories(id) ON DELETE CASCADE
);

CREATE INDEX IF NOT EXISTS idx_categories_parent ON agent_categories(parent_id);
CREATE INDEX IF NOT EXISTS idx_categories_sort ON agent_categories(sort_order, name);

-- Agent Ratings and Reviews
CREATE TABLE IF NOT EXISTS agent_ratings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    agent_id INTEGER NOT NULL, -- References agents.id directly
    user_identifier TEXT,
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    review_title TEXT,
    review_content TEXT,
    is_verified BOOLEAN DEFAULT 0,
    helpful_votes INTEGER DEFAULT 0,
    total_votes INTEGER DEFAULT 0,
    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE CASCADE,
    UNIQUE(agent_id, user_identifier)
);

CREATE INDEX IF NOT EXISTS idx_ratings_agent ON agent_ratings(agent_id, rating DESC);
CREATE INDEX IF NOT EXISTS idx_ratings_helpful ON agent_ratings(helpful_votes DESC);

-- User Preferences
CREATE TABLE IF NOT EXISTS user_preferences (
    key TEXT PRIMARY KEY,
    value TEXT NOT NULL,
    value_type TEXT DEFAULT 'string',
    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Marketplace Cache
CREATE TABLE IF NOT EXISTS marketplace_cache (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    cache_key TEXT NOT NULL UNIQUE,
    cache_type TEXT NOT NULL,
    data TEXT NOT NULL,
    expires_at TEXT NOT NULL,
    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS idx_cache_key ON marketplace_cache(cache_key);
CREATE INDEX IF NOT EXISTS idx_cache_type_expires ON marketplace_cache(cache_type, expires_at);

-- Agent Downloads
CREATE TABLE IF NOT EXISTS agent_downloads (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    agent_id INTEGER NOT NULL,
    user_identifier TEXT,
    download_source TEXT,
    download_url TEXT,
    success BOOLEAN DEFAULT 1,
    error_message TEXT,
    downloaded_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE CASCADE
);

CREATE INDEX IF NOT EXISTS idx_downloads_agent_date ON agent_downloads(agent_id, downloaded_at DESC);
CREATE INDEX IF NOT EXISTS idx_downloads_success ON agent_downloads(success, downloaded_at DESC);

-- Agent Dependencies
CREATE TABLE IF NOT EXISTS agent_dependencies (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    agent_id INTEGER NOT NULL,
    dependency_type TEXT NOT NULL,
    dependency_name TEXT NOT NULL,
    dependency_version TEXT,
    is_required BOOLEAN DEFAULT 1,
    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE CASCADE,
    UNIQUE(agent_id, dependency_type, dependency_name)
);

CREATE INDEX IF NOT EXISTS idx_dependencies_agent ON agent_dependencies(agent_id);

-- Agent Collections
CREATE TABLE IF NOT EXISTS agent_collections (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    description TEXT,
    curator TEXT,
    is_featured BOOLEAN DEFAULT 0,
    is_public BOOLEAN DEFAULT 1,
    sort_order INTEGER DEFAULT 0,
    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX IF NOT EXISTS idx_collections_featured ON agent_collections(is_featured, sort_order);
CREATE INDEX IF NOT EXISTS idx_collections_public ON agent_collections(is_public, updated_at DESC);

-- Agent Collection Items
CREATE TABLE IF NOT EXISTS agent_collection_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    collection_id INTEGER NOT NULL,
    agent_id INTEGER NOT NULL,
    sort_order INTEGER DEFAULT 0,
    added_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (collection_id) REFERENCES agent_collections(id) ON DELETE CASCADE,
    FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE CASCADE,
    UNIQUE(collection_id, agent_id)
);

CREATE INDEX IF NOT EXISTS idx_collection_items_collection ON agent_collection_items(collection_id, sort_order);
CREATE INDEX IF NOT EXISTS idx_collection_items_agent ON agent_collection_items(agent_id);

-- Create triggers for timestamp updates
CREATE TRIGGER IF NOT EXISTS update_agent_rating_timestamp 
AFTER UPDATE ON agent_ratings 
FOR EACH ROW
BEGIN
    UPDATE agent_ratings SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

CREATE TRIGGER IF NOT EXISTS update_user_preferences_timestamp 
AFTER UPDATE ON user_preferences 
FOR EACH ROW
BEGIN
    UPDATE user_preferences SET updated_at = CURRENT_TIMESTAMP WHERE key = NEW.key;
END;

CREATE TRIGGER IF NOT EXISTS update_agent_collection_timestamp 
AFTER UPDATE ON agent_collections 
FOR EACH ROW
BEGIN
    UPDATE agent_collections SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- Create triggers for rating statistics
CREATE TRIGGER IF NOT EXISTS update_agent_rating_stats_insert
AFTER INSERT ON agent_ratings
FOR EACH ROW
BEGIN
    UPDATE agents 
    SET rating_average = (
        SELECT AVG(CAST(rating AS REAL)) 
        FROM agent_ratings 
        WHERE agent_id = NEW.agent_id
    ),
    rating_count = (
        SELECT COUNT(*) 
        FROM agent_ratings 
        WHERE agent_id = NEW.agent_id
    )
    WHERE id = NEW.agent_id;
END;

CREATE TRIGGER IF NOT EXISTS update_agent_rating_stats_update
AFTER UPDATE ON agent_ratings
FOR EACH ROW
BEGIN
    UPDATE agents 
    SET rating_average = (
        SELECT AVG(CAST(rating AS REAL)) 
        FROM agent_ratings 
        WHERE agent_id = NEW.agent_id
    ),
    rating_count = (
        SELECT COUNT(*) 
        FROM agent_ratings 
        WHERE agent_id = NEW.agent_id
    )
    WHERE id = NEW.agent_id;
END;

CREATE TRIGGER IF NOT EXISTS update_agent_rating_stats_delete
AFTER DELETE ON agent_ratings
FOR EACH ROW
BEGIN
    UPDATE agents 
    SET rating_average = (
        SELECT COALESCE(AVG(CAST(rating AS REAL)), 0.0) 
        FROM agent_ratings 
        WHERE agent_id = OLD.agent_id
    ),
    rating_count = (
        SELECT COUNT(*) 
        FROM agent_ratings 
        WHERE agent_id = OLD.agent_id
    )
    WHERE id = OLD.agent_id;
END;

-- Insert default categories
INSERT OR IGNORE INTO agent_categories (name, slug, description, icon, sort_order) VALUES
('Development Tools', 'development-tools', 'Agents for code development, testing, and debugging', '🔧', 1),
('Code Review', 'code-review', 'Agents specialized in code analysis and review', '🔍', 2),
('Documentation', 'documentation', 'Agents for generating and maintaining documentation', '📚', 3),
('Testing', 'testing', 'Agents for automated testing and quality assurance', '🧪', 4),
('Deployment', 'deployment', 'Agents for CI/CD and deployment automation', '🚀', 5),
('Data Analysis', 'data-analysis', 'Agents for data processing and analysis', '📊', 6),
('Security', 'security', 'Agents for security scanning and vulnerability assessment', '🔒', 7),
('Performance', 'performance', 'Agents for performance optimization and monitoring', '⚡', 8),
('Content Creation', 'content-creation', 'Agents for generating content and creative work', '✍️', 9),
('Learning & Education', 'learning-education', 'Agents for educational content and tutorials', '🎓', 10),
('Utilities', 'utilities', 'General-purpose utility agents', '🛠️', 11),
('Experimental', 'experimental', 'Cutting-edge and experimental agents', '🔬', 12);

-- Insert default user preferences
INSERT OR IGNORE INTO user_preferences (key, value, value_type) VALUES
('marketplace_enabled', 'true', 'boolean'),
('auto_update_agents', 'false', 'boolean'),
('download_confirmations', 'true', 'boolean'),
('preferred_categories', '[]', 'json'),
('cache_expiry_hours', '24', 'number'),
('show_experimental', 'false', 'boolean'),
('default_sort_order', 'downloads', 'string'),
('anonymous_analytics', 'true', 'boolean'),
('github_token', '', 'string'),
('github_repo', 'getAsterisk/claudia', 'string'),
('github_branch', 'main', 'string');

-- Create a default "Featured Agents" collection
INSERT OR IGNORE INTO agent_collections (name, description, curator, is_featured, is_public, sort_order) VALUES
('Featured Agents', 'Curated collection of high-quality, popular agents', 'Claudia Team', 1, 1, 1),
('Getting Started', 'Essential agents for new users', 'Claudia Team', 1, 1, 2),
('Developer Toolkit', 'Must-have agents for software development', 'Claudia Team', 0, 1, 3),
('Content & Documentation', 'Agents for creating and maintaining content', 'Claudia Team', 0, 1, 4);

-- Record this migration
INSERT OR IGNORE INTO user_preferences (key, value, value_type) VALUES
('migration_001_applied', datetime('now'), 'string');