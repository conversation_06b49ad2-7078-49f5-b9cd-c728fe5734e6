# Database Migrations

This directory contains SQL migration scripts for <PERSON>'s marketplace functionality.

## Migration Files

- `001_marketplace_initial.sql` - Initial marketplace schema setup
- Additional migrations will be numbered sequentially (002_, 003_, etc.)

## Migration Strategy

The migrations are designed to extend the existing Claudia database schema without breaking existing functionality:

1. **Backward Compatibility**: Existing `agents` and `agent_runs` tables are preserved
2. **Gradual Enhancement**: New marketplace columns are added as optional fields
3. **Safe Defaults**: All new columns have sensible defaults
4. **Index Optimization**: New indexes are added to support marketplace queries

## Key Design Principles

### 1. Existing Agent Compatibility
The migration adds marketplace-specific columns to the existing `agents` table rather than creating a separate `marketplace_agents` table. This allows:
- Existing agents to be "promoted" to marketplace status
- Unified querying across local and marketplace agents  
- Gradual adoption of marketplace features

### 2. SQLite Best Practices
- Uses `TEXT` for dates (ISO 8601 format) following existing patterns
- Uses `INTEGER PRIMARY KEY AUTOINCREMENT` for all ID fields
- Proper foreign key constraints with cascading deletes
- Strategic indexes for performance

### 3. Data Integrity
- Triggers maintain rating statistics automatically
- Timestamp triggers update `updated_at` fields
- Check constraints ensure data validity (e.g., ratings 1-5)
- Unique constraints prevent duplicate data

## Schema Extensions

### Core Tables Added:
- `agent_categories` - Classification system for agents
- `agent_ratings` - User reviews and ratings
- `user_preferences` - Marketplace configuration
- `marketplace_cache` - Offline capability support
- `agent_downloads` - Download tracking and analytics
- `agent_dependencies` - Agent dependency management
- `agent_collections` - Curated agent lists
- `agent_collection_items` - Many-to-many relationship

### Extended Tables:
- `agents` - Added marketplace-specific columns

### Indexes Added:
Strategic indexes to support:
- Marketplace search and filtering
- Rating-based sorting
- Download statistics
- Category browsing
- Installation status tracking

## Running Migrations

Migrations should be applied by the Rust backend's database initialization code. The migration system should:

1. Track applied migrations in the `user_preferences` table
2. Apply migrations in sequential order
3. Skip already-applied migrations
4. Handle migration failures gracefully

Example Rust code structure:

```rust
pub fn apply_migrations(conn: &Connection) -> Result<(), Box<dyn Error>> {
    let applied = get_applied_migrations(conn)?;
    
    if !applied.contains("001_marketplace_initial") {
        apply_migration_001(conn)?;
        record_migration(conn, "001_marketplace_initial")?;
    }
    
    // Apply additional migrations as needed
    
    Ok(())
}
```

## Testing Migrations

Before applying migrations:

1. **Backup Database**: Always backup the existing database
2. **Test on Copy**: Apply migrations to a copy of production data
3. **Verify Data Integrity**: Ensure existing agents and runs are preserved
4. **Performance Testing**: Verify query performance with new indexes

## Rollback Strategy

While SQLite doesn't support `ALTER TABLE DROP COLUMN`, rollbacks can be achieved by:

1. Creating backup of pre-migration database
2. Selective data restoration if needed
3. Application-level feature flags to disable marketplace features

## Default Data

The migration includes default data insertion:

- **Categories**: 12 predefined agent categories with icons and descriptions
- **Preferences**: Default marketplace preferences for new installations  
- **Collections**: Featured collections for agent discovery

## Performance Considerations

The migration adds several indexes to maintain query performance:

- Marketplace agent searches by category, rating, downloads
- Agent filtering by installation status
- Cache expiration lookups
- Rating aggregation queries

Estimated storage overhead: ~20% increase for metadata and indexes.